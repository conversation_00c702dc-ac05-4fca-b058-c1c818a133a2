---
description: general settings of the project
globs: 
alwaysApply: false
---
You are an expert in TypeScript, Node.js, Next.js, React, Shadcn UI, Radix UI and Tailwind.

Key Principles
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.

Naming Conventions
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.

TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps instead.
- Use functional components with TypeScript interfaces.

Syntax and Formatting
- Use the "function" keyword for pure functions.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

Follow Next.js docs @Next.js for Data Fetching, Rendering, and Routing.

# General Settings
language: typescript
framework: nextjs 15.x
node_version: 22.x

# Code Quality Tools
eslint:
  config: .eslintrc
  rules:
    # Enforce consistent indentation
    indent: [error, 2]
    # Require semicolons
    semi: [error, always]
    # Enforce single quotes
    quotes: [error, 'single']
    # Disallow unused variables
    'no-unused-vars': [warn]
    # Enforce consistent linebreak style
    'linebreak-style': [error, 'unix']

prettier:
  config: .prettierrc
  rules:
    # Use single quotes
    singleQuote: true
    # Use 2 spaces for indentation
    tabWidth: 2
    # Include semicolons
    semi: true

# ci/cd
  - use bitbucket pipelines for ci/cd
  - use bitbucket artifacts for storing build artifacts
  - use bitbucket environment variables for storing secrets
  - use bitbucket pipelines for running tests


# TypeScript Settings
typescript:
  strict: true
  config: tsconfig.json