---
description: Best practices for Next.js applications and routing
globs: 
alwaysApply: false
---

You are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI and Tailwind.


# Your rule content

- You can @ files here
- You can use markdown but dont have to
name: nextjs-best-practices
description: Best practices for Next.js applications and routing
globs: **/*.{ts,tsx}

# Next.js Best Practices

## Naming conventions

 - Use PascalCase for directories
  ```
  // Good
  components/AuthWizard/index.tsx
  components/DataTable/index.tsx
  
  // Bad
  components/auth-wizard/login-form.tsx
  components/dataTable/table-header.tsx
  components/AuthWizard/LoginForm.tsx
  components/DataTable/TableHeader.tsx
  ```

## App Router and Routing
- Use the App Router for improved performance and simpler data fetching
- Implement proper error boundaries using error.tsx files
- Create loading.tsx files for Suspense boundaries
- Use route groups (folders) for logical organization
- Implement proper middleware for auth and redirects

## Performance Optimization
- Utilize Next.js's built-in Image component for optimized images
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
- Use Script component for third-party scripts
- Implement proper caching strategies
- Use generateMetadata for dynamic SEO
- Leverage streaming with Suspense boundaries
- Use dynamic loading for non-critical components
- Optimize Web Vitals (LCP, CLS, FID).
- Follow Next.js docs for Data Fetching, Rendering, and Routing

## Data Fetching
- Use Server Components for data fetching where possible
- Implement proper revalidation strategies
- Use React Query for client-side data management
- Handle loading and error states appropriately

## Rendering Patterns
- Use Server Components by default
- Only use 'use client' when necessary
- Implement proper static/dynamic rendering strategies
- Use generateStaticParams for static paths
- Leverage ISR for semi-static content

## Component Architecture
- Keep components small and focused
- Use layout.tsx for shared layouts
- Implement proper template.tsx files
- Use page.tsx for route endpoints
- Organize components by feature/domain
- Favor named exports for components.

## State Management
- Use Server Components to eliminate client state where possible
- Implement proper form handling with React Hook Form
- Use URL state for shareable UI state
- Leverage React Context for global state
- Use localStorage/sessionStorage appropriately

## Security
- Implement proper CORS policies
- Use proper environment variables
- Implement proper authentication/authorization
- Sanitize user inputs
- Use proper Content Security Policies