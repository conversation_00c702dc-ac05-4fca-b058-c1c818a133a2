---
description:  Best practices for using React Hook Form
globs: 
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
name: react-hook-form-best-practices
description: Best practices for using React Hook Form
globs: **/*.{ts,tsx,js,jsx}

# React Hook Form Best Practices

## Form Setup
- Use proper useForm initialization
- Implement proper form validation
- Use proper default values
- Handle proper form submission
- Implement proper error handling

## Field Registration
- Use proper register function
- Implement proper field validation
- Use proper field arrays
- Handle proper field dependencies
- Follow proper naming conventions

## Validation
- Use proper validation schemas
- Implement proper custom validation
- Use proper error messages
- Handle proper async validation
- Implement proper cross-field validation

## Performance
- Use proper form optimization
- Implement proper re-rendering control
- Use proper field isolation
- Handle proper form state updates
- Leverage proper watch API

## Integration
- Use proper TypeScript integration
- Implement proper Zod integration
- Use proper UI component integration
- Handle proper controlled inputs
- Follow proper testing practices

## Error Handling
- Use proper error display
- Implement proper error mapping
- Use proper error states
- Handle proper validation errors
- Follow proper error patterns

## State Management
- Use proper form state
- Implement proper form reset
- Use proper form values
- Handle proper dirty states
- Follow proper state patterns

## Advanced Features
- Use proper form context
- Implement proper nested forms
- Use proper dynamic forms
- Handle proper conditional fields
- Follow proper advanced patterns