---
description: Best practices for React development
globs: 
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
name: react-best-practices
description: Best practices for React development
globs: **/*.{ts,tsx,js,jsx}

# React Best Practices

## Component Architecture
- Use functional components with hooks instead of class components
- Keep components small and focused on a single responsibility
- Implement proper component composition
- Use proper prop drilling alternatives (Context, composition)
- Extract reusable logic into custom hooks

## State Management
- Use useState for local component state
- Implement useReducer for complex state logic
- Use Context API for global state when appropriate
- Avoid unnecessary state updates
- Keep state as close as possible to where it's used

## Performance Optimization
- Use React.memo for expensive computations
- Implement useMemo for complex calculations
- Use useCallback for function memoization
- Avoid inline function definitions in JSX
- Implement proper lazy loading with Suspense

## Event Handling
- Use proper event delegation
- Implement debouncing/throttling where needed
- Handle async operations properly
- Use proper cleanup in useEffect
- Implement proper error boundaries

## Props and TypeScript
- Use proper prop types with TypeScript
- Implement proper interface definitions
- Use proper generic types where needed
- Avoid any type unless absolutely necessary
- Use proper discriminated unions

## Code Organization
- Group related components together
- Implement proper file/folder structure
- Use proper naming conventions
- Keep logic separate from presentation
- Use proper imports/exports

## Testing
- Write unit tests for components
- Implement integration tests
- Use proper testing utilities
- Test error scenarios
- Implement proper mocking strategies

## Accessibility
- Use proper ARIA attributes
- Implement keyboard navigation
- Use proper semantic HTML
- Test with screen readers
- Follow WCAG guidelines