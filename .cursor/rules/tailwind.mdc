---
description: Best practices for using Tailwind CSS
globs: 
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
name: tailwind-best-practices
description: Best practices for using Tailwind CSS
globs: **/*.{ts,tsx,js,jsx,css}

# Tailwind CSS Best Practices

## Utility Classes
- Use utility classes instead of custom CSS
- Implement proper responsive design patterns
- Use proper spacing utilities
- Leverage flexbox and grid utilities
- Use proper color utilities

## Component Design
- Create reusable component patterns
- Use proper component variants
- Implement proper hover/focus states
- Use proper transition utilities
- Leverage animation utilities

## Responsive Design
- Use mobile-first approach
- Implement proper breakpoint usage
- Use proper container queries
- Implement proper responsive spacing
- Use proper responsive typography

## Dark Mode
- Implement proper dark mode support
- Use proper color schemes
- Handle dark mode transitions
- Use proper dark mode variants
- Test dark mode implementations

## Performance
- Use proper purge configuration
- Implement proper class extraction
- Use proper JIT compilation
- Minimize custom CSS usage
- Optimize for production

## Organization
- Use proper class ordering
- Implement proper component extraction
- Use proper naming conventions
- Keep styles maintainable
- Use proper CSS architecture

## Accessibility
- Use proper contrast ratios
- Implement proper focus states
- Use proper semantic markup
- Handle proper color blindness support
- Follow WCAG guidelines

## Best Practices
- Use proper prefix configuration
- Implement proper plugin usage
- Use proper theme configuration
- Handle proper browser support
- Follow proper upgrade procedures