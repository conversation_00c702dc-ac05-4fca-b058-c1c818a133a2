---
description: TypeScript coding standards and type safety guidelines
globs:
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
name: typescript-best-practices
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}

# TypeScript Best Practices

## General

- Use TypeScript for all code; prefer types over interfaces.
- Use functional components with TypeScript types.

## Type Safety
- Enable strict mode in tsconfig.json
- Avoid using 'any' type
- Use proper type annotations
- Implement proper type guards
- Use proper type assertions only when necessary

## Type Definitions
- Use types for object shapes
- Use type aliases for unions and complex types
- Implement proper generic constraints
- Use proper utility types
- Define proper discriminated unions

## Advanced Types
- Use mapped types for object transformations
- Implement conditional types when needed
- Use template literal types for string manipulation
- Leverage intersection types appropriately
- Use proper index signatures

## Error Handling
- Use proper error types
- Implement proper error handling patterns
- Use Result types for operations that can fail
- Implement proper null checking
- Use proper undefined handling

## Code Organization
- Use proper module organization
- Implement proper barrel exports
- Use proper import/export patterns
- Keep type definitions close to usage
- Separate types into dedicated files when appropriate

## Best Practices
- Use readonly where appropriate
- Implement proper immutability patterns
- Avoid enums; use maps instead
- Leverage const assertions
- Use proper type inference

## Generics
- Use proper generic constraints
- Implement proper generic defaults
- Use proper generic naming conventions
- Leverage generic type inference
- Use proper generic type guards

## Configuration
- Use proper tsconfig settings
- Implement proper path aliases
- Use proper module resolution
- Configure proper lib settings
- Use proper compiler options
