# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# linter
.eslintcache
axe-linter.yml
.hintrc

# idea
**/.idea/workspace.xml
**/.idea/tasks.xml
**/.idea/shelf
**/.idea/*

# codegen
.schema.graphql
/src/common/apis/contentful/generated/

# robots.txt and sitemap
/public/robots.txt
/public/sitemap*.xml

# Storybook
storybook-static

certificates

biome.json
pnpm-lock.yaml

# VSCode IDE
.vscode
/.env

# DG images
/public/images/dg/

# e2e tests
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Claude
/.claude/
