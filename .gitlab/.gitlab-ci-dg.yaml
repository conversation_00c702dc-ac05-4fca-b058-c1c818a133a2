include:
  - project: 'software/online/ci-templates'
    ref: 'release/1.2'
    file:
      - '/applications/docker/build.yml'

variables:
  DOCKER_IMAGE_BASE_NAME: europe-west3-docker.pkg.dev/dgcp-prod-images-0/dg-website/dg-website
  DOCKER_CI_REGISTRY: europe-west3-docker.pkg.dev
  VERSION: "0.1"
  CONTENTFUL_ENVIRONMENT: master

stages:
  - docker
  - deploy

# Base Templates
.container-image-base:
  extends: .docker-build:amd64
  variables:
    DOCKERFILE_NAME: Dockerfile.dg-gcp
    BUILD_ARGS: CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME CI_COMMIT_SHA=$CI_COMMIT_SHA CI_PIPELINE_IID=$CI_PIPELINE_IID CI_PIPELINE_CREATED_AT=$CI_PIPELINE_CREATED_AT

.cp-website-deploy-base:
  stage: deploy
  image:
    name: gcr.io/google.com/cloudsdktool/google-cloud-cli:latest
  variables:
    CLOUDSDK_CONFIG: $CI_PROJECT_DIR/gcloud
  script:
    - gcloud auth activate-service-account $GCP_AUTH_NAME --key-file $GOOGLE_APPLICATION_CREDENTIALS
    - gcloud beta run services update $GCP_SERVICE --project=$GCP_PROJECT --region=$GCP_REGION --container dg-website --update-env-vars "REDIS_CACHE_ENABLED=$REDIS_CACHE_ENABLED" --update-env-vars "REDIS_CACHE_TEST_ROUTE_ENABLED=$REDIS_CACHE_TEST_ROUTE_ENABLED" --image "$DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    - gcloud run services update-traffic $GCP_SERVICE --to-latest --region=$GCP_REGION --project=$GCP_PROJECT
    - .gitlab/scripts/gcp-cleanup-services.sh
  tags:
    - docker



# ACC Environment
container-image:acc:
  extends: .container-image-base
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      variables:
        CONTENTFUL_ENVIRONMENT: master
    - if: $CI_COMMIT_REF_NAME == "feature/czimnick-patch-2"
      variables:
        CONTENTFUL_ENVIRONMENT: master
  variables:
    DOCKER_IMAGE_NAME: "${DOCKER_IMAGE_BASE_NAME}-acc"
    REDIS_CACHE_ENABLED: 'true'
    REDIS_CACHE_TEST_ROUTE_ENABLED: 'true'
  before_script:
    - touch .env.local
    - echo "CONTENTFUL_SPACE_ID=$CONTENTFUL_SPACE_ID" >> .env.local
    - echo "CONTENTFUL_DELIVERY_TOKEN=$CONTENTFUL_DELIVERY_TOKEN" >> .env.local
    - echo "CONTENTFUL_PREVIEW_TOKEN=$CONTENTFUL_PREVIEW_TOKEN" >> .env.local
    - echo "CONTENTFUL_ENVIRONMENT=$CONTENTFUL_ENVIRONMENT" >> .env.local
    - echo "CONTENTFUL_PREVIEW_SECRET=$CONTENTFUL_PREVIEW_SECRET" >> .env.local
    - echo "CONTENTFUL_REVALIDATE_SECRET=$CONTENTFUL_REVALIDATE_SECRET" >> .env.local
    - echo "DG_GTM_SRC=$DG_GTM_SRC" >> .env.local
    - echo "DG_HOST_OL=$ACC_DG_HOST_OL" >> .env.local
    - echo "DG_HOST_AC=$ACC_DG_HOST_AC" >> .env.local
    - echo "DG_HOST_CMS=$ACC_DG_HOST_CMS" >> .env.local
    - echo "DG_USER_OL=$ACC_DG_USER_OL" >> .env.local
    - echo "DG_PASSWORD_OL=$ACC_DG_PASSWORD_OL" >> .env.local
    - echo "DG_USER_AC=$ACC_DG_USER_AC" >> .env.local
    - echo "DG_PASSWORD_AC=$ACC_DG_PASSWORD_AC" >> .env.local
    - echo "DG_MOCK=false" >> .env.local
    - echo "NEXT_PUBLIC_SITE_URL=$ACC_NEXT_PUBLIC_SITE_URL" >> .env.local
    - echo "NEXT_PUBLIC_SHOW_VERSION=true" >> .env.local
    - echo "NEXT_PUBLIC_RECAPTCHA_SITE_KEY=$NEXT_PUBLIC_RECAPTCHA_SITE_KEY" >> .env.local
    - echo "RECAPTCHA_SECRET_KEY=$RECAPTCHA_SECRET_KEY" >> .env.local
    - echo "DG_USE_DIRECTLY_SERVER_CONNECTION=true" >> .env.local
    - echo "DG_X_API_PERMISSIONS=$DG_X_API_PERMISSIONS" >> .env.local
    - echo "SMTP_HOST=$SMTP_HOST" >> .env.local
    - echo "SMTP_USER=$SMTP_USER" >> .env.local
    - echo "SMTP_PORT=$SMTP_PORT" >> .env.local
    - echo "SMTP_SECURE=$SMTP_SECURE" >> .env.local
    - echo "SMTP_EMAIL=$SMTP_EMAIL" >> .env.local
    - echo "REDIS_CACHE_ENABLED=$REDIS_CACHE_ENABLED" >> .env.local
    - echo "REDIS_CACHE_TEST_ROUTE_ENABLED=$REDIS_CACHE_TEST_ROUTE_ENABLED" >> .env.local
    - echo "REDIS_URL=$REDIS_URL" >> .env.local
    - echo "REDIS_AUTH=$REDIS_AUTH" >> .env.local
    - echo "NEXT_PUBLIC_ALGOLIA_INDEX_NAME=DG-Website_Stage" >> .env.local
    - echo "NEXT_PUBLIC_TRUSTPILOT_BUSINESSUNIT_ID=$NEXT_PUBLIC_TRUSTPILOT_BUSINESSUNIT_ID" >> .env.local
    - cat .env.local

gcp-website-deploy:acc:
  extends: .cp-website-deploy-base
  variables:
    REDIS_CACHE_ENABLED: 'true'
    REDIS_CACHE_TEST_ROUTE_ENABLED: 'true'
    DOCKER_IMAGE_NAME: "${DOCKER_IMAGE_BASE_NAME}-acc"
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      variables:
        CONTENTFUL_ENVIRONMENT: master
    - if: $CI_COMMIT_REF_NAME == "feature/czimnick-patch-2"
      variables:
        CONTENTFUL_ENVIRONMENT: master
  needs:
    - job: container-image:acc
      artifacts: true
  environment:
    name: $CI_COMMIT_REF_NAME
    url: https://acc2.deutsche-glasfaser.de/api/draft?secret=secret



# PROD Environment
container-image:prod:
  extends: .container-image-base
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"
      when: manual
      variables:
        CONTENTFUL_ENVIRONMENT: master
  variables:
    DOCKER_IMAGE_NAME: "${DOCKER_IMAGE_BASE_NAME}-prod"
  before_script:
    - touch .env.local
    - echo "CONTENTFUL_SPACE_ID=$CONTENTFUL_SPACE_ID" >> .env.local
    - echo "CONTENTFUL_DELIVERY_TOKEN=$CONTENTFUL_DELIVERY_TOKEN" >> .env.local
    - echo "CONTENTFUL_PREVIEW_TOKEN=$CONTENTFUL_PREVIEW_TOKEN" >> .env.local
    - echo "CONTENTFUL_ENVIRONMENT=$CONTENTFUL_ENVIRONMENT" >> .env.local
    - echo "CONTENTFUL_PREVIEW_SECRET=$CONTENTFUL_PREVIEW_SECRET" >> .env.local
    - echo "CONTENTFUL_REVALIDATE_SECRET=$CONTENTFUL_REVALIDATE_SECRET" >> .env.local
    - echo "DG_GTM_SRC=$DG_GTM_SRC" >> .env.local
    - echo "DG_HOST_OL=$PROD_DG_HOST_OL" >> .env.local
    - echo "DG_HOST_AC=$PROD_DG_HOST_AC" >> .env.local
    - echo "DG_HOST_CMS=$PROD_DG_HOST_CMS" >> .env.local
    - echo "DG_USER_OL=$PROD_DG_USER_OL" >> .env.local
    - echo "DG_PASSWORD_OL=$PROD_DG_PASSWORD_OL" >> .env.local
    - echo "DG_USER_AC=$PROD_DG_USER_AC" >> .env.local
    - echo "DG_PASSWORD_AC=$PROD_DG_PASSWORD_AC" >> .env.local
    - echo "DG_MOCK=false" >> .env.local
    - echo "NEXT_PUBLIC_SITE_URL=$PROD_NEXT_PUBLIC_SITE_URL" >> .env.local
    - echo "NEXT_PUBLIC_SHOW_VERSION=$NEXT_PUBLIC_SHOW_VERSION" >> .env.local
    - echo "NEXT_PUBLIC_RECAPTCHA_SITE_KEY=$NEXT_PUBLIC_RECAPTCHA_SITE_KEY" >> .env.local
    - echo "RECAPTCHA_SECRET_KEY=$RECAPTCHA_SECRET_KEY" >> .env.local
    - echo "DG_USE_DIRECTLY_SERVER_CONNECTION=true" >> .env.local
    - echo "DG_X_API_PERMISSIONS=$DG_X_API_PERMISSIONS" >> .env.local
    - echo "SMTP_HOST=$SMTP_HOST" >> .env.local
    - echo "SMTP_USER=$SMTP_USER" >> .env.local
    - echo "SMTP_PORT=$SMTP_PORT" >> .env.local
    - echo "SMTP_SECURE=$SMTP_SECURE" >> .env.local
    - echo "SMTP_EMAIL=$SMTP_EMAIL" >> .env.local
    - echo "NEXT_PUBLIC_ALGOLIA_INDEX_NAME=DG-Website" >> .env.local
    - echo "NEXT_PUBLIC_TRUSTPILOT_BUSINESSUNIT_ID=$NEXT_PUBLIC_TRUSTPILOT_BUSINESSUNIT_ID" >> .env.local
    - cat .env.local

gcp-website-deploy:prod:
  extends: .cp-website-deploy-base
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"
      when: manual
      variables:
        CONTENTFUL_ENVIRONMENT: master
  variables:
    REDIS_CACHE_ENABLED: 'false'
    REDIS_CACHE_TEST_ROUTE_ENABLED: 'false'
    DOCKER_IMAGE_NAME: "${DOCKER_IMAGE_BASE_NAME}-prod"
  needs:
      - job: container-image:prod
        artifacts: true
  environment:
    name: production
    url: https://www.deutsche-glasfaser.de/
