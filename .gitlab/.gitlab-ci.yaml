# Defaults
default:
  image: node:22.11.0-slim
  cache:
    key: ${CI_COMMIT_REF_SLUG}-${CI_NODE_IMAGE}
    paths:
      - .npm/
    policy: pull-push
    when: on_success
    untracked: false

# Stages
stages:
  - build
  - test

variables:
  npm_config_cache: .npm

# Build
build_app:
  stage: build
  before_script:
    - apt update && apt install -y git
    - npm config set cache .npm
  script:
    # Use custom contentful env if it is set in the commit message with "env: nameOfEnvironment"
    - chmod +x .gitlab/scripts/use-custom-contentful-env.sh
    - source .gitlab/scripts/use-custom-contentful-env.sh
    - npm ci --prefer-offline
    - npm run build
  artifacts:
    paths:
      - node_modules/
      - dist/
    expire_in: 1 hour
  tags:
    - spot-1

# Tests
unit_tests:
  stage: test
  needs:
    - build_app
  before_script:
    - apt update && apt install -y git
  script:
    # Use custom contentful env if it is set in the commit message with "env: nameOfEnvironment"
    - source .gitlab/scripts/use-custom-contentful-env.sh
    - npm run codegen
    - npm run test

# TODO: Enable after having a working CI pipeline
#end_to_end_tests:
#  stage: test
#  needs:
#    - build_app
#  image: mcr.microsoft.com/playwright:v1.50.0-jammy
#  variables:
#    CI: 'true'
#  rules:
#    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"'
#  script:
#    - npx playwright test
#  artifacts:
#    when: always
#    paths:
#      - playwright-report
#    expire_in: 1 day
