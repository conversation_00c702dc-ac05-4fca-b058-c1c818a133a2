#!/bin/bash

set -ex

if [ -z "$GCP_PROJECT" ] || [ -z "$GCP_SERVICE" ] || [ -z "$GCP_REGION" ]; then
    echo "ERROR: set environment variable GCP_PROJECT, GCP_SERVICE and GCP_REGION, please!"
    exit 1
fi

if [ -z "$GCP_HOW_MANY_REVISIONS_LEFT"]; then
    GCP_HOW_MANY_REVISIONS_LEFT=5
fi

# build query to get last 5 revisions
QUERY_REVISIONS_THAT_WILL_NOT_DELETED=$(
    gcloud run revisions list \
    --filter="status.conditions.type:Active AND status.conditions.status:'False'" \
    --format='value(metadata.name)' \
    --project $GCP_PROJECT \
    --region $GCP_REGION \
    --service $GCP_SERVICE \
    --sort-by "~creation_timestamp" \
  | head -n $GCP_HOW_MANY_REVISIONS_LEFT \
  | awk '{printf "metadata.name != "$0" AND "}'
)

REVISIONS_TO_DELETE=$(
    gcloud run revisions list \
    --filter="$(echo $QUERY_REVISIONS_THAT_WILL_NOT_DELETED) status.conditions.type:Active AND status.conditions.status:'False'" \
    --project $GCP_PROJECT \
    --region $GCP_REGION \
    --service $GCP_SERVICE \
    --format "value(metadata.name)"
)

echo $REVISIONS_TO_DELETE | tr ' ' '\n' | xargs -r -L1 \
    gcloud run revisions delete \
    --region $GCP_REGION \
    --project $GCP_PROJECT \
    --quiet
