#!/bin/bash

set -ex

# Fetch the latest commit message and store it in COMMIT_MESSAGE
COMMIT_MESSAGE=$(git log --format=%B -n 1 $CI_COMMIT_SHA)

# Extract the value after 'env:' using awk
CUSTOM_CONTENTFUL_ENVIRONMENT=$(echo "$COMMIT_MESSAGE" | awk '/env:/ {print $2}')

# If main branch, always use the default environment
if [ "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME" == "main" ]; then
    echo "Current target branch: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME"
    echo "Target branch is main, using default CF environment. Current Contentful environment: $CONTENTFUL_ENVIRONMENT"
# Otherwise, check for custom environment in commit message
elif [ -n "$CUSTOM_CONTENTFUL_ENVIRONMENT" ]; then
    echo "Using CUSTOM_CONTENTFUL_ENVIRONMENT=$CUSTOM_CONTENTFUL_ENVIRONMENT from commit message"
    export CONTENTFUL_ENVIRONMENT=$CUSTOM_CONTENTFUL_ENVIRONMENT
else
    echo "No custom environment variable found in commit message, using default CF environment. Current Contentful environment: $CONTENTFUL_ENVIRONMENT"
fi