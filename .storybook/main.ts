import type { StorybookConfig } from '@storybook/nextjs';
import path from 'path';

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],

  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-actions',
    '@storybook/addon-interactions',
    '@storybook/addon-docs',
    '@chromatic-com/storybook',
  ],

  framework: {
    name: '@storybook/nextjs',
    options: {},
  },

  docs: {},

  staticDirs: [
    '../public',
    {
      from: '../public',
      to: 'public',
    },
  ],

  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },

  features: {
    experimentalRSC: true,
  },

  webpackFinal: async (config) => {
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/hooks/useUsercentricsConsent': path.resolve(__dirname, './mocks/useUsercentricsConsent.ts'),
      };
    }
    return config;
  },
};

export default config;
