import React from 'react';

import localFont from 'next/font/local';

import type { Preview } from '@storybook/react';
import { initialize, mswLoader } from 'msw-storybook-addon';

import '@/app/globals.css';

// Declare UC_UI type
declare global {
  interface Window {
    UC_UI: {
      isInitialized: () => boolean;
      acceptService: (serviceId: string) => void;
      showFirstLayer: () => void;
      showSecondLayer: () => void;
      getServicesBaseInfo: () => any[];
    };
  }
}

initialize({
  onUnhandledRequest: 'warn',
});

const soehne = localFont({
  src: [
    {
      path: '../../public/fonts/Soehne-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Soehne-Italic.woff2',
      weight: '400',
      style: 'italic',
    },
    {
      path: '../../public/fonts/Soehne-SemiBold.woff2',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Soehne-SemiBoldItalic.woff2',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-soehne',
});

const soehneCondensed = localFont({
  src: '../../public/fonts/SoehneCondensed-SemiBold.woff2',
  display: 'swap',
  variable: '--font-soehne-condensed',
});

const fontDecorator = (Story: React.ComponentType) => (
  <div className={`${soehne.variable} ${soehneCondensed.variable} font-soehne`}>
    <Story />
  </div>
);

const usercentricsDecorator = (Story: React.ComponentType) => {
  // Mock the window.UC_UI object
  if (typeof window !== 'undefined') {
    window.UC_UI = {
      isInitialized: () => true,
      acceptService: () => {},
      showFirstLayer: () => {},
      showSecondLayer: () => {},
      getServicesBaseInfo: () => [],
    };
  }

  return <Story />;
};

const preview: Preview = {
  parameters: {
    backgrounds: {
      default: 'light',
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },

  loaders: [mswLoader],
  decorators: [fontDecorator, usercentricsDecorator],
  tags: ['autodocs'],
};

export default preview;
