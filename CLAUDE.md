# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Primary Development:**

- `npm run dev` - Start development server with GraphQL codegen watch mode and HTTPS
- `npm run dev-turbo` - Development server with Turbo mode enabled
- `npm run build` - Production build (runs GraphQL codegen first)

**Testing:**

- `npm test` - Run Jest unit tests
- `npm run test:watch` - Run Jest tests in watch mode
- `npm run e2e` - Run Playwright end-to-end tests
- `npm run e2e:ui` - Run Playwright tests with UI mode

**Code Quality:**

- `npm run lint` - Run ESLint with Next.js rules
- `npm run codegen` - Generate TypeScript types from GraphQL schema
- `npm run check:circles` - Check for circular dependencies

**Component Development:**

- `npm run storybook` - Start Storybook for component development on port 6006

**Icon Management:**

- `npm run generate-icons` - Process SVG icons from assets/icons/ into React components

## Architecture Overview

This is a Next.js 14 website for Deutsche Glasfaser using App Router architecture with the following key patterns:

### Folder Structure

- `src/app/` - Next.js App Router (pages, layouts, API routes)
- `src/common/` - Shared utilities, components, hooks, stores
- `src/modules/` - Feature-specific components (TariffOverview, AvailabilityCheck, etc.)

### Component Architecture

- **Common Components**: Reusable UI elements in `src/common/components/`
- **Modules**: Business logic components in `src/modules/`
- **Pages**: App Router pages with server-side data fetching

### State Management

- **Zustand stores** in `src/common/stores/` for client-side state
- **TanStack React Query** for server state and caching
- **Store pattern**: Each domain has its own store (UserStore, AvailabilityCheckStore, etc.)

### API Integration

- **Contentful CMS**: GraphQL API with code generation (`npm run codegen`)
- **DG Backend APIs**: REST APIs for availability checks, offers, projects
- **Third-party APIs**: SmartRecruiters (jobs), Algolia (search), Trustpilot (reviews)

## Key Technologies & Patterns

**Tech Stack:**

- Next.js 14 with App Router and TypeScript
- Tailwind CSS for styling with custom cache handler
- Redis caching for performance
- React Hook Form for form management
- GraphQL Code Generation for type safety

**Content Management:**

- All content managed through Contentful CMS
- GraphQL schema generates TypeScript types automatically
- Draft mode available for content preview: `https://localhost:3000/api/draft?secret=secret`

**Tracking & Analytics:**

- Comprehensive Google Tag Manager integration
- Custom `useTracking` hook for event tracking
- All UI components automatically tracked (Button, Checkbox, TextLink, etc.)
- Each module wrapped in `ModuleWrapper` for tracking context

**Forms & User Input:**

- React Hook Form with dynamic form builder from Contentful
- File upload capabilities with validation
- ReCaptcha integration for spam protection
- Custom form components with automatic tracking

## Development Environment Setup

1. **Environment Variables**: Copy `.env.example` to `.env.local` and fill with values from PW Depot
2. **HTTPS Development**: Development runs on `https://localhost:3000` by default
3. **GraphQL Types**: Run `npm run codegen` when Contentful schema changes
4. **Icons**: Place SVG files in `assets/icons/` and run `npm run generate-icons`

## Testing Strategy

- **Unit Tests**: Jest with React Testing Library in `__tests__/` directories
- **E2E Tests**: Playwright tests in `e2e/` directory
- **Component Tests**: Storybook stories for visual component testing
- **API Mocking**: MSW (Mock Service Worker) for API mocking in tests

## Performance & Caching

- **Redis Caching**: Custom cache handler with fallback to LRU cache
- **Image Optimization**: Next.js Image component with multiple formats (AVIF, WebP)
- **Bundle Analysis**: `@next/bundle-analyzer` available for performance analysis
- **Server-Side Rendering**: ISR (Incremental Static Regeneration) for optimal performance

## Code Quality & Conventions

- **TypeScript**: Strict mode enabled with comprehensive type definitions
- **ESLint**: Next.js, TypeScript, and accessibility rules configured
- **Prettier**: Code formatting with import sorting
- **Commit Standards**: Conventional commits enforced with commitlint
- **Pre-commit Hooks**: Lint-staged runs linting and formatting on staged files

## Business Domains

**Core Features:**

- **Availability Check**: Internet service availability checking and results
- **Tariff Management**: Product offerings, pricing, and comparison
- **Project Information**: Network expansion and construction project status
- **Job Portal**: Career opportunities with SmartRecruiters integration
- **Content Pages**: Marketing and informational content from Contentful CMS

**Key Modules:**

- `AvailabilityCheck` - Core business functionality for service availability
- `TariffOverview` - Product comparison and selection
- `Jobportal` - Career page with job listings and applications
- `InteractiveMap` - Network coverage visualization
- `Shopfinder` - Store locator functionality
