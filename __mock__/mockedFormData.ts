import type { FormField, FormGroup } from '@/types/forms';

export const mockedFormData: (FormField | FormGroup)[] = [
  {
    fieldName: 'firstname',
    inputType: 'text',
    label: 'Vorname',
    validation: 'name',
    validationRules: {
      required: 'Das ist ein Pf<PERSON>feld',
      minLength: { value: 3, message: 'Es sind min 3 Zeichen erlaubt' },
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '1/2',
  },
  {
    fieldName: 'lastname',
    inputType: 'text',
    label: 'Nachname',
    validation: 'name',
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '1/2',
  },
  {
    fieldName: 'language',
    inputType: 'select',
    label: 'Sprache',
    options: [
      { value: 'english', label: 'Englisch' },
      { value: 'french', label: '<PERSON><PERSON><PERSON><PERSON>' },
    ],
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    tooltip: {
      title: 'English speaking?',
      text: 'We have a special contact form for english customers.',
    },
  },
  {
    fieldName: 'englishInfoBox',
    inputType: 'info',
    label: 'Do you speak english only?',
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'eq',
      },
    ],
    infoBox: {
      text: 'Please use the contact form for english customers for your request.',
      cta: { label: 'Contact', href: 'https://www.google.de' },
    },
  },
  {
    fieldName: 'street',
    inputType: 'text',
    label: 'Straße',
    validation: 'street',
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '2/3',
  },
  {
    fieldName: 'number',
    inputType: 'text',
    label: 'Nummer',
    validation: 'houseNumber',
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '1/3',
  },
  {
    fieldName: 'upload',
    inputType: 'file',
    label: 'Laden Sie Ihre Datei(en) hoch',
    required: true,
    tooltip: {
      title: 'Datei Upload',
      text: 'Bitte laden Sie Ihre Datei(en) hoch',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
  },
  {
    fieldName: 'tos',
    inputType: 'checkbox',
    label: 'Checkbox',
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
  },
  {
    fieldName: 'description',
    inputType: 'textarea',
    label: 'Beschreibung',
    conditions: [
      {
        fieldName: 'tos',
        value: true,
        operator: 'eq',
      },
    ],
    validationRules: {
      maxLength: 500,
    },
  },
  {
    fieldName: 'isCustomer',
    inputType: 'radio',
    label: 'Bestandskunde',
    options: [
      { value: 'yes', label: 'Ja' },
      { value: 'no', label: 'Nein' },
    ],
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
  },
  {
    fieldName: 'customerGroup',
    label: 'Machen Sie Angaben zu ihrem Kundenkonto:',
    conditions: [
      {
        fieldName: 'isCustomer',
        value: 'yes',
        operator: 'eq',
      },
    ],
    fields: [
      {
        fieldName: 'customerNumber',
        inputType: 'text',
        label: 'Kundennummer',
        options: [
          { value: 'yes', label: 'Ja' },
          { value: 'no', label: 'Nein' },
        ],
        validationRules: {
          required: 'Das ist ein Pflichtfeld',
        },
      },
      {
        fieldName: 'customerNumberInfo',
        inputType: 'info',
        label: 'Kundennummer',
        infoBox: {
          text: 'Ihre Kundennummer finden sie im Kundenportal',
          cta: { label: 'Zum Kundenportal', href: 'https://www.google.de' },
        },
      },
    ],
  },
  {
    fieldName: 'age',
    inputType: 'number',
    label: 'Alter',
    defaultValue: 18,
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '1/3',
  },
  {
    fieldName: 'dateInput',
    inputType: 'date',
    label: 'Datum',
    validationRules: {
      required: 'Das ist ein Pflichtfeld',
    },
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
    width: '2/3',
  },
  {
    fieldName: 'submit',
    inputType: 'submit',
    label: 'Abschicken',
    conditions: [
      {
        fieldName: 'language',
        value: 'english',
        operator: 'neq',
      },
    ],
  },
];
