export const mockedPageAction = {
  internalName: 'Test Page Action',
  title: 'Test Page Action',
  slug: '/test-page-action',
  businessArea: 'Privatkunden',
  notification: undefined,
  stage: null,
  blocks: [
    {
      __typename: 'TextButtonModule',
      sys: {
        id: '6F3bX8B4t7Xvz6J4wZ7Xvz',
        spaceId: 'nurz7nsnha04',
        environmentId: 'master',
        publishedAt: '2023-05-03T09:22:42.681Z',
        firstPublishedAt: '2023-04-18T09:43:58.842Z',
        publishedVersion: 22,
      },
      internalName: 'Text Module',
      text: 'Lorem Ipsum dolor met...',
      headline: 'They floated in the Japanese night',
      image: {
        width: 720,
        height: 480,
        src: 'https://images.ctfassets.net/cfwjf2vnzww5/742SXMacNXJPhA0f9dy1VC/5a924f652c352094b6d6ff4427ed7dd3/csm_90af1ed4c542b0d06b5fa68d733345d3_Standardkampagne_a320229727.webp',
        alt: 'TextImageTeaser Image',
      },
      cta: {
        type: 'Link',
        label: 'Read more',
        link: '#',
      },
      theme: 'light',
    },
  ],
  faq: undefined,
  teaserText: 'Lorem Ipsum dolor met...',
  teaserImage: {
    width: 720,
    height: 480,
    src: 'https://images.ctfassets.net/cfwjf2vnzww5/742SXMacNXJPhA0f9dy1VC/5a924f652c352094b6d6ff4427ed7dd3/csm_90af1ed4c542b0d06b5fa68d733345d3_Standardkampagne_a320229727.webp',
    alt: 'TextImageTeaser Image',
  },
  seoData: {
    title: 'Test Page Action',
    description: 'Test Page Action',
    keywords: ['Test', 'Page', 'Action'],
    noIndex: false,
    noFollow: false,
  },
};
