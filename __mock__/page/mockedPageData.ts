import type { Page } from '@/apis/contentful/generated/types';

export const mockedPageData = {
  _id: '2t7z9Wl2N23ujl0IfPKGvF',
  __typename: 'Page',
  sys: {
    id: '2t7z9Wl2N23ujl0IfPKGvF',
    spaceId: 'nurz7nsnha04',
    environmentId: '17065d4d-4821-47b0-b04d-d5962a930eab',
    publishedAt: '2023-05-03T09:22:42.681Z',
    firstPublishedAt: '2023-04-18T09:43:58.842Z',
    publishedVersion: 22,
  },
  contentfulMetadata: {
    concepts: [],
    tags: [],
  },
  slug: '/',
  pageType: 'Start-/Übersichtsseite',
  categories: null,
  showMetadata: true,
  previewVideo: null,
} as Page;
