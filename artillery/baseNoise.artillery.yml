config:
  # This is a test server run by team Artillery
  # It's designed to be highly scalable
  target: https://dg.acc.gcp.dg-sys.net
  phases:
    - duration: 1
      arrivalRate: 1
      rampTo: 1
      name: Debug
  processor: ./flows.js
  engines:
    playwright:
      launchOptions:
        headless: true
      defaultNavigationTimeout: 1500
      defaultTimeout: 2000
      aggregateByName: true
      contextOptions:
        extraHTTPHeaders:
          Authorization: 'Basic ZWNvbTpyZWxhdW5jaA=='

  environments:
    local-debug:
      phases:
        - duration: 5 #seconds
          arrivalRate: 1 #users per second
          rampTo: 2 #users per second
          name: Debug
    production:
      phases:
        - duration: 60
          arrivalRate: 1
          rampTo: 2
          name: Production

scenarios:
  - name: Browse
    engine: playwright
    flowFunction: browse
    flow: []
