const { randomUserIdleTimeout, getRandomAddress} = require('../helpers')

async function checkAvailability(page, context) {
    //availability check
    await page.goto('/wohnungswirtschaft') //go to this page to avoid duplicate form
    await randomUserIdleTimeout(page)
    const address = await getRandomAddress()
    await page.locator('xpath=/html/body/div[1]/div/div[2]/div[1]/div/button').click()
    await randomUserIdleTimeout(page)
    await page.locator('#AC-PLZ\\ \\/\\ Ort').fill(address.zip)
    await randomUserIdleTimeout(page)
    await page.locator('xpath=/html/body/div[1]/div/div[2]/div[2]/div[1]/div/div/div/div/div[1]/div/div[2]/div[1]/div[1]/div[2]').click()
    await randomUserIdleTimeout(page)
    await page.locator('#AC-Straße').fill(address.street)
    await randomUserIdleTimeout(page)
    await page.locator('xpath=/html/body/div[1]/div/div[2]/div[2]/div[1]/div/div/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]').click()
    await randomUserIdleTimeout(page)
    await page.locator('#AC-Nr\\.').fill(address.nr)
    await randomUserIdleTimeout(page)
    await page.locator('xpath=/html/body/div[1]/div/div[2]/div[2]/div[1]/div/div/div/div/div[1]/div/div[2]/div[1]/div[3]/div[2]').click()
    await randomUserIdleTimeout(page)
    await page.locator('xpath=/html/body/div[1]/div/div[2]/div[2]/div[1]/div/div/div/div/div[1]/div/div[2]/div[1]/button/span[1]').click()
    await randomUserIdleTimeout(page)
}

module.exports = {checkAvailability}