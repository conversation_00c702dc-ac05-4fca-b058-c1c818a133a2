const { acceptCookies, randomUserIdleTimeout, getRandomAddress} = require('../helpers')
const { checkAvailability } = require('./availabilityCheck')

async function browse(page, context) {
    //start page
    await page.goto('/')
    await randomUserIdleTimeout(page)

    //TODO: fix cookies
    // await acceptCookies(page)
    // await randomUserIdleTimeout(page)

    //tariffs
    await page.goto('/tarife')
    await randomUserIdleTimeout(page)
    await page.goto('/tarife/dg-basic-100')
    await randomUserIdleTimeout(page)
    await page.goto('/tarife/dg-classic-300')
    await randomUserIdleTimeout(page)
    await page.goto('/tarife/dg-premium-500')
    await randomUserIdleTimeout(page)
    await page.goto('/tarife/dg-giga-1000')
    await randomUserIdleTimeout(page)

    await checkAvailability(page, context)

    //nvm campaign
    await page.goto('/aktion')
    await randomUserIdleTimeout(page)
    await page.goto('/rabatt')
    await randomUserIdleTimeout(page)

    //grid expansion
    await page.goto('/netzausbau')
    await randomUserIdleTimeout(page)
    await page.goto('/netzausbau/rheinland-pfalz')
    await randomUserIdleTimeout(page)
    await page.goto('/netzausbau/niedersachsen')
    await randomUserIdleTimeout(page)
    await page.goto('/netzausbau/baden-wuerttemberg/landkreis-goeppingen/adelberg')
    await randomUserIdleTimeout(page)
    await page.goto('/netzausbau/baden-wuerttemberg/landkreis-boeblingen/aidlingen')
    await randomUserIdleTimeout(page)

    //fibre info
    await page.goto('/glasfaser/eigentuemer')
    await randomUserIdleTimeout(page)
    await page.goto('/glasfaser/bau/hausbegehung')
    await randomUserIdleTimeout(page)
    await page.goto('/digital-wissen/ftth')
    await randomUserIdleTimeout(page)
    await page.goto('glasfaser/leitungswege')
    await randomUserIdleTimeout(page)
}

module.exports = browse