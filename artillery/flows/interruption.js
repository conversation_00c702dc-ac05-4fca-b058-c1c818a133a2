const { acceptCookies, randomUserIdleTimeout, getRandomAddress} = require('../helpers')
const { checkAvailability } = require('./availabilityCheck')

async function interruption(page, context) {
    await page.goto('/')
    await randomUserIdleTimeout(page)
    // await acceptCookies(page)
    // await randomUserIdleTimeout(page)

    await checkAvailability(page, context)

    await page.goto('/service/stoerungen-und-wartung')
    await randomUserIdleTimeout(page)

    await page.goto('/service/speedtest')
    await randomUserIdleTimeout(page)

    await page.goto('/service/downloads')
    await randomUserIdleTimeout(page)
    await page.goto('/telefonische-beratung-b2c')
    await randomUserIdleTimeout(page)
    await page.goto('/service/faq')
    await randomUserIdleTimeout(page)
}

module.exports = interruption