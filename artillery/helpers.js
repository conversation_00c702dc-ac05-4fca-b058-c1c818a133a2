const config = require('./config')

async function acceptCookies(page, context) {
    const cookieButton = await page.locator("[data-testid='uc-accept-all-button']")
    if (!cookieButton) {
        return
    }
    await cookieButton.click()
}

async function randomUserIdleTimeout(page, increaseDuration = false) {
    const min = config.userIdleTimeout.min + (increaseDuration ? 2000 : 0)
    const max = config.userIdleTimeout.max + (increaseDuration ? 5000 : 0)

    const timeout = Math.floor(Math.random() * (max - min) + min)
    await page.waitForTimeout(timeout)
}

async function getRandomAddress() {
    return config.randomAddress[Math.floor(Math.random() * config.randomAddress.length)]
}

module.exports = {
    acceptCookies,
    randomUserIdleTimeout,
    getRandomAddress
}