# This is a test designed to simulate many users having problems with their internet and checking
# on the website for known problems

config:
  target: https://dg.acc.gcp.dg-sys.net
  phases:
    - duration: 1
      arrivalRate: 1
      rampTo: 1
      name: Debug
  processor: ./flows.js
  engines:
    playwright:
      launchOptions:
        headless: true
      defaultNavigationTimeout: 1500
      defaultTimeout: 2000
      aggregateByName: true
      contextOptions:
        extraHTTPHeaders:
          Authorization: 'Basic ZWNvbTpyZWxhdW5jaA=='

  environments:
    local-debug:
      phases:
        - duration: 1
          arrivalRate: 1
          rampTo: 1
          name: Debug
    production:
      phases:
        - duration: 180
          arrivalRate: 1000
          rampTo: 1200
          name: Production

scenarios:
  - name: Interruption
    engine: playwright
    flowFunction: interruption
    flow: []
