import { expect, test } from '@playwright/test';

test.describe('AC Flyout', () => {
  async function fillAvailabilityCheckForm({
    page,
    city,
    citySuggestion,
    street,
    streetSuggestion,
    number,
    numberSuggestion,
  }: {
    page: any;
    city: string;
    citySuggestion: string;
    street: string;
    streetSuggestion: string;
    number: string;
    numberSuggestion: string;
  }) {
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('PLZ / Ort eingeben').fill(city);
    await page.getByText(citySuggestion).click();
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('Straße eingeben').fill(street);
    await page.getByText(streetSuggestion).click();
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('Nr.').fill(number);
    await page.getByText(numberSuggestion).click();
    await page.locator('#verfuegbarkeitscheck-navigation').getByTestId('ac-submit-button').click();
  }

  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.getByTestId('uc-accept-all-button').click();
    await page.getByTestId('ac-open-button').click();
  });

  test('AC Flyout is available', async ({ page }) => {
    await expect(page.locator('#availabilityCheck')).toBeVisible();

    const dataLayerContent = await page.evaluate(() => {
      return window.dataLayer;
    });

    expect(dataLayerContent).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ event: 'VC_Event', byVcType: 'flyOut', interactionVc: 'open' }),
      ]),
    );
  });

  test('AC Flyout shows error for not existing city', async ({ page }) => {
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('PLZ / Ort eingeben').fill('notexisting');
    await expect(page.locator('#verfuegbarkeitscheck-navigation-city-input-error')).toBeVisible();
  });

  test('AC Flyout shows hint if city input value has < 3 chars', async ({ page }) => {
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('PLZ / Ort eingeben').fill('bo');
    await expect(page.locator('#availabilityCheck').getByText('Geben Sie mind. 3 Zeichen an')).toBeVisible();
  });

  test('AC Flyout shows suggestions if city input value has 3 chars', async ({ page }) => {
    await page.locator('#verfuegbarkeitscheck-navigation').getByPlaceholder('PLZ / Ort eingeben').fill('bor');
    await expect(page.getByText('Borken (Westfalen)')).toBeVisible();
  });

  test('AC Flyout leads to interested button', async ({ page }) => {
    await fillAvailabilityCheckForm({
      page,
      city: 'bor',
      citySuggestion: 'Borken (Westfalen)',
      street: 'auf',
      streetSuggestion: 'Auf dem Dievelt',
      number: '1',
      numberSuggestion: '19',
    });

    await expect(
      page.locator('#availabilityCheck').getByRole('link', { name: 'Als Interessent registrieren' }),
    ).toBeVisible();

    const dataLayerContent = await page.evaluate(() => {
      return window.dataLayer;
    });

    expect(dataLayerContent).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ event: 'VC_Event', byVcType: 'flyOut', interactionVc: 'negative' }),
      ]),
    );
  });
});
