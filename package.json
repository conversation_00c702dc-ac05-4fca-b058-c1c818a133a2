{"name": "dg-website", "version": "2.18.3", "private": true, "scripts": {"dev": "concurrently -n codegen,next \"npm:codegen -- --watch\" \"next dev --experimental-https\"", "dev-turbo": "concurrently -n codegen,next \"npm:codegen -- --watch\" \"next dev --turbo --experimental-https\"", "dev-vscode": "NODE_OPTIONS='--inspect' next dev", "build": "graphql-codegen --config codegen.config.ts && next build", "start": "next start", "e2e": "npx playwright test", "e2e:ui": "npx playwright test --ui", "test": "jest", "test:watch": "jest --watch", "lint": "next lint", "lint-staged": "DEBUG=\"lint-staged:*\" lint-staged", "codegen": "graphql-codegen --config codegen.config.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "remove-svg-fill": "sed \"s/#14A0DC/currentColor/g\" assets/icons/sm/*.svg", "svgr": "svgr -d src/common/components/Icons assets/icons/ --no-prettier --typescript && prettier src/common/components/Icons --write", "format-icons": "prettier src/common/components/Icons --write", "generate-icons": "npm run svgr && npm run format-icons", "next-dev": "next dev --experimental-https --turbo -H 0.0.0.0", "check:circles": "madge --circular --ts-config ./tsconfig.json --extensions ts,tsx src/"}, "dependencies": {"@contentful/rich-text-html-renderer": "^16.6.1", "@contentful/rich-text-react-renderer": "^15.22.1", "@floating-ui/react": "^0.26.19", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/error-message": "^2.0.1", "@neshca/cache-handler": "^1.4.0", "@neshca/json-replacer-reviver": "^1", "@next/bundle-analyzer": "^14.2.5", "@parcel/watcher": "^2.4.1", "@sentry/browser": "^8.17.0", "@sentry/nextjs": "^8.17.0", "@sentry/tracing": "^7.114.0", "@sentry/utils": "^8.17.0", "@storybook/test": "^8.6.12", "@tanstack/react-query": "^5.51.1", "@tanstack/react-query-devtools": "^5.51.1", "@types/node": "^20.14.10", "@types/react-dom": "^18.3.0", "@vis.gl/react-google-maps": "^1.5.2", "algoliasearch": "^4.24.0", "class-variance-authority": "^0.7.0", "dotenv": "^16.4.7", "feiertagejs": "^1.4.1", "focus-visible": "^5.2.0", "graphql": "^16.9.0", "graphql-request": "^6.1.0", "html-react-parser": "^5.1.10", "lodash-es": "^4.17.21", "luxon": "^3.4.4", "motion": "^12.6.5", "next": "^14.2.27", "next-seo": "^6.5.0", "nextjs-toploader": "^1.6.12", "nodemailer": "^6.9.14", "react": "^18.3.1", "react-animate-height": "^3.2.3", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.0", "react-markdown": "^9.0.1", "react-youtube": "^10.1.0", "redis": "latest", "schema-dts": "^1.1.2", "sharp": "^0.33.5", "striptags": "^3.2.0", "swr": "^2.3.3", "tailwind-merge": "^2.4.0", "wretch": "^2.9.0", "zustand": "^4.5.4"}, "devDependencies": {"@chromatic-com/storybook": "^2.0.2", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@contentful/rich-text-types": "^16.6.1", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-graphql-request": "^5.0.0", "@graphql-codegen/typescript-operations": "^4.2.3", "@playwright/test": "^1.50.0", "@sentry/types": "^8.17.0", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.4.4", "@storybook/blocks": "^8.4.4", "@storybook/cli": "^8.6.12", "@storybook/manager-api": "^8.4.4", "@storybook/nextjs": "^8.6.12", "@storybook/preview-api": "^8.4.4", "@storybook/react": "^8.6.12", "@storybook/theming": "^8.4.4", "@svgr/cli": "^8.1.0", "@tailwindcss/postcss": "^4.1.5", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "29.5.12", "@types/lodash": "^4.17.13", "@types/luxon": "^3.4.2", "@types/nodemailer": "^6.4.15", "@types/react": "^18.3.3", "@types/react-google-recaptcha": "^2.1.9", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "clsx": "^2.1.1", "concurrently": "^8.2.2", "contentful-management": "^11.44.0", "contentful-migration": "^4.22.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-storybook": "^0.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.7", "madge": "^8.0.0", "msw": "^2.4.9", "msw-storybook-addon": "^2.0.3", "postcss": "^8.4.39", "postcss-focus-visible": "^9.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "start-server-and-test": "^2.0.4", "storybook": "^8.6.12", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "whatwg-fetch": "^3.6.20", "yargs": "^17.7.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --cache --fix", "*.{js,css,md,ts,tsx}": "prettier --write"}, "msw": {"workerDirectory": "public"}, "madge": {"detectiveOptions": {"ts": {"skipAsyncImports": true, "skipTypeImports": true}, "tsx": {"skipAsyncImports": true, "skipTypeImports": true}}}}