import type { <PERSON>ada<PERSON> } from 'next';
import { draftMode } from 'next/headers';
import { notFound } from 'next/navigation';

import { fetchBreadcrumbs } from '@/apis/contentful/getBreadcrumbs';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import { fetchPage, fetchPages } from '@/apis/contentful/getPages';
import BlockRenderer, { type Block } from '@/apis/contentful/renderer/BlockRenderer';
import type { StageType } from '@/apis/contentful/renderer/StageRenderer';
import StageRenderer from '@/apis/contentful/renderer/StageRenderer';

import Breadcrumb from '@/components/Breadcrumb';
import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageSchema from '@/components/PageSchema';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';

import type { PageParams, PageProps } from '@/types/pageProps';

import { getPageParams } from '@/utils/getPageParams';
import { getSanitizedSlug } from '@/utils/url/getSanitizedSlug';

export async function generateStaticParams(): Promise<PageParams[]> {
  const data = await fetchPages({ preview: false });
  return getPageParams(data);
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  // Simulate a server error when trying to access the test-500 page
  if (params.slug?.[0] === 'test-500') {
    throw new Error('Simulated server error');
  }

  const { isEnabled } = draftMode();
  const slug = getSanitizedSlug({ params });
  const data = await fetchPage({ slug: slug, preview: isEnabled });

  if (!data) {
    return notFound();
  }

  const seoTitle = data.seoData.title || 'Deutsche Glasfaser liefert Ihnen Internet';
  const seoDescription =
    data.seoData.description ||
    'Deutsche Glasfaser liefert Ihnen Internet, Telefon und Fernsehen mit bis zu 1 Gbit/s (1000 Mbit/s) und schließt Ihr Zuhause an das Internet der Zukunft an.';

  return {
    title: seoTitle,
    description: seoDescription,
    keywords: data.seoData.keywords,
    robots: {
      index: !data.seoData.noIndex,
      follow: !data.seoData.noFollow,
    },
    alternates: {
      canonical: `/${slug}`,
    },
  };
}

export default async function Page({ params }: PageProps) {
  const { isEnabled } = draftMode();
  const currentSlug = getSanitizedSlug({ params });
  const data = await fetchPage({ slug: currentSlug, preview: isEnabled });

  if (!data) {
    return notFound();
  }

  const {
    businessArea,
    slug,
    notification,
    stage,
    blocks,
    pageType,
    title,
    teaserImage,
    publishedAt,
    scrollToTop,
    callButton,
  } = data;

  const mainNavigation = await fetchMainNavigation({ businessArea });

  const breadcrumbs = await fetchBreadcrumbs({ pathname: currentSlug });

  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });

  return (
    <PageWrapper
      businessArea={businessArea}
      pageType={pageType}
      hasScrollToTop={!!scrollToTop}
      hasCallButton={!!callButton}
    >
      <SkipLinks />
      <PageHeader pageType={pageType}>
        <MetaNavigation businessArea={businessArea} pageType={pageType} />

        <MainNavigation
          businessArea={businessArea}
          navigationItems={mainNavigation}
          initialNotification={notification}
          pageType={pageType}
        />
      </PageHeader>

      <MainContent>
        <Breadcrumb pageType={pageType} breadcrumbs={breadcrumbs} />
        <StageRenderer stage={stage as StageType} />
        <BlockRenderer blocks={blocks as Block[]} />
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={slug} />

      <PageSchema
        businessArea={businessArea}
        pageType={pageType}
        title={title}
        image={teaserImage}
        publishedAt={publishedAt}
      />
    </PageWrapper>
  );
}
