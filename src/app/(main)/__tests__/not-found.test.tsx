import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';

import NotFound from '@/app/(main)/not-found';

// TODO: Fix issue rendering NotFound
describe.skip('Test NotFound component', () => {
  it('renders 404 h1', async () => {
    render(<NotFound />);
    await waitFor(() => expect(screen.getByText(/Seite nicht gefunden/i)).toBeInTheDocument());
  });

  // test('renders Not found paragraph', () => {
  //   render(<NotFound />);
  //   const paragraphElement = screen.getByText(/Not found/i);
  //   expect(paragraphElement).toBeInTheDocument();
  // });
});
