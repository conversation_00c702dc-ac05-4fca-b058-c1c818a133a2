import type { <PERSON>actNode } from 'react';
import React from 'react';

import type { Metadata, Viewport } from 'next';
import localFont from 'next/font/local';
import { NextResponse } from 'next/server';

import { cva } from 'class-variance-authority';
import LoadingBar from 'nextjs-toploader';

import Usercentrics from '@/modules/Usercentrics';

import DraftModeBanner from '@/components/DraftModeBanner';
import { GoogleTagManager } from '@/components/GTM';
import { OPMC } from '@/components/OPMC';

import { cn } from '@/utils/cn';

import '../globals.css';

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? 'https://www.deutsche-glasfaser.de/';

const soehne = localFont({
  src: [
    {
      path: '../../../public/fonts/Soehne-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/Soehne-Italic.woff2',
      weight: '400',
      style: 'italic',
    },
    {
      path: '../../../public/fonts/Soehne-SemiBold.woff2',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/Soehne-SemiBoldItalic.woff2',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-soehne',
});

const soehneCondensed = localFont({
  src: '../../../public/fonts/SoehneCondensed-SemiBold.woff2',
  display: 'swap',
  variable: '--font-soehne-condensed',
});

const iconFont = localFont({
  src: '../../../public/fonts/ico-dgl-br.woff2',
  variable: '--font-dgl-icons',
});

const layoutStyles = cva('relative m-auto max-w-page shadow-body');

const htmlStyles = cva(
  layoutStyles({
    className: 'scroll-pt-mobile-nav scroll-smooth bg-sand font-soehne text-basalt lg:scroll-pt-desktop-nav',
  }),
);

export function middleware() {
  const res = NextResponse.next();
  res.headers.set(
    'Accept-CH',
    'Sec-CH-UA, Sec-CH-UA-Full-Version-List, Sec-CH-UA-Model, Sec-CH-UA-Mobile, Sec-CH-UA-Platform, Sec-CH-UA-Platform-Version',
  );
  res.headers.set(
    'Delegate-CH',
    'Sec-CH-UA https://dsst.deutsche-glasfaser.de; Sec-CH-UA-Full-Version-List https://dsst.deutsche-glasfaser.de; Sec-CH-UA-Model https://dsst.deutsche-glasfaser.de; Sec-CH-UA-Mobile https://dsst.deutsche-glasfaser.de; Sec-CH-UA-Platform https://dsst.deutsche-glasfaser.de; Sec-CH-UA-Platform-Version https://dsst.deutsche-glasfaser.de;',
  );

  const headersObject: { [key: string]: string } = {};
  for (const [name, value] of res.headers.entries()) {
    headersObject[name] = value;
  }

  return headersObject;
}

export const metadata: Metadata = {
  other: middleware(),
  metadataBase: new URL(BASE_URL),
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html lang="de" className={cn(soehne.variable, soehneCondensed.variable, iconFont.variable, htmlStyles())}>
      <body className={layoutStyles()}>
        <LoadingBar showSpinner={false} />
        {children}
        <DraftModeBanner />
        <GoogleTagManager />
        <Usercentrics />
        <OPMC />
      </body>
    </html>
  );
}
