import React from 'react';

import type { Metada<PERSON> } from 'next';
import { draftMode } from 'next/headers';
import { notFound, permanentRedirect } from 'next/navigation';

import { BusinessArea, QUERY_PARAMS } from '@/app/consts';

import { fetchSeoDataByName } from '@/modules/AreaSites/contentfulData/fetchSeoDataByName';
import Download from '@/modules/Download';
import Grid from '@/modules/Grid';
import ImportantInfo from '@/modules/ImportantInformation';
import PointOfContact from '@/modules/PointOfContact';
import ProjectPromotionLogos from '@/modules/ProjectPromotionLogos';
import ProjectVideos from '@/modules/ProjectVideos';
import SealBar from '@/modules/SealBar';
import type { SealProps } from '@/modules/SealBar/Seal';

import contentfulClient from '@/apis/contentful/api';
import type {
  Accordion as AccordionType,
  Asset,
  GridModule,
  Maybe,
  SealBarModuleExplanationText,
  TextImageButtonModule,
} from '@/apis/contentful/generated/types';
import { fetchBreadcrumbs } from '@/apis/contentful/getBreadcrumbs';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import { parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import BlockRenderer from '@/apis/contentful/renderer/BlockRenderer';
import { parseDGCmsImage } from '@/apis/dg/parser/image';
import { parseProjectStageData } from '@/apis/dg/parser/projectStage';
import { isBusinessProject, isPrivateProject } from '@/apis/dg/parser/projects';
import { getProject, getProjects } from '@/apis/dg/projects';

import PartnerGroups from '@/components/AreaSites/PartnerGroups';
import { generateProjectNotification } from '@/components/AreaSites/utils/generateProjectNotification';
import Breadcrumb from '@/components/Breadcrumb';
import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import ProjectEventList from '@/components/ProjectEventList';
import ProjectPhases from '@/components/ProjectPhases';
import { SkipLinks } from '@/components/SkipLinks';
import ProjectStage from '@/components/Stages/AreaSites';
import Teaser from '@/components/Teaser';

import type { CTA } from '@/types/cta';

import { fetchDgCmsImage } from '@/utils/fetchDgCmsImage';
import { isDeletedPhase, isDemandBundling, isNetworkActivePhase } from '@/utils/projects/checkProjectPhase';
import {
  NETZAUSBAU_B2C_CONTENT_TEASER_MODULE,
  NETZAUSBAU_B2C_SHOPFINDER_APPOINTMENT_MODULE,
  NETZAUSBAU_CF_PROJECT_PLACEHOLDER,
  NETZAUSBAU_FOERDER_FAQ_ACCORDION_MODULE,
  NETZAUSBAU_FOERDER_INFO_GRID_MODULE,
  NETZAUSBAU_FOERDER_INFO_MODULE,
  NETZAUSBAU_LABEL,
  NETZAUSBAU_PROJECT_POINTOFCONTACT_MODULE,
  NETZAUSBAU_SEO_DATA_PROJECT_MODULE,
} from '@/utils/specialIds/specialIdsCatalog';

export async function generateStaticParams(): Promise<{ projectSlug: string }[]> {
  const projects = await getProjects();

  if (!projects) return [];

  return projects.map((p) => ({ projectSlug: p.slug }));
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const { isEnabled } = draftMode();
  const project = await getProject({ project: params.project, preview: isEnabled });
  const canonical = `/netzausbau/${params.state}/${params.district}/${params.project}`;

  const seoFallback = 'Glasfaser in Gebieten - Deutsche Glasfaser';

  const seoFallbackData = {
    title: seoFallback,
    description: seoFallback,
    keywords: undefined,
    robots: undefined,
    alternates: {
      canonical,
    },
  };

  if (!project) return seoFallbackData;

  const seoData = await fetchSeoDataByName(
    `${NETZAUSBAU_SEO_DATA_PROJECT_MODULE}${project.projectStatus.statusGroupCode}`,
  );

  if (!seoData) return seoFallbackData;

  const seoProjectName = `Deutsche Glasfaser Netzausbau ${project.name}`;

  const title = seoData.seoTitle?.replaceAll(NETZAUSBAU_CF_PROJECT_PLACEHOLDER, project.name) || seoProjectName;
  const description =
    seoData.description?.replaceAll(NETZAUSBAU_CF_PROJECT_PLACEHOLDER, project.name) || seoProjectName;

  return {
    title,
    description,
    keywords: seoData?.keywords?.filter(Boolean) as string[],
    robots: {
      // Note: network active (Netz aktiv) pages are automatically removed from the sitemap. see /src/app/sitemap.ts
      index: !seoData.noIndex,
      follow: !seoData.noFollow,
    },
    alternates: {
      canonical,
    },
  };
}

export default async function Page({ params }: ProjectPageProps) {
  const { isEnabled } = draftMode();

  const project = await getProject({ project: params.project, preview: isEnabled });

  if (!project) {
    return notFound();
  }

  const businessArea = BusinessArea.Private;
  const mainNavigation = await fetchMainNavigation({ businessArea });

  const pathname = `/netzausbau/${params.state}/${params.district}/${params.project}`;
  const breadcrumbs = await fetchBreadcrumbs({
    pathname,
    labels: [NETZAUSBAU_LABEL, project.district.state.name, project.district.name, project.name],
  });

  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea });

  const {
    district,
    isFederalTerritory,
    projectType,
    projectStatus,
    name,
    deadline,
    deadlineVisibleUntil,
    deadlineAlwaysVisible,
    dateModified,
    news,
    percentage,
    targetPercentage,
    hidePercentage,
    events,
    projectPromotionLogos,
    contactPersons,
    importantInfo,
    videos,
    downloads,
  } = project;

  /**
   * If project is active or has been deleted, we redirect to its parent state page (landkreis)
   * and use the param NETZAKTIV_URL_PARAM to determine which stage we display there
   */
  if (isNetworkActivePhase(projectStatus.statusGroupCode) || isDeletedPhase(projectStatus.statusGroupCode)) {
    return permanentRedirect(
      `/netzausbau/${district.state.slug}/${district.slug}?${QUERY_PARAMS.NETZAKTIV}=${encodeURIComponent(project.name)}`,
    );
  }

  const stateSlug = district.state.slug;
  const districtSlug = district.slug;
  const currentPage = breadcrumbs.slice(-1)?.[0]?.href;

  // we can't check the correct url beforehand, so we do this here and throw 404 if the project
  // slugs don't match the url
  if (
    stateSlug &&
    districtSlug &&
    currentPage &&
    (currentPage.indexOf(stateSlug) === -1 || currentPage.indexOf(districtSlug) === -1)
  ) {
    return notFound();
  }

  const parsedStage = parseProjectStageData(project);
  const contentful = contentfulClient({ preview: isEnabled });
  const sealbarData = (await contentful.SealBarModuleById({ id: '3gqWPgORdkMs5FNiINLmJw', preview: isEnabled }))
    .sealBarModule;

  const pkTeaserGrid = (
    await contentful.GridByName({
      internalName: NETZAUSBAU_B2C_SHOPFINDER_APPOINTMENT_MODULE,
      preview: isEnabled,
    })
  ).gridModuleCollection?.items?.[0];

  const faqs = isFederalTerritory
    ? ((await contentful.AccordionByInternalName({ name: NETZAUSBAU_FOERDER_FAQ_ACCORDION_MODULE, preview: isEnabled }))
        .accordionCollection?.items?.[0] as AccordionType)
    : null;

  const federalTeaser = isFederalTerritory
    ? ((
        await contentful.TextImageButtonModuleByInternalName({
          name: NETZAUSBAU_FOERDER_INFO_MODULE,
          preview: isEnabled,
        })
      )?.textImageButtonModuleCollection?.items?.[0] as TextImageButtonModule)
    : null;

  const additionalTeaserGrid = (
    await contentful.GridByName({
      internalName: isFederalTerritory ? NETZAUSBAU_FOERDER_INFO_GRID_MODULE : NETZAUSBAU_B2C_CONTENT_TEASER_MODULE,
      preview: isEnabled,
    })
  )?.gridModuleCollection?.items?.[0];

  const projectNews: React.ReactNode = await Promise.all(
    news.map(async (n, index) => {
      const internalName = n.title;
      const dgImageUrl = n.image_url ? await fetchDgCmsImage(process.env.DG_HOST_CMS + n.image_url) : undefined;
      const dgImageAsset = dgImageUrl
        ? {
            url: dgImageUrl,
            alt: n.title,
          }
        : undefined;
      const title = n.title;
      const text = n.description.replace(/(<([^>]+)>)/gi, '').replace(/&nbsp;/g, '');
      const image = parseDGCmsImage(dgImageAsset);
      const cta: CTA = { type: 'Link', label: n.button_text, to: n.button_link };
      return (
        <Teaser
          key={index}
          variant={'image'}
          internalName={internalName}
          headline={title}
          text={text}
          cta={cta}
          image={image}
        />
      );
    }),
  );

  return (
    <PageWrapper pageType="netzausbau" businessArea={businessArea} hasScrollToTop={true}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />

        <MainNavigation
          businessArea={businessArea}
          navigationItems={mainNavigation}
          initialNotification={generateProjectNotification(project)}
        />
      </PageHeader>

      <MainContent>
        <Breadcrumb breadcrumbs={breadcrumbs} />

        <ProjectStage
          {...parsedStage}
          project={{
            percentage,
            targetPercentage,
            hidePercentage,
            demandBundling: isDemandBundling(projectStatus.statusGroupCode),
            deadline,
            deadlineVisibleUntil,
            deadlineAlwaysVisible,
            dateModified,
            statusGroupCode: projectStatus.statusGroupCode,
            isFederalTerritory,
          }}
          isBusinessProjectStage={isBusinessProject(projectType)}
        />

        {pkTeaserGrid && isPrivateProject(projectType) && <BlockRenderer blocks={[pkTeaserGrid as GridModule]} />}

        {events && events.length > 0 && <ProjectEventList events={events} />}

        {projectStatus && (
          <ProjectPhases
            currentStatusGroupCode={projectStatus.statusGroupCode}
            isFederalTerritory={isFederalTerritory}
            description={projectStatus.descriptionGk}
          />
        )}

        <ProjectPromotionLogos logos={projectPromotionLogos} />

        {importantInfo && <ImportantInfo info={importantInfo} />}

        {news && news.length > 0 && (
          <Grid headline="Zusatzinformation" internalName="zusatzinformation">
            {projectNews}
          </Grid>
        )}

        {faqs && <BlockRenderer blocks={[faqs]} />}

        {federalTeaser && <BlockRenderer blocks={[federalTeaser]} />}

        {additionalTeaserGrid && <BlockRenderer blocks={[additionalTeaserGrid as GridModule]} />}

        {contactPersons.map((p: ProjectPartner) => (
          <PointOfContact key={p.name} {...p} internalName={NETZAUSBAU_PROJECT_POINTOFCONTACT_MODULE} />
        ))}

        <PartnerGroups project={project} />

        {videos && videos.length > 0 && <ProjectVideos videos={videos} headline="Videos" />}

        {downloads && downloads.length > 0 && (
          <Download internalName={name} headline={'Downloads'} headlineTag="h2" files={downloads} downloadSource="dg" />
        )}

        {sealbarData && (
          <SealBar
            internalName={sealbarData?.internalName ?? 'netzausbau-auszeichnungen'}
            headline={sealbarData?.headline ?? undefined}
            explanationText={(sealbarData?.explanationText as Maybe<SealBarModuleExplanationText>) ?? undefined}
            seals={(sealbarData?.sealsCollection?.items ?? []).map((seal) => {
              return {
                image: parseContentfulContentImage(seal?.image as Asset),
                link: seal?.link,
              } as SealProps;
            })}
          />
        )}
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={project.slug} />
    </PageWrapper>
  );
}
