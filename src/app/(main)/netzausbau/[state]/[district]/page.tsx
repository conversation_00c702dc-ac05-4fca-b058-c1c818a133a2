import { Suspense } from 'react';

import type { Metadata } from 'next';
import { draftMode } from 'next/headers';
import { notFound } from 'next/navigation';

import { BusinessArea, LOADING_DATA } from '@/app/consts';

import AreaSites from '@/modules/AreaSites';
import { fetchSeoDataByName } from '@/modules/AreaSites/contentfulData/fetchSeoDataByName';
import { SubregionOverview } from '@/modules/SubregionOverview';

import contentfulClient from '@/apis/contentful/api';
import type { DefaultStage } from '@/apis/contentful/generated/types';
import { fetchBreadcrumbs } from '@/apis/contentful/getBreadcrumbs';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import { parseDefaultStageData } from '@/apis/contentful/parser/stage';
import BlockRenderer, { type Block } from '@/apis/contentful/renderer/BlockRenderer';
import { getStates } from '@/apis/dg/states';

import Breadcrumb from '@/components/Breadcrumb';
import Copy from '@/components/Copy';
import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';
import type { DefaultStageProps } from '@/components/Stages/DefaultStage';
import DistrictStage from '@/components/Stages/DistrictStage';

import {
  FOOTER_SEALBAR_AWARDS_MODULE,
  NETZAUSBAU_CF_DISTRICT_PLACEHOLDER,
  NETZAUSBAU_DISTRICT_FAQ_MODULE,
  NETZAUSBAU_DISTRICT_GRID_MODULE,
  NETZAUSBAU_DISTRICT_TEXT_IMAGE_MODULE,
  NETZAUSBAU_LABEL,
  NETZAUSBAU_NETWORK_ACTIVE_STAGE_MODULE,
  NETZAUSBAU_SEO_DATA_DISTRICT_MODULE,
} from '@/utils/specialIds/specialIdsCatalog';

import districtImage from './district.svg';

export async function generateStaticParams(): Promise<{ state: string }[]> {
  const states = await getStates();

  if (!states) return [];

  const districts = (states ?? []).flatMap((federalState) =>
    (federalState.districts ?? []).map((d) => Object.assign({}, d, { state: federalState.slug })),
  );

  if (!districts) return [];

  return districts.map((s) => ({ state: s.state, district: s.slug }));
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const seoData = await fetchSeoDataByName(NETZAUSBAU_SEO_DATA_DISTRICT_MODULE);
  const states = await getStates();
  const state = states?.find((_) => _.slug === params.state);
  const district = state?.districts?.find((_) => _.slug === params.district);
  const canonical = `/netzausbau/${params.state}/${params.district}`;

  const seoFallback = 'Glasfaser in Landkreisen - Deutsche Glasfaser';

  if (!(seoData && district)) {
    // Fallback
    return {
      title: seoFallback,
      description: seoFallback,
      keywords: undefined,
      robots: undefined,
      alternates: {
        canonical,
      },
    };
  }

  const seoDistrictName = `Deutsche Glasfaser Netzausbau ${district.name}`;

  const title = seoData.seoTitle?.replaceAll(NETZAUSBAU_CF_DISTRICT_PLACEHOLDER, district.name) || seoDistrictName;
  const description =
    seoData.description?.replaceAll(NETZAUSBAU_CF_DISTRICT_PLACEHOLDER, district.name) || seoDistrictName;

  return {
    title,
    description,
    keywords: seoData.keywords?.filter(Boolean) as string[],
    robots: {
      // Note: network active (Netz aktiv) pages are automatically removed from the sitemap. see /src/app/sitemap.ts
      index: !seoData.noIndex,
      follow: !seoData.noFollow,
    },
    alternates: {
      canonical,
    },
  };
}

export default async function Page({ params }: DistrictPageProps) {
  const { isEnabled: preview } = draftMode();

  const states = await getStates();
  const state = states?.find((_) => _.slug === params.state);
  const district = state?.districts?.find((_) => _.slug === params.district);

  if (!states || !state || !district) {
    return notFound();
  }

  const businessArea = BusinessArea.Private;
  const pathname = `/netzausbau/${params.state}/${params.district}`;
  const mainNavigation = await fetchMainNavigation({ businessArea });
  const breadcrumbs = await fetchBreadcrumbs({ pathname, labels: [NETZAUSBAU_LABEL, state.name, district.name] });
  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });
  const contentful = contentfulClient({ preview });
  const blocks: Block[] = [
    (await contentful.GridByName({ internalName: NETZAUSBAU_DISTRICT_GRID_MODULE, preview }))?.gridModuleCollection
      ?.items[0],
    (await contentful.TextImageButtonModuleByInternalName({ name: NETZAUSBAU_DISTRICT_TEXT_IMAGE_MODULE, preview }))
      ?.textImageButtonModuleCollection?.items[0],
    (await contentful.AccordionByInternalName({ name: NETZAUSBAU_DISTRICT_FAQ_MODULE, preview }))?.accordionCollection
      ?.items[0],
    (await contentful.SealBarModuleByInternalName({ name: FOOTER_SEALBAR_AWARDS_MODULE, preview }))
      ?.sealBarModuleCollection?.items[0],
  ].filter(Boolean) as Block[];

  const defaultStage: DefaultStageProps = {
    internalName: 'netzausbau-landkreis-stage',
    headline: `Glasfaser-Ausbau in ${district.name}`,
    text: 'Informieren Sie sich hier über unsere Ausbau-Projekte in Ihrer Region.',
    mediaDesktop: {
      src: districtImage, // fallback if no activeProjectStage image
      alt: '',
      width: 1200,
      height: 1000,
      contentType: 'image/svg+xml',
    },
    mediaMobile: {
      src: districtImage, // fallback if no activeProjectStage image
      alt: '',
      width: 600,
      height: 400,
      contentType: 'image/svg+xml',
    },
    layout: 'split',
  };

  const activeProjectStage = (
    await contentful.DefaultStageByName({ name: NETZAUSBAU_NETWORK_ACTIVE_STAGE_MODULE, preview })
  )?.defaultStageCollection?.items[0] as DefaultStage;

  const parsedActiveProjectStage = parseDefaultStageData(activeProjectStage) ?? undefined;

  return (
    <PageWrapper pageType="netzausbau" businessArea={businessArea} hasScrollToTop={true}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />
        <MainNavigation businessArea={businessArea} navigationItems={mainNavigation} />
      </PageHeader>

      <MainContent>
        <Breadcrumb breadcrumbs={breadcrumbs} />

        <Suspense fallback={<Copy className="p-4 lg:p-8">{LOADING_DATA}</Copy>}>
          <DistrictStage defaultStage={defaultStage} activeProjectStage={parsedActiveProjectStage} />
        </Suspense>

        <AreaSites
          internalName="netzausbau-landkreis"
          headline="Ihre Region, unsere Projekte:"
          headlineTag="h2"
          presetState={{ label: state.name, value: state.slug }}
          presetDistrict={district}
        />

        <SubregionOverview
          internalName="netzausbau-landkreis-uebersicht"
          headline="Ihre Übersicht nach Kreisen"
          subregions={state.districts}
          regionSlug={state.slug}
        />

        <BlockRenderer blocks={blocks} />
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={state.slug} />
    </PageWrapper>
  );
}
