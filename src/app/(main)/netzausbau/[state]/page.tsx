import type { Metadata } from 'next';
import { draftMode } from 'next/headers';
import { notFound } from 'next/navigation';

import { BusinessArea } from '@/app/consts';

import AreaSites from '@/modules/AreaSites';
import { fetchSeoDataByName } from '@/modules/AreaSites/contentfulData/fetchSeoDataByName';
import { SubregionOverview } from '@/modules/SubregionOverview';

import contentfulClient from '@/apis/contentful/api';
import { fetchBreadcrumbs } from '@/apis/contentful/getBreadcrumbs';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import BlockRenderer, { type Block } from '@/apis/contentful/renderer/BlockRenderer';
import { getPresetProjects } from '@/apis/dg/projects';
import { getStates } from '@/apis/dg/states';

import Breadcrumb from '@/components/Breadcrumb';
import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';
import DefaultStage, { type DefaultStageProps } from '@/components/Stages/DefaultStage';

import {
  FOOTER_SEALBAR_AWARDS_MODULE,
  NETZAUSBAU_CF_STATE_PLACEHOLDER,
  NETZAUSBAU_LABEL,
  NETZAUSBAU_SEO_DATA_STATE_MODULE,
  NETZAUSBAU_STATE_FAQ_MODULE,
  NETZAUSBAU_STATE_TEXT_IMAGE_MODULE,
} from '@/utils/specialIds/specialIdsCatalog';

export async function generateStaticParams(): Promise<{ state: string }[]> {
  const states = await getStates();

  if (!states) return [];

  return states.map((s) => ({ state: s.slug }));
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const seoData = await fetchSeoDataByName(NETZAUSBAU_SEO_DATA_STATE_MODULE);
  const states = await getStates();
  const state = states?.find((_) => _.slug === params.state);
  const canonical = `/netzausbau/${params.state}`;

  const seoFallback = 'Glasfaser in Bundesländern - Deutsche Glasfaser';

  if (!(seoData && state)) {
    // Fallback
    return {
      title: seoFallback,
      description: seoFallback,
      keywords: undefined,
      robots: undefined,
      alternates: {
        canonical,
      },
    };
  }

  const seoStateName = `Deutsche Glasfaser Netzausbau ${state.name}`;

  const title = seoData.seoTitle?.replaceAll(NETZAUSBAU_CF_STATE_PLACEHOLDER, state.name) || seoStateName;
  const description = seoData.description?.replaceAll(NETZAUSBAU_CF_STATE_PLACEHOLDER, state.name) || seoStateName;

  return {
    title,
    description,
    keywords: seoData.keywords?.filter(Boolean) as string[],
    robots: {
      // Note: network active (Netz aktiv) pages are automatically removed from the sitemap. see /src/app/sitemap.ts
      index: !seoData.noIndex,
      follow: !seoData.noFollow,
    },
    alternates: {
      canonical,
    },
  };
}

export default async function Page({ params }: StatePageProps) {
  const { isEnabled: preview } = draftMode();

  const states = await getStates();
  const state = states?.find((_) => _.slug === params.state);

  if (!states || !state) {
    return notFound();
  }

  const businessArea = BusinessArea.Private;
  const mainNavigation = await fetchMainNavigation({ businessArea });
  const pathname = `/netzausbau/${params.state}`;
  const breadcrumbs = await fetchBreadcrumbs({ pathname, labels: [NETZAUSBAU_LABEL, state.name] });
  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });
  const contentful = contentfulClient({ preview });
  const region = (await contentful.RegionByName({ name: state.name, preview })).regionCollection?.items[0] ?? null;
  const blocks: Block[] = [
    (await contentful.TextImageButtonModuleByInternalName({ name: NETZAUSBAU_STATE_TEXT_IMAGE_MODULE, preview }))
      ?.textImageButtonModuleCollection?.items[0],
    (await contentful.AccordionByInternalName({ name: NETZAUSBAU_STATE_FAQ_MODULE, preview }))?.accordionCollection
      ?.items[0],
    (await contentful.SealBarModuleByInternalName({ name: FOOTER_SEALBAR_AWARDS_MODULE, preview }))
      ?.sealBarModuleCollection?.items[0],
  ].filter(Boolean) as Block[];

  const stage: DefaultStageProps = {
    internalName: 'netzausbau-bundesland-stage',
    layout: 'split',
    headline: state?.name ?? 'Bundesland',
    text: region?.text ?? '',
    mediaDesktop: {
      src: region?.image?.url ?? '',
      alt: '',
      width: 1200,
      height: 1000,
      contentType: 'image/jpeg',
    },
    mediaMobile: {
      src: region?.image?.url ?? '',
      alt: '',
      width: 600,
      height: 400,
      contentType: 'image/jpeg',
    },
  };

  const presetProjects = await getPresetProjects(state.slug);

  return (
    <PageWrapper pageType="netzausbau" businessArea={businessArea} hasScrollToTop={true}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />
        <MainNavigation businessArea={businessArea} navigationItems={mainNavigation} />
      </PageHeader>

      <MainContent>
        <Breadcrumb breadcrumbs={breadcrumbs} />

        <DefaultStage {...stage} />

        <AreaSites
          internalName="netzausbau-regionen"
          headline="Ihre Region, unsere Projekte:"
          headlineTag="h2"
          presetProjects={presetProjects ?? []}
          presetState={{ label: state.name, value: state.slug }}
        />

        <SubregionOverview
          internalName="netzausbau-regionen-uebersicht"
          headline="Ihre Übersicht nach Kreisen"
          subregions={state.districts}
          regionSlug={state.slug}
        />

        <BlockRenderer blocks={blocks} />
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={state.slug} />
    </PageWrapper>
  );
}
