import { draftMode } from 'next/headers';

import { BusinessArea } from '@/app/consts';

import SealBar from '@/modules/SealBar';
import type { SealProps } from '@/modules/SealBar/Seal';

import contentfulClient from '@/apis/contentful/api';
import type { Asset, Maybe, SealBarModuleExplanationText } from '@/apis/contentful/generated/types';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import { fetchDefaultStageById } from '@/apis/contentful/getStages';
import { parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import BlockRenderer, { type Block } from '@/apis/contentful/renderer/BlockRenderer';

import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import SearchBar from '@/components/MainNavigation/SearchBar';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';
import DefaultStage from '@/components/Stages/DefaultStage';

export default async function NotFound() {
  const { isEnabled } = draftMode();
  const slug = '404';
  const businessArea = BusinessArea.Private;

  const mainNavigation = await fetchMainNavigation({ businessArea });
  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });

  const contentful = contentfulClient({ preview: isEnabled });
  const data = await contentful.GridByName({ internalName: 'Schnellnavigation', preview: isEnabled });
  const stage = await fetchDefaultStageById({ id: '1SssgrTp1oI3nBsALlV31J' });
  const blocks = [data.gridModuleCollection?.items?.[0]];

  const sealbarData = (await contentful.SealBarModuleByInternalName({ name: 'Siegeleiste PK', preview: isEnabled }))
    ?.sealBarModuleCollection?.items[0];

  if (!mainNavigation || !socialMediaItems || !linkBlocks) {
    return (
      <PageWrapper pageType="not-found" businessArea={businessArea}>
        <main id="main" className="container mx-auto px-2 text-center">
          <div className="flex h-screen flex-col items-center justify-center">
            <h1 className="mb-4 font-soehne-condensed text-4xl leading-[90%] font-normal hyphens-auto uppercase md:text-[3.75rem]">
              Seite nicht gefunden
            </h1>
            <p>
              Leider konnte die von Ihnen aufgerufene Seite nicht gefunden werden.
              <br />
              Möglicherweise haben sie sich vertippt oder die Seite existiert nicht mehr.
            </p>
          </div>
        </main>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper pageType={'404'} businessArea={businessArea}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />
        <MainNavigation businessArea={businessArea} navigationItems={mainNavigation} />
      </PageHeader>

      <MainContent>
        {stage && <DefaultStage {...stage} />}
        <SearchBar type={'module'} />
        <BlockRenderer blocks={blocks as Block[]} />
        {sealbarData && (
          <SealBar
            internalName={sealbarData?.internalName ?? 'netzausbau-auszeichnungen'}
            headline={sealbarData?.headline ?? undefined}
            explanationText={(sealbarData?.explanationText as Maybe<SealBarModuleExplanationText>) ?? undefined}
            seals={(sealbarData?.sealsCollection?.items ?? []).map((seal) => {
              return {
                image: parseContentfulContentImage(seal?.image as Asset),
                link: seal?.link,
              } as SealProps;
            })}
          />
        )}
      </MainContent>
      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={slug} />
    </PageWrapper>
  );
}
