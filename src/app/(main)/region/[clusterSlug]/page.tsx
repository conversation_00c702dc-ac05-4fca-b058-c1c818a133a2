import type { Metadata } from 'next';
import { draftMode } from 'next/headers';
import { notFound } from 'next/navigation';

import stageImageGk from '@/assets/netzausbau-stage-gk.jpg';
import stageImagePk from '@/assets/netzausbau-stage-pk.jpg';

import { BusinessArea } from '@/app/consts';

import ProjectField from '@/modules/AreaSites/AreaSearchClient/ProjectField/ProjectField';
import ImportantInfo from '@/modules/ImportantInformation';

import contentfulClient from '@/apis/contentful/api';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import BlockRenderer, { type Block } from '@/apis/contentful/renderer/BlockRenderer';
import { getCluster, getClusterSlugs } from '@/apis/dg/cluster';

import Footer from '@/components/Footer';
import Headline from '@/components/Headline';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';
import DefaultStage from '@/components/Stages/DefaultStage';
import type { DefaultStageProps } from '@/components/Stages/DefaultStage';

export async function generateStaticParams(): Promise<{ clusterSlug: string }[]> {
  const slugs = await getClusterSlugs();

  if (!slugs) return [];

  return slugs.map((slug) => ({ clusterSlug: slug }));
}

export async function generateMetadata({ params }: ClusterPageProps): Promise<Metadata> {
  const cluster = await getCluster(params.clusterSlug);

  if (!cluster || !cluster.slug) {
    return notFound();
  }

  return {
    title: `Deutsche Glasfaser Cluster ${cluster.name}`,
    description: `Deutsche Glasfaser Cluster ${cluster.name}`,
    keywords: undefined,
    // Cluster pages should not appear in SERPS, hence they should keep nofollow and noindex
    robots: {
      index: false,
      follow: false,
    },
    alternates: {
      canonical: `/region/${params.clusterSlug}`,
    },
  };
}

export default async function Page({ params }: ClusterPageProps) {
  const { isEnabled: preview } = draftMode();

  const cluster = await getCluster(params.clusterSlug);

  if (!cluster || !cluster.slug) {
    return notFound();
  }

  const businessArea = BusinessArea.Private;
  const mainNavigation = await fetchMainNavigation({ businessArea });
  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });
  const contentful = contentfulClient({ preview });
  const blocks: Block[] = [
    (await contentful.GridByName({ internalName: 'netzausbau-cluster-grid-vorteile', preview }))?.gridModuleCollection
      ?.items[0],
    (await contentful.TextImageButtonModuleByInternalName({ name: 'netzausbau-cluster-text-bild-teaser', preview }))
      ?.textImageButtonModuleCollection?.items[0],
    (await contentful.TextButtonModuleByInternalName({ name: 'netzausbau-cluster-text-teaser', preview }))
      ?.textButtonModuleCollection?.items[0],
  ].filter(Boolean) as Block[];

  const stage: DefaultStageProps = {
    internalName: 'netzausbau-cluster-stage',
    layout: 'split',
    headline: cluster.headline,
    mediaDesktop: {
      src: cluster.image ? cluster.image : cluster.projectType === 'private' ? stageImagePk.src : stageImageGk.src,
      alt: cluster.name ?? '',
      width: 1200,
      height: 1000,
      contentType: 'image/jpeg',
    },
    mediaMobile: {
      src: cluster.image ? cluster.image : cluster.projectType === 'private' ? stageImagePk.src : stageImageGk.src,
      alt: cluster.name ?? '',
      width: 600,
      height: 400,
      contentType: 'image/jpeg',
    },
  };

  return (
    <PageWrapper pageType="region" businessArea={businessArea} hasScrollToTop={true}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />
        <MainNavigation businessArea={businessArea} navigationItems={mainNavigation} />
      </PageHeader>

      <MainContent>
        <DefaultStage {...stage} />

        <Headline intent={'h2'} className="px-4 pb-12 text-center md:px-8 xl:px-24 2xl:px-48">
          Ihr Anschluss an die Zukunft.
          <br />
          Eine Übersicht unserer Projekte.
        </Headline>

        <div className="grid w-full grid-cols-1 gap-6 px-4 py-12 md:px-8 lg:grid-cols-2 xl:px-24 2xl:px-48">
          {cluster.projects.map((project, index) => (
            <ProjectField key={index} project={project} />
          ))}
        </div>

        {cluster.importantInfo && <ImportantInfo info={cluster.importantInfo} />}

        <BlockRenderer blocks={blocks} />
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={cluster.slug} />
    </PageWrapper>
  );
}
