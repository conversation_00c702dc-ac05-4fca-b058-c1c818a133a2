'use client';

import { useEffect } from 'react';

import { BusinessArea } from '@/app/consts';

import PageError from '@/components/PageError';

export default function Error({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    // Optionally log the error to an error reporting service
    console.error('Error during client-side navigation Jobportal:', error);
  }, [error]);

  return <PageError businessArea={BusinessArea.Companies} />;
}
