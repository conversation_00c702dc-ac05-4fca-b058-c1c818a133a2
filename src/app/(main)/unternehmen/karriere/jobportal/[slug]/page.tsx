import type { Metada<PERSON> } from 'next';
import { notFound } from 'next/navigation';

import { BusinessArea } from '@/app/consts';

import ApplicationProcess from '@/modules/JobApplicationProcess';
import JobDetails from '@/modules/JobDetails';
import JobportalVideo from '@/modules/JobportalVideo';
import MoreJobs from '@/modules/MoreJobs';

import { fetchBreadcrumbs } from '@/apis/contentful/getBreadcrumbs';
import { getFooterData } from '@/apis/contentful/getFooter';
import { fetchMainNavigation } from '@/apis/contentful/getMainNavigation';
import { getJob, getJobDetailPath } from '@/apis/smartrecruiters';

import Breadcrumb from '@/components/Breadcrumb';
import { getSanitizedBreadcrumb } from '@/components/Breadcrumb/utils/getSanitizedBreadcrumb';
import Footer from '@/components/Footer';
import MainContent from '@/components/MainContent';
import MainNavigation from '@/components/MainNavigation';
import MetaNavigation from '@/components/MetaNavigation';
import PageHeader from '@/components/PageHeader';
import PageWrapper from '@/components/PageWrapper';
import { SkipLinks } from '@/components/SkipLinks';

import type { PageProps } from '@/types/pageProps';

import { getPathSegments } from '@/utils/routing/getPathSegments';

type DetailPageProps = PageProps & { params: { slug: string } };

const businessArea = BusinessArea.Companies;
const pageType = 'Content';

export async function generateMetadata({ params }: DetailPageProps): Promise<Metadata> {
  // Slug must be prefixed by the job posting id, separated by a hyphen
  const jobPostingId = params.slug.split('-')[0];
  const job = await getJob(jobPostingId);
  if (!job) return {};

  const canonical = getJobDetailPath(job);
  return {
    title: job.seoTitle,
    description: job.seoDescription,
    keywords: job.seoKeywords,
    robots: {
      index: true,
      follow: true,
    },
    alternates: {
      canonical,
    },
  };
}

export default async function Page({ params }: DetailPageProps) {
  // Slug must be prefixed by the job posting id, separated by a hyphen
  const jobPostingId = params.slug.split('-')[0];
  const job = await getJob(jobPostingId);
  if (job === null) return notFound();

  const mainNavigation = await fetchMainNavigation({ businessArea });
  const pathname = getJobDetailPath(job);
  const breadcrumbLabels = getPathSegments(pathname);
  breadcrumbLabels[breadcrumbLabels.length - 1] = getSanitizedBreadcrumb(job.name);
  const breadcrumbs = await fetchBreadcrumbs({ pathname, labels: breadcrumbLabels });
  const { socialMediaItems, linkBlocks } = await getFooterData({ businessArea: businessArea });

  return (
    <PageWrapper businessArea={businessArea} pageType={pageType} hasScrollToTop={true}>
      <SkipLinks />
      <PageHeader>
        <MetaNavigation businessArea={businessArea} />
        <MainNavigation businessArea={businessArea} navigationItems={mainNavigation} />
      </PageHeader>

      <MainContent>
        {/* Hide breadcrumb on mobile */}
        <div className="hidden lg:block">
          <Breadcrumb breadcrumbs={breadcrumbs} />
        </div>
        <JobDetails internalName="job-details" job={job} />

        <JobportalVideo />
        <ApplicationProcess />
        <MoreJobs currentJob={job} />
      </MainContent>

      <Footer socialMedia={socialMediaItems} linkBlocks={linkBlocks} segment={pathname} />
    </PageWrapper>
  );
}
