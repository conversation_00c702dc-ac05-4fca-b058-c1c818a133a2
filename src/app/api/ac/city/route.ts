import { getCity } from '@/apis/dg/availablityCheck';

import { cityData } from '@/mock/availability/mockedCityData';

export async function GET(request: Request) {
  const query = new URL(request.url).searchParams.get('query');

  if (!query) {
    return Response.json({ message: 'No query provided' });
  }

  if (process.env.DG_MOCK === 'false') {
    const result = await getCity(query);
    return Response.json(result);
  }

  return Response.json(cityData);
}
