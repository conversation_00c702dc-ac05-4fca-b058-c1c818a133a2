import { getStreetNr } from '@/apis/dg/availablityCheck';

import { streetNrData } from '@/mock/availability/mockedStreetNrData';

export async function GET(request: Request) {
  const street_id = new URL(request.url).searchParams.get('street_id');

  if (!street_id) {
    return Response.json({ message: 'No street_id provided' });
  }

  if (process.env.DG_MOCK === 'false') {
    return Response.json(await getStreetNr(street_id));
  }

  return Response.json(streetNrData);
}
