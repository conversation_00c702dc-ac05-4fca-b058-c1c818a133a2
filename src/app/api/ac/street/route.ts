import { getStreet } from '@/apis/dg/availablityCheck';

import { streetData } from '@/mock/availability/mockedStreetData';

export async function GET(request: Request) {
  const searchParams = new URL(request.url).searchParams;
  const zipcode = searchParams.get('zipcode');
  const city_id = searchParams.get('city_id');

  if (!zipcode || !city_id) {
    return Response.json({ message: 'No zipcode and/or city_id provided' });
  }

  if (process.env.DG_MOCK === 'false') {
    return Response.json(await getStreet(zipcode, city_id));
  }

  return Response.json(streetData);
}
