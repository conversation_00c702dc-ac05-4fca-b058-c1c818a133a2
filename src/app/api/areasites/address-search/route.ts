import type { NextRequest } from 'next/server';

import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

export async function GET(request: NextRequest) {
  const term = request.nextUrl.searchParams.get('term');

  if (term) {
    const data = await dgClient(Host.CMS)
      .get(ApiType.AREASITES + Endpoint.ADDRESS_SEARCH + '?term=' + request.nextUrl.searchParams.get('term'))
      .res((_) => _.json());

    return Response.json(data);
  } else {
    return Response.json({ error: 'No search term provided' });
  }
}
