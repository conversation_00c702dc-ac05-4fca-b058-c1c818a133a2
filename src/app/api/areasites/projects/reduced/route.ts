import type { NextRequest } from 'next/server';

import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

export async function GET(request: NextRequest) {
  const params: string[][] = [
    'gkey',
    'offset',
    'project_type',
    'is_federal_territory',
    'status',
    'federal_state',
    'slug_district',
    'limit',
  ]
    .map((key) => {
      const value = request.nextUrl.searchParams.get(key);
      return [key, value ?? ''];
    })
    .filter(([, value]) => value !== '');

  try {
    const data = await dgClient(Host.CMS)
      .get(`${ApiType.AREASITES + Endpoint.PROJECTS_REDUCED}/?small=true&${new URLSearchParams(params).toString()}`)
      .res((_) => _.json());

    return Response.json(data);
  } catch (e) {
    return Response.json({ error: e });
  }
}
