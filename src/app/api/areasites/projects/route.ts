import type { NextRequest } from 'next/server';

import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

export async function GET(request: NextRequest) {
  const defaultParams = {
    radius: '10000',
  };

  const params: string[][] = [
    'gkey',
    'offset',
    'project_type',
    'is_federal_territory',
    'status',
    'slug_state',
    'slug_district',
    'limit',
    'small',
    'slug',
    'limit',
    'radius',
  ]
    .map((key) => {
      const value = request.nextUrl.searchParams.get(key) ?? defaultParams[key as keyof typeof defaultParams] ?? '';
      return [key, value];
    })
    .filter(([, value]) => value !== '');

  const data = await dgClient(Host.CMS)
    .get(`${ApiType.AREASITES + Endpoint.PROJECTS}/?${new URLSearchParams(params).toString()}`)
    .res((_) => _.json());

  return Response.json(data);
}
