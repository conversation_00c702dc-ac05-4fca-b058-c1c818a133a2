import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

export async function GET() {
  try {
    const data = await dgClient(Host.CMS)
      .get(`${ApiType.AREASITES + Endpoint.STATES}`)
      .res((_) => _.json());

    if (!data)
      return new Response(JSON.stringify({ error: 'No data found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });

    return Response.json(data);
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: 'An error occurred while fetching data' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
