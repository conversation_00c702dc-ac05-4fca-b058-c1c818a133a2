import { NextResponse } from 'next/server';

import { type DialfireContactPayload, createDialfireContact } from '@/apis/dialfire/api';

/**
 * API route to create a Dialfire contact
 */
export async function POST(request: Request) {
  try {
    const payload: DialfireContactPayload = await request.json();

    const response = await createDialfireContact(payload);

    if ('error' in response) {
      return NextResponse.json({ success: false, message: response.error }, { status: 400 });
    }

    return NextResponse.json({ success: true }, { status: 201 });
  } catch (error) {
    console.error('Failed to create Dialfire contact:', error);

    return NextResponse.json({ success: false, message: 'Failed to process request' }, { status: 500 });
  }
}
