import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

export async function GET(request: Request) {
  const path = new URL(request.url).searchParams.get('path');

  if (!path) {
    return Response.json({ message: 'path is missing!' });
  }

  try {
    const filename = path.split('/').pop();
    const data = dgClient(Host.NEW).get(`${ApiType.SHOP2}${Endpoint.DOWNLOAD}?path=${path}`);

    if (data) {
      return new Response(await data.blob(), {
        status: 200,
        headers: new Headers({
          'content-disposition': `attachment; filename=${filename}`,
        }),
      });
    }
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: 'An error occurred while fetching data' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
