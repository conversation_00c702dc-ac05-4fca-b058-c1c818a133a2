import { getLegalNote } from '@/apis/dg/legalNote';

import {
  loremIpsumData,
  mockedLegalNoteDGB100Data,
  mockedLegalNoteDGB300Data,
  mockedLegalNoteDGB500Data,
  mockedLegalNoteDGB1000Data,
} from '@/mock/mockedLegalNoteData';

export async function GET(request: Request) {
  const id = new URL(request.url).searchParams.get('id');

  if (!id) {
    return Response.json({ message: 'ID is missing!' });
  }

  if (process.env.DG_MOCK === 'false') {
    return Response.json(await getLegalNote(Number(id)));
  }

  if (id === '102') {
    return Response.json(mockedLegalNoteDGB100Data);
  }

  if (id === '11') {
    return Response.json(mockedLegalNoteDGB300Data);
  }

  if (id === '101') {
    return Response.json(mockedLegalNoteDGB500Data);
  }

  if (id === '100') {
    return Response.json(mockedLegalNoteDGB1000Data);
  }

  return Response.json(loremIpsumData);
}
