import type { NextApiRequest } from 'next';
import { NextResponse } from 'next/server';

import { dgClient } from '@/apis/dg/api';
import { ApiType, Endpoint, Host } from '@/apis/dg/consts';

type DownloadQuery = {
  params: {
    path: string;
    type: string;
  };
};

export async function GET(req: NextApiRequest, query: DownloadQuery) {
  try {
    const filename = query.params.path.replaceAll('%2F', '/');
    let data;

    if (query.params.type === 'areasites') {
      data = dgClient(Host.CMS).get(`${ApiType.MEDIA}${Endpoint.AREASITES}/${filename}`);
    }

    if (data) {
      return new NextResponse(await data.blob(), {
        status: 200,
        headers: new Headers({
          'content-disposition': `attachment; filename=${filename}`,
        }),
      });
    }

    return new NextResponse(null, {
      status: 404,
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: 'An error occurred while fetching data' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
