import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';

import { fetchPage } from '@/apis/contentful/getPages';
import { getCluster } from '@/apis/dg/cluster';
import { getProject } from '@/apis/dg/projects';

import { getAbsolutePath } from '@/utils/routing/getAbsolutePath';
import { getLastPathSegment } from '@/utils/routing/getPathSegments';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const secret = searchParams.get('secret');
  const path = searchParams.get('slug') ?? '/';

  if (secret !== process.env.CONTENTFUL_PREVIEW_SECRET) {
    return new Response('Invalid token', { status: 401 });
  }

  let page;

  if (path.startsWith('/netzausbau')) {
    const slug = getLastPathSegment(path);
    page = await getProject({ project: slug, preview: true });
  } else if (path.startsWith('/region')) {
    const slug = getLastPathSegment(path);
    page = await getCluster(slug);
  } else {
    page = await fetchPage({ slug: path, preview: true });
  }

  if (!page) {
    return new Response('Invalid slug', { status: 401 });
  }

  draftMode().enable();
  redirect(getAbsolutePath(`${path}`));
}
