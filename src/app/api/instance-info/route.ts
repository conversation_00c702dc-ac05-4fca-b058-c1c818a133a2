import wretch from 'wretch';

const REDIS_CACHE_TEST_ROUTE_ENABLED = (process.env.REDIS_CACHE_TEST_ROUTE_ENABLED ?? 'false') === 'true';

export async function GET(request: Request) {
  let instanceId = 'unknown';
  let ip = 'unknown';

  if (REDIS_CACHE_TEST_ROUTE_ENABLED) {
    try {
      instanceId = await wretch('http://metadata.google.internal/computeMetadata/v1/instance/id', {
        headers: { 'Metadata-Flavor': 'Google' },
        cache: 'no-store',
      })
        .get()
        .text();
    } catch (ex) {}

    try {
      ip = await wretch('http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/ip', {
        headers: { 'Metadata-Flavor': 'Google' },
        cache: 'no-store',
      })
        .get()
        .text();
    } catch (ex) {}
  }

  return Response.json({ ip, instanceId });
}
