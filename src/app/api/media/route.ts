import { NextResponse } from 'next/server';

import { decodeString } from '@/utils/customEncoder';

export const DOWNLOAD_MEDIA_PREFIX = 'assets.ctfassets.net';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const file = searchParams.get('file');

  if (!file) {
    return NextResponse.json({ error: 'File URL parameter of media request is missing' }, { status: 400 });
  }

  try {
    const url = decodeString(file);
    const response = await fetch(url);

    if (!response.ok) {
      return new Response(null, { status: response.status });
    }

    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    const buffer = await response.arrayBuffer();

    return new Response(buffer, {
      headers: {
        'Content-Type': contentType,
      },
    });
  } catch (error) {
    console.error('<PERSON><PERSON> beim Abrufen der Datei:', error);

    return new Response(null, {
      status: 302,
      headers: {
        Location: '/404',
      },
    });
  }
}
