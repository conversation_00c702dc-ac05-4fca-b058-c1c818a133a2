import { revalidatePath } from 'next/cache';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const contentfulUrl = 'https://cdn.contentful.com';
const spaceId = process.env.CONTENTFUL_SPACE_ID;
const environment = process.env.CONTENTFUL_ENVIRONMENT;
const accessToken = process.env.CONTENTFUL_DELIVERY_TOKEN;

export async function POST(request: NextRequest) {
  const pathParams = request.nextUrl.searchParams.get('path');

  if (pathParams) {
    const requestHeaders = new Headers(request.headers);
    const secret = requestHeaders.get('x-vercel-reval-key');

    if (secret !== process.env.CONTENTFUL_REVALIDATE_SECRET) {
      return NextResponse.json({ message: 'Invalid secret' }, { status: 401 });
    }

    try {
      const path = decodeURI(pathParams);

      // check if path is valid
      if (!path.startsWith('/')) {
        return NextResponse.json({ message: 'Invalid Path' }, { status: 404 });
      }

      console.log('Attempting to revalidate DG path:', path);
      revalidatePath(path);

      return NextResponse.json({
        revalidated: true,
        message: `Revalidated path: ${path}`,
        now: Date.now(),
        'Content-Type': 'application/json',
      });
    } catch (e) {
      // Catches a malformed URI
      console.error('Cannot decode path:', e);
    }
  }

  // coming from Contentful
  try {
    const body = await request.json();

    if (body?.sys) {
      if (body?.fields?.slug) {
        const path = body.fields.slug?.de;

        console.log('Attempting to revalidate CF path:', path);
        revalidatePath(path);

        return NextResponse.json({
          revalidated: true,
          message: `Revalidated path: ${path}`,
          now: Date.now(),
          'Content-Type': 'application/json',
        });
      } else if (body?.sys?.id) {
        // get all entries that link to this entry recursively
        const paths = await revalidate(body.sys.id);

        if (paths.length === 0) {
          return NextResponse.json({
            revalidated: false,
            now: Date.now(),
            message: 'No paths to revalidate',
          });
        }

        return NextResponse.json({
          revalidated: true,
          message: `Revalidated paths: ${paths.join(', ')}`,
          now: Date.now(),
          'Content-Type': 'application/json',
        });
      }
    }
  } catch (error) {
    console.error('Cannot parse body:', error);
  }

  return NextResponse.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing path or entry id to revalidate',
  });
}

async function revalidate(entryId: string): Promise<string[]> {
  const paths: string[] = [];
  const url = `${contentfulUrl}/spaces/${spaceId}/environments/${environment}/entries?access_token=${accessToken}&links_to_entry=${entryId}`;
  try {
    const response = await fetch(url);

    checkData(response, entryId);

    const data = await response.json();

    if (data.items.length === 0) return paths;

    await Promise.all(
      data.items.map(async (item: any) => {
        if (item?.fields?.slug) {
          console.log('Attempting to revalidate CF path:', item.fields.slug);
          revalidatePath(item.fields.slug);
          paths.push(item.fields.slug);
        } else if (item?.sys?.id) {
          const path = await revalidate(item.sys.id);
          paths.push(...path);
        }
      }),
    );
  } catch (error) {
    console.error('Error:', error);
  }

  return paths;
}

function checkData(data: any, id: string) {
  if (!data) throw Error('Cannot fetch links to entry: ' + id);
}
