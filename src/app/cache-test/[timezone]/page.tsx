import Link from 'next/link';
import { notFound } from 'next/navigation';

import wretch from 'wretch';

import { RevalidateFrom } from '../revalidate-form';
import { revalidateTagTimeData } from '../server-actions';

export const dynamic = 'force-dynamic';

type TimeData = {
  time: string;
  timezone: string;
};

const timeZones = ['cet', 'gmt'];

export async function generateStaticParams() {
  return timeZones.map((timezone) => ({ timezone }));
}

export type PageParams = {
  timezone: string;
};

export type PageProps = {
  params: PageParams;
};

type InstanceInfo = {
  instanceId: string;
  ip: string;
};

export default async function Page({ params }: PageProps) {
  if (params.timezone == 'revalidate') {
    await revalidateTagTimeData();
    return <>REVALIDATE PATH / TAG</>;
  }

  const data = await fetch(
    `https://q3df.org/api/time?zone=${encodeURIComponent(params.timezone)}&key=sdfg2f2efevsdSDF33&bla=0`,
    {
      next: { tags: ['time-data'] },
    },
  );

  const dataNoCache = await fetch(
    `https://q3df.org/api/time?zone=${encodeURIComponent(params.timezone)}&key=sdfg2f2efevsdSDF33&bla=1`,
    {
      cache: 'no-store',
    },
  );

  const dataRevalidate = await fetch(
    `https://q3df.org/api/time?zone=${encodeURIComponent(params.timezone)}&key=sdfg2f2efevsdSDF33&bla=2`,
    {
      next: { revalidate: 3 },
    },
  );

  let instanceInfo: InstanceInfo = { ip: '127.0.0.1', instanceId: 'buildtime' };
  try {
    instanceInfo = await wretch(process.env.NEXT_PUBLIC_SITE_URL + '/api/instance-info', { cache: 'no-store' })
      .auth('Basic ZWNvbTpyZWxhdW5jaA==')
      .get()
      .json();
  } catch (ex) {}

  if (!data.ok) {
    notFound();
  }

  const timeData: TimeData = await data.json();
  const timeDataNoCache: TimeData = await dataNoCache.json();
  const timeDataRevalidate: TimeData = await dataRevalidate.json();

  return (
    <>
      <header className="header">
        <h1>Cache Testing</h1>
        <ul>
          {timeZones.map((timeZone) => (
            <li key={timeZone}>
              <Link className="link" href={`/cache-test/${timeZone}`}>
                {timeZone.toUpperCase()} Time
              </Link>
            </li>
          ))}
        </ul>
      </header>
      <main className="widget">
        <div className="pre-rendered-at">
          <hr />
          <h3>Cached (by tag)</h3>
          <strong>Timezone:</strong> {timeData.timezone}
          <br />
          <strong>Time:</strong> {timeData.time}
          <br />
          <hr />
          <h3>Cached (revalidate 3000)</h3>
          <strong>Timezone:</strong> {timeDataRevalidate.timezone}
          <br />
          <strong>Time:</strong> {timeDataRevalidate.time}
          <br />
          <hr />
          <h3>Not Cached</h3>
          <strong>Timezone:</strong> {timeDataNoCache.timezone}
          <br />
          <strong>Time:</strong> {timeDataNoCache.time}
          <br />
          <hr />
          <h3>Instance Info</h3>
          <strong>CloudRun - InstanceID:</strong> {instanceInfo?.instanceId}
          <br />
          <strong>CloudRun - IP:</strong> {instanceInfo?.ip}
          <br />
        </div>
        <hr />
        <RevalidateFrom />
      </main>
      <footer className="footer"></footer>
    </>
  );
}
