'use client';

import { useFormStatus } from 'react-dom';

import { revalidateTagTimeData } from './server-actions';

function RevalidateButton({ title }: { title: string }) {
  const { pending } = useFormStatus();

  return (
    <button className="revalidate-from-button1" type="submit" disabled={pending} aria-disabled={pending}>
      {title}
    </button>
  );
}

export function RevalidateFrom() {
  return (
    <form className="revalidate-from1" action={revalidateTagTimeData}>
      <RevalidateButton title="Revalidate TAG (time-data)" />
    </form>
  );
}
