import { BeratungZuhauseSm, ContactSm, PhoneSm, ShopfinderSm } from '@/components/Icons/sm';
import type { ContactFlyoutListItemProps } from '@/components/MainNavigation/ContactFlyout/ContactFlyoutListItem';

export const Home = 'Deutsche Glasfaser';
export const FALLBACK_HOTLINE = '02861 8133 400';
export const DGP_CAMPAIGN_PAGE_HOTLINE = '02861 687 9974';
export const FALLBACK_DGP_PROFESSIONAL_PAGE_HOTLINE = '02861 687 9940';
export const AVIDAT_CHANNEL = 'website';

export enum PageType {
  Homepage = 'Startseite',
  Content = 'Content',
  TariffOverview = 'Tarifübersicht',
  TariffDetail = 'Tarifdetail',
  LandingPage = 'Landingpage',
  Products = 'Produkte',
  Category = 'Kategorie',
  DigitalKnowledge = 'Digital-Wissen',
  Campaign = 'Aktion',
}

// Labels
export const AC_BUTTON_LABEL = 'Verfügbarkeit prüfen';
export const CO_BUTTON_LABEL = 'Tarif buchen';
export const CLOSE_BUTTON_LABEL = 'Schließen';
export const CONTACT_BUTTON_LABEL = 'Kontakt aufnehmen';
export const PROMOTE_TO_FRIENDS_LABEL = 'Freunde werben';
export const CONSULTATION_AT_HOME_PK_LABEL = 'Beratung zuhause';
export const CONSULTATION_AT_HOME_GK_LABEL = 'Beratungstermin';
export const CONTACT_LABEL = 'Kontakt';
export const SHOPFINDER_LABEL = 'Shopfinder';
export const CONSULTATION_BY_PHONE_LABEL = 'Telefonische Beratung';
export const CHOOSE_ALL_SELECT_LABEL = 'Alle auswählen';

export const LOADING_DATA = 'Daten werden geladen...';

export const TARIFF_ID_NOT_FOUND = 'Der Tarif kann nicht geladen werden oder existiert nicht!';

export const NOT_AVAILABLE_CONTENT_TITLE = 'Tipp: Jetzt vormerken lassen!';
export const NOT_AVAILABLE_CONTENT_DESCRIPTION =
  'Wir benachrichtigen Sie kostenlos und unverbindlich, sobald wir bei Ihnen mit unserem Netzausbau starten.';
export const NOT_AVAILABLE_CONTENT_BUTTON_TEXT = 'Als Interessent registrieren';

export const BusinessArea = {
  Private: 'Privatkunden',
  Professional: 'Geschäftskunden',
  HousingIndustry: 'Wohnungswirtschaft',
  Communities: 'Kommunen',
  Companies: 'Unternehmen',
  DigitalCitizenNetwork: 'Digitales Bürgernetz',
} as const;

export type BusinessArea = (typeof BusinessArea)[keyof typeof BusinessArea];

export type BusinessAreaType = {
  name: BusinessArea;
  url: string;
  phoneConsultationLink: ContactFlyoutListItemProps;
  homeConsultationLink?: ContactFlyoutListItemProps;
  contactFormLink: ContactFlyoutListItemProps;
  shopFinderLink: ContactFlyoutListItemProps;
};

export type BusinessAreaMapType = Map<BusinessArea, BusinessAreaType>;

export const businessAreaMap: BusinessAreaMapType = new Map([
  [
    BusinessArea.Private,
    {
      name: BusinessArea.Private,
      url: '/',
      phoneConsultationLink: { icon: PhoneSm, label: CONSULTATION_BY_PHONE_LABEL, href: '/telefonische-beratung-b2c' },
      contactFormLink: { icon: ContactSm, label: CONTACT_LABEL, href: '/service/kontakt' },
      homeConsultationLink: {
        icon: BeratungZuhauseSm,
        label: CONSULTATION_AT_HOME_PK_LABEL,
        href: '/beratung-zuhause',
      },
      shopFinderLink: { icon: ShopfinderSm, label: SHOPFINDER_LABEL, href: '/shopfinder' },
    },
  ],
  [
    BusinessArea.Professional,
    {
      name: BusinessArea.Professional,
      url: '/geschaeftskunden',
      phoneConsultationLink: { icon: PhoneSm, label: CONSULTATION_BY_PHONE_LABEL, href: '/telefonische-beratung-b2b' },
      contactFormLink: { icon: ContactSm, label: CONTACT_LABEL, href: '/geschaeftskunden/service/kontakt' },
      homeConsultationLink: {
        icon: BeratungZuhauseSm,
        label: CONSULTATION_AT_HOME_GK_LABEL,
        href: '/geschaeftskunden/beratungstermin',
      },
      shopFinderLink: { icon: ShopfinderSm, label: SHOPFINDER_LABEL, href: '/shopfinder' },
    },
  ],
  [
    BusinessArea.HousingIndustry,
    {
      name: BusinessArea.HousingIndustry,
      url: '/wohnungswirtschaft',
      phoneConsultationLink: { icon: PhoneSm, label: CONSULTATION_BY_PHONE_LABEL, href: '/telefonische-beratung-wowi' },
      contactFormLink: { icon: ContactSm, label: CONTACT_LABEL, href: '/wohnungswirtschaft/kontakt' },
      shopFinderLink: { icon: ShopfinderSm, label: SHOPFINDER_LABEL, href: '/shopfinder' },
    },
  ],
  [
    BusinessArea.Communities,
    {
      name: BusinessArea.Communities,
      url: '/kommunen',
      phoneConsultationLink: {
        icon: PhoneSm,
        label: CONSULTATION_BY_PHONE_LABEL,
        href: '/telefonische-beratung-kommunen',
      },
      contactFormLink: { icon: ContactSm, label: CONTACT_LABEL, href: '/kommunen/kontaktformular' },
      shopFinderLink: { icon: ShopfinderSm, label: SHOPFINDER_LABEL, href: '/shopfinder' },
    },
  ],
  [
    BusinessArea.Companies,
    {
      name: BusinessArea.Companies,
      url: '/unternehmen',
      phoneConsultationLink: { icon: PhoneSm, label: CONSULTATION_BY_PHONE_LABEL, href: '/telefonische-beratung-b2c' },
      contactFormLink: { icon: ContactSm, label: CONTACT_LABEL, href: '/unternehmen/kontakt' },
      shopFinderLink: { icon: ShopfinderSm, label: SHOPFINDER_LABEL, href: '/shopfinder' },
    },
  ],
]);

export enum ApiRoutes {
  CITY = '/api/ac/city',
  STREET = '/api/ac/street',
  HOUSE_NR = '/api/ac/street-nr',
  MEDIA = '/api/media',
  SERVER_TIME = '/api/server-time',
}

export enum ThirdPartyScriptUrls {
  USERCENTRICS = 'https://app.usercentrics.eu/browser-ui/latest/loader.js',
}

export enum FormSubmissionErrorMessages {
  FILE_TYPE = 'Ungültiger Dateityp. Bitte laden Sie eine gültige Datei hoch.',
  AVIDAT = 'Das Formular konnte nicht gesendet werden. Bitte versuchen Sie es erneut oder wenden Sie sich an unseren Kundenservice.',
  INTERESTED_PERSON = 'Wir konnten Sie nicht als Interessent registrieren. Bitte versuchen Sie es erneut oder wenden Sie sich an unseren Kundenservice.',
  RECAPTCHA = 'ReCAPTCHA-Überprüfung fehlgeschlagen. Bitte versuchen Sie es erneut.',
  TO_RECIPIENT = 'Kontaktanfrage konnte nicht gesendet werden. Bitte versuchen Sie es erneut oder wenden Sie sich an unseren Kundenservice.',
  TO_CUSTOMER = 'Wir haben Ihre Mail erhalten. Wir konnten leider keine Mail an die von Ihnen angegebene Adresse versenden. Bitte wenden Sie sich an unseren Kundenservice.',
  GENERIC = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
  OUTSIDE_BUSINESS_HOURS = 'Unser Service-Team ist aktuell nicht verfügbar. Bitte wählen Sie ein anderes Zeitfenster für Ihren Rückruf.',
}

// AVIDAT
export const AVIDAT_FORM_SESSION_KEY = 'AviDatFormContainer-odp';

// URL PARAMS
export const QUERY_PARAMS = {
  NETZAKTIV: 'netzaktiv',
  TELLJA_ID: 'telljaid',
  ODP: 'odp',
  ORDER_ID: 'orderId',
} as const;

// Algolia
export const ALGOLIA_AC_SEARCH_TERM = 'Verfügbarkeitscheck';
export const ALGOLIA_AC_OBJECT_ID = '1QFFBP3ikz9pwl5Clor9jz';

// Hotline
export const OUTSIDE_OF_WORKING_HOURS_HOTLINE_LABEL = 'Jetzt Rückruf vereinbaren';
export const DEFAULT_PROFESSIONAL_CAMPAIGN_ID = '10163';

// Usercentrics
export const UsercentricsServiceId = {
  YOUTUBE: 'BJz7qNsdj-7',
  GOOGLE_MAPS: 'S1pcEj_jZX',
  LIVE_PERSON: '_bUtl8gS',
  ALGOLIA: 'tnfBi7gwe',
  TRUSTPILOT: 'qEs4t49Rg',
} as const;

export type UsercentricsServiceId = (typeof UsercentricsServiceId)[keyof typeof UsercentricsServiceId];

// Logos
export const DEFAULT_DG_LOGO = '/logo.svg';
export const DEFAULT_DG_PARTNER_LOGO = '/dg-certified-partner-logo.svg';

export const vcSplitTestEventName = 'vcSplitTestVariantVarReady';
