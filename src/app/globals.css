@import 'tailwindcss';

@theme {
  --breakpoint-xxs: 384px;

  --font-soehne: var(--font-soehne);
  --font-soehne-condensed: var(--font-soehne-condensed);
  --font-dgl-icons: var(--font-dgl-icons);

  --text-sm: 14px;
  --text-sm--line-height: 21px;

  --color-sand: #f5f5f0;
  --color-basalt: #464646;

  --color-gray-50: #ebebe5;
  --color-gray-100: #e3e3df;
  --color-gray-200: #d2d2ce;
  --color-gray-300: #c0c0bd;
  --color-gray-400: #afafac;
  --color-gray-500: #9d9d9b;
  --color-gray-600: #8c8c8a;
  --color-gray-700: #7a7a79;
  --color-gray-800: #696968;
  --color-gray-900: #575757;

  --color-heaven: #14a0dc;
  --color-flower: #ebb40f;
  --color-gras: #32c864;
  --color-sun: #ebb40f;
  --color-success: #019303;
  --color-error: #c51f02;
  --color-error-light: #efa3a3;
  --color-warning: #ffa933;
  --color-info: #14a0dc;
  --color-hover: #0265a3;
  --color-focus: #003399;
  --color-active: #026c99;
  --color-system: #0793cf;

  --content-checked-icon: url('/icons/checked.svg');
  --content-indeterminate-icon: url('/icons/indeterminate.svg');

  --background-image-footer-mobile: url('/images/footer-bg-mobile.png');
  --background-image-footer-desktop: url('/images/footer-bg-desktop.png');

  --shadow-button: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
  --shadow-container: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
  --shadow-body: 0 0 50px 5px rgba(70, 70, 70, 0.1);

  --height-120: 30rem;

  --spacing-mobile-nav: 56px;
  --spacing-desktop-nav: 84px;
  --spacing-meta-nav: 48px;

  --transition-property-height: height;

  --container-page: 1920px;

  --line-clamp-7: 7;
  --line-clamp-10: 10;

  --z-index-base: 0;
  --z-index-float: 10;
  --z-index-overlay: 30;
  --z-index-modal: 40;
  --z-index-header: 50;
  --z-index-max: 100;

  /* Safe area and viewport unit support */
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);

  /* Dynamic viewport fallbacks */
  --viewport-height: 100vh;
  --viewport-width: 100vw;
}

/* Safe area and viewport support */
@supports (height: 100dvh) {
  :root {
    --viewport-height: 100dvh;
  }
}

@supports (width: 100dvw) {
  :root {
    --viewport-width: 100dvw;
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button,
  input[type='number'] {
    -webkit-appearance: none;
    margin: 0;
    -moz-appearance: textfield !important;
  }

  input[type='search']::-webkit-search-decoration,
  input[type='search']::-webkit-search-cancel-button,
  input[type='search']::-webkit-search-results-button,
  input[type='search']::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }

  button,
  [role='button'] {
    cursor: pointer;
  }

  :focus-visible {
    outline: 2px solid var(--color-focus);
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff;
  }

  .bg-basalt input:focus-visible {
    outline: 2px solid #ffffff;
    box-shadow: 0 0 0 2px #003399;
  }
}

@utility toggle__button {
  &:focus-visible + .toggle__label .toggle__switch {
    box-shadow:
      0 0 0 1px rgba(0, 124, 248, 1),
      0 0 0 3px rgba(0, 124, 248, 1);
  }
}

@utility toggle__label {
  .toggle__button:focus-visible + & .toggle__switch {
    box-shadow:
      0 0 0 1px rgba(0, 124, 248, 1),
      0 0 0 3px rgba(0, 124, 248, 1);
  }
}

@utility toggle__switch {
  .toggle__button:focus-visible + .toggle__label & {
    box-shadow:
      0 0 0 1px rgba(0, 124, 248, 1),
      0 0 0 3px rgba(0, 124, 248, 1);
  }
}

@utility word-break {
  word-break: break-word;
}

svg * {
  transform-box: fill-box;
}

html:has(dialog[open]):has([id='modal']) {
  overflow: hidden;
}

input[type='date']::-webkit-inner-spin-button,
input[type='date']::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

input[type='date']::-webkit-date-and-time-value {
  height: 3em;
  text-align: left;
}

input[type='date'] {
  -webkit-min-logical-width: calc(100% - 16px);
}
