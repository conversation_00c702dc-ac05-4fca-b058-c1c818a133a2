import type { MetadataRoute } from 'next';

import { getSitemapEntries } from '@/utils/sitemap/getSitemapEntries';

export default async function robots(): Promise<MetadataRoute.Robots> {
  const sitemapEntries = await getSitemapEntries();

  return {
    rules: {
      userAgent: '*',
      allow: '/',
    },
    sitemap: [
      ...sitemapEntries.map(({ id }) => `${process.env.NEXT_PUBLIC_SITE_URL}/sitemap/${id}.xml`),
      'https://www.deutsche-glasfaser.de/blog/sitemap_index.xml',
    ],
  };
}
