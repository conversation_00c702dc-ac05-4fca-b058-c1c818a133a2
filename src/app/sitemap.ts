/**
 * See https://nextjs.org/docs/app/api-reference/functions/generate-sitemaps
 * In production, your generated sitemaps will be available at /.../sitemap/[id].xml. For example, /product/sitemap/1.xml.
 * In development, you can view the generated sitemap on /.../sitemap.xml/[id]. For example, /product/sitemap.xml/1. This difference is temporary and will follow the production format.
 *
 */
import type { MetadataRoute } from 'next';

import { statSync } from 'node:fs';
import { readdir } from 'node:fs/promises';

import contentfulClient from '@/apis/contentful/api';

import { isDevEnv } from '@/utils/isDevEnv';
import { type SitemapEntriesReturnType, getSitemapEntries } from '@/utils/sitemap/getSitemapEntries';
import { getStates } from '@/utils/sitemap/getStates';

let sitemapEntries: undefined | SitemapEntriesReturnType = undefined;

export async function generateSitemaps() {
  sitemapEntries = await getSitemapEntries();

  return sitemapEntries;
}

export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  const contentful = contentfulClient({ preview: isDevEnv });
  const limit = 1000;

  const currentSitemapEntry = sitemapEntries?.find((obj) => obj.id === id);

  if (!currentSitemapEntry) {
    return [];
  }

  const netzausbauResult = await getStates();

  switch (currentSitemapEntry.type) {
    // prod: /sitemap/0.xml
    // local: /sitemap.xml/0
    case 'page':
      const pagesResult = await contentful.PageCollection({
        preview: isDevEnv,
        limit,
        skip: currentSitemapEntry.counter * limit,
        where: { seoMetadata: { noIndex: false } },
      });
      const pageCollection = pagesResult.pageCollection;

      if (!pageCollection?.items.length) {
        return [];
      }

      return pageCollection.items
        .map((page) => {
          if (!page) {
            return;
          }

          return generateSitemapEntry(page.slug || '', page.sys.publishedAt);
        })
        .filter((obj) => obj) as MetadataRoute.Sitemap;

    // prod: /sitemap/1.xml
    // local: /sitemap.xml/1
    case 'pdf':
      const assetsResult = await contentful.AssetCollection({
        preview: isDevEnv,
        where: { contentType: 'application/pdf' },
        limit,
        skip: currentSitemapEntry.counter * limit,
      });
      const assetsCollection = assetsResult.assetCollection;

      const path = '/public/productinformation/';
      const pdfsFromFilesystemArray = await readdir(process.cwd() + path);
      const pdfsFromFilesystem: { url: string; modified: string }[] = [];
      pdfsFromFilesystemArray.forEach((pdf) => {
        pdfsFromFilesystem.push({
          url: '/productinformation/' + pdf,
          modified: statSync(process.cwd() + path + pdf).mtime.toISOString(),
        });
      });

      if (!assetsCollection?.items.length && !netzausbauResult.length && !pdfsFromFilesystem.length) {
        return [];
      }

      return [
        ...(assetsCollection?.items
          .map((asset) => {
            if (!asset) {
              return;
            }

            return generateSitemapEntry(asset.url || '', asset.sys.publishedAt, false);
          })
          .filter((obj) => obj) as MetadataRoute.Sitemap),
        ...(netzausbauResult
          ?.map((page) => {
            if (!page) {
              return;
            }
            return page.downloads
              ?.map((d) => {
                if (!d || d.type !== 'pdf') {
                  return;
                }

                return generateSitemapEntry(d.src || '', page.modified, true);
              })
              .filter((obj) => obj) as MetadataRoute.Sitemap;
          })
          .flat() as MetadataRoute.Sitemap),
        ...(pdfsFromFilesystem
          .map((pdf) => {
            if (!pdf) {
              return;
            }

            return generateSitemapEntry(pdf.url || '', pdf.modified || '');
          })
          .filter((obj) => obj) as MetadataRoute.Sitemap),
      ];

    // prod: /sitemap/2.xml
    // local: /sitemap.xml/2
    case 'netzausbau':
      if (!netzausbauResult.length) {
        return [];
      }

      return netzausbauResult
        .map((page) => {
          if (!page) {
            return;
          }

          return generateSitemapEntry(page.slug || '', page.modified || '');
        })
        .filter((obj) => obj) as MetadataRoute.Sitemap;
  }
}

function generateSitemapEntry(url: string, lastModified: string, includeHost: boolean = true) {
  return {
    url: (includeHost ? process.env.NEXT_PUBLIC_SITE_URL : '') + url,
    lastModified,
  };
}
