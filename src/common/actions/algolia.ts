'use server';

import { headers } from 'next/headers';

import algoliaSearch from 'algoliasearch';

import { ALGOLIA_AC_OBJECT_ID, ALGOLIA_AC_SEARCH_TERM } from '@/app/consts';

import { deleteCookie, getCookie, setCookie } from '@/actions/cookie';

import type { CustomResult } from '@/types/algolia';

const ALGOLIA_COOKIE_KEY = '_ALGOLIA';

function getSearchIndex() {
  if (
    !process.env.NEXT_PUBLIC_ALGOLIA_APP_ID ||
    !process.env.NEXT_PUBLIC_ALGOLIA_INDEX_NAME ||
    !process.env.ALGOLIA_SEARCH_ADMIN_KEY
  )
    return;

  const algoliaClient = algoliaSearch(process.env.NEXT_PUBLIC_ALGOLIA_APP_ID, process.env.ALGOLIA_SEARCH_ADMIN_KEY);
  return algoliaClient.initIndex(process.env.NEXT_PUBLIC_ALGOLIA_INDEX_NAME);
}

export async function getUserTokenFromCookies(): Promise<string | null> {
  const cookie = await getCookie(ALGOLIA_COOKIE_KEY);

  return cookie?.value || null;
}

export async function setUserTokenInCookies(userToken: string) {
  await setCookie({
    name: ALGOLIA_COOKIE_KEY,
    value: userToken,
    httpOnly: true,
    secure: true,
    path: '/',
    expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year expiration
    sameSite: 'lax',
  });
}

export async function removeUserTokenFromCookies() {
  await deleteCookie(ALGOLIA_COOKIE_KEY);
}

export async function searchClient(
  term: string,
  page: number,
  userToken: string,
  filter?: string[],
): Promise<CustomResult | undefined> {
  if (term && term.length > 0 && term.length < 3) return;

  const index = getSearchIndex();

  // Check if the term is a 5-digit code (like a German zipcode)
  const isZipCode = /^\d{5}$/.test(term);

  // If it's a zipcode, replace the search term with "verfügbarkeitscheck"
  // and filter for the specific page with objectId="1QFFBP3ikz9pwl5Clor9jz"
  const searchTerm = isZipCode ? ALGOLIA_AC_SEARCH_TERM : term;
  const specificObjectFilter = isZipCode ? `objectID:${ALGOLIA_AC_OBJECT_ID}` : '';

  // Get the user agent from the request headers
  const headersList = headers();
  const userAgent = headersList.get('user-agent');

  // Check if the device is a mobile device
  const isMobileView = userAgent!.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i);

  const hitsPerPageDesktop = 6;
  const hitsPerPageMobile = 4;
  const hitsPerPage = isMobileView ? hitsPerPageMobile : hitsPerPageDesktop;

  // Construct the filters string
  let filtersString = '';

  // Add business area filters if provided
  if (filter && filter.length > 0) {
    filtersString = Array.isArray(filter)
      ? filter.map((f) => `fields.businessArea:${f}`).join(' OR ')
      : `fields.businessArea:${filter}`;
  }

  // Add the specific object filter for zipcode searches
  if (specificObjectFilter) {
    filtersString = specificObjectFilter;
  }

  return index?.search(searchTerm, {
    userToken,
    clickAnalytics: true,
    hitsPerPage,
    page,
    facets: ['fields.businessArea'],
    filters: filtersString,
  });
}
