'use server';

import { cookies } from 'next/headers';

export type CookieProps = {
  name: string;
  value: string;
  httpOnly?: boolean;
  path?: string;
  domain?: string;
  expires?: Date;
  secure?: boolean;
  sameSite?: 'lax' | 'strict' | 'none';
};

export async function setCookie({
  name,
  value,
  httpOnly = true,
  path = '/',
  domain,
  expires,
  secure = true,
  sameSite,
}: CookieProps) {
  'use server';
  cookies().set({
    name,
    value,
    httpOnly,
    path,
    domain,
    expires,
    secure,
    sameSite,
  });
}

export async function getCookie(key: string) {
  'use server';
  return cookies().get(key);
}

export async function deleteCookie(key: string) {
  'use server';
  return cookies().delete(key);
}
