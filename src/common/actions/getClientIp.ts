'use server';

import { headers } from 'next/headers';

/**
 * Server Action to extract the client IP address from the current request headers.
 * Optimized for GCP environments.
 *
 * @returns The extracted IP address string, or null if not found.
 */
export async function getClientIpAddress(): Promise<string | null> {
  try {
    const headersList = headers();

    // GCP Load Balancer headers in order of preference
    const gcpHeaders = ['x-forwarded-for', 'x-real-ip', 'x-client-ip'];

    for (const header of gcpHeaders) {
      const clientIp = headersList.get(header);

      if (clientIp) {
        // Extract the first IP if multiple are present
        const ipAddress = clientIp.split(',')[0].trim();
        return ipAddress;
      }
    }

    return null;
  } catch (error) {
    // Handle potential errors during header access (though unlikely for .get)
    console.error('Error accessing request headers:', error);
    return null;
  }
}
