'use server';

import { FormSubmissionErrorMessages } from '@/app/consts';

import type {
  FileProps,
  FormData,
  FormField,
  FormGroup,
  FormRecipient,
  FormSubmissionResult,
  FormType,
} from '@/types/forms';

import { handleAviDatForm, handlePKOwnerDataAviDatForm } from '@/utils/forms/avidat';
import { ALLOWED_FILE_TYPES } from '@/utils/forms/consts';
import { handleConsultationAtHomeForm } from '@/utils/forms/consultationAtHome';
import { handleDefaultForm } from '@/utils/forms/defaultForm';
import { handleDialfireForm } from '@/utils/forms/dialfire';
import { createErrorResponse } from '@/utils/forms/helpers';
import { handleInterestedPersonForm } from '@/utils/forms/interestedPerson';

type SubmitFormProps = {
  recipient: FormRecipient;
  data: FormData;
  formType: FormType;
  formElements: (FormField | FormGroup)[];
  attachments?: FileProps[];
  subject?: string;
  provider?: string;
};

// Map of form types to their handlers
type FormHandlerMap = {
  [key in FormType]?: (props: {
    recipient: FormRecipient;
    data: FormData;
    formType: FormType;
    formElements: (FormField | FormGroup)[];
    attachments?: FileProps[];
    subject?: string;
  }) => Promise<FormSubmissionResult>;
};

// Define handler map for different form types
const formHandlers: FormHandlerMap = {
  AviDat: async ({ data }) => handleAviDatForm(data),
  PKOwnerDataAviDat: async ({ data }) => handlePKOwnerDataAviDatForm(data),
  InterestedPerson: async ({ data }) => handleInterestedPersonForm(data),
  ConsultationAtHomeB2C: async ({ recipient, data, formType, formElements }) =>
    handleConsultationAtHomeForm(recipient, data, formType, formElements),
  ConsultationAtHomeB2B: async ({ recipient, data, formType, formElements }) =>
    handleConsultationAtHomeForm(recipient, data, formType, formElements),
  Dialfire: async ({ data }) => handleDialfireForm(data),
};

export async function submitForm({
  recipient,
  data,
  formType,
  formElements,
  attachments,
  subject,
  provider,
}: SubmitFormProps): Promise<FormSubmissionResult> {
  'use server';

  // Validate file types
  if (attachments && attachments.some((file) => !ALLOWED_FILE_TYPES.includes(file.type))) {
    console.error('Error in Form Submission: Invalid file type');
    return createErrorResponse(FormSubmissionErrorMessages.FILE_TYPE);
  }

  try {
    // Get the handler for the form type or use the default handler
    const handler =
      formHandlers[formType] ||
      (({ recipient, data, formType, formElements, attachments, subject }) =>
        handleDefaultForm(recipient, data, formType, formElements, attachments, subject, provider));

    // Call the appropriate handler with all parameters as a single object
    return await handler({
      recipient,
      data,
      formType,
      formElements,
      attachments,
      subject,
    });
  } catch (error) {
    console.error('Error in Form Submission:', error);
    return createErrorResponse(FormSubmissionErrorMessages.GENERIC);
  }
}
