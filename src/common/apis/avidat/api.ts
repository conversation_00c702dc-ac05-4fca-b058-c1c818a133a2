import wretch from 'wretch';

import type { FormType } from '@/types/forms';

import { checkEnvVariable } from '@/utils/checkEnvVariable';

/**
 * AVI DAT post data
 * Documentation: https://brandung.atlassian.net/browse/DGWRL-430
 */
export type AviDatData = {
  vornamemieter?: string; // Vorname des Mieters
  nachnamemieter?: string; // Nachname des Mieters
  kundennummermieter?: string; // Kundennummer des Mieters
  vornameeigentuemer?: string; // Vorname des Eigentümers
  nachnameeigentuemer: string; // Nachname des Eigentümers
  firmennameeigentuemer?: string; // Firmenname des Eigentümers
  telefonnummereigentuemer: string; // Telefonnummer des Eigentümers
  e_maileigentuemer?: string; // E-Mail-Adresse des Eigentümers
  weiterehinweise?: string; // Weitere Hinweise
  dsgvo?: string; // Zustimmung zur Datenschutzgrundverordnung (DSGVO)
  pflichtfeld?: string; // Pflichtfeld
  anfragesenden?: string; // Anfrage senden an
  odp?: string; // SHA256 Hash aus object_id und customer_id
  typeOfPerson?: string; // Typ der Person
  management?: boolean; // Hausverwaltung vorhanden
  managementInfo?: string; // Hausverwaltung Informationen
};

export type AviDatDataOwner = {
  egt_salutation: string;
  egt_firstName: string;
  egt_lastName: string;
  egt_phoneNumber: string;
  egt_emailAddress: string;
  orderId: string;
  optin: string;
  channel: string;
};

export async function sendAviDatForm(formType: FormType, data: AviDatData | AviDatDataOwner) {
  const AVIDAT_API_URL = checkEnvVariable('AVIDAT_API_URL');
  const AVIDAT_API_KEY = checkEnvVariable('AVIDAT_API_KEY');

  if (!AVIDAT_API_URL || !AVIDAT_API_KEY) {
    return false;
  }

  // DG IT might take a while to change the API URL, so we parse it to get the base URL
  const url = new URL(AVIDAT_API_URL);
  const baseUrl = `${url.protocol}//${url.host}`;

  const apiVersion = '/api/v1';
  const endpoint = formType === 'AviDat' ? '/data' : '/data-owner';

  try {
    return await wretch(`${baseUrl}${apiVersion}${endpoint}`)
      .headers({ 'X-API-Key': AVIDAT_API_KEY })
      .post(data)
      .res((res) => {
        if (res.status !== 204) {
          console.warn(`AVI DAT api warning: Unexpected response status ${res.status}. Status 204 expected.`);
        }
        return res;
      });
  } catch (error: any) {
    const message =
      typeof error.message === 'object' && Object.keys(error.message).length > 0
        ? JSON.stringify(error.message)
        : (error.response?.statusText ?? error.toString());

    console.error(`AVI DAT api error status ${error.status}: ${message}`);
    return false;
  }
}
