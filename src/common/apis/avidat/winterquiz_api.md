# AVI DAT QUIZ API DOCUMENTATION

Source: https://brandung.atlassian.net/browse/DGWRL-837

openapi: 3.0.0
info:
title: Quiz Formulardaten API
description: API zur Entgegennahme von Formulardaten aus einer Quiz Landingpage. Die Daten bestehen aus Kundennummer und einer Liste aus Antworten zu Fragen.
version: 1.1.0
servers:

- description: Production
  url: https://dg-form-data.avi-dat.de/api/v1
  paths:
  /quiz:
  post:
  summary: Daten einreichen
  description: Ermöglicht das Einreichen der Daten
  requestBody:
  required: true
  content:
  application/json:
  schema:
  type: object
  properties:
  kundennummer:
  type: string
  description: SHA256 Hash aus der Kundennummer
  antworten:
  type: object
  description: Liste der Antworten
  properties:
  frage1:
  type: string
  frage2:
  type: string
  frage3:
  type: string
  frage4:
  type: string
  frage5:
  type: string
  required: - kundennummer - antworten
  responses:
  204:
  description: Erfolgreich abgespeichert
  400:
  description: Ungültige Daten
  401:
  description: Nicht authentifiziert
  403:
  description: Zugriff abgelehnt
  429:
  description: Zu viele Anfragen
  500:
  description: Interner Serverfehler

components:
securitySchemes:
ApiKeyAuth:
type: apiKey
in: header
name: X-API-KEY

security:

- ApiKeyAuth: []
