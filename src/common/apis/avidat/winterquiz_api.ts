'use server';

import crypto from 'crypto';
import wretch from 'wretch';

const AVIDAT_QUIZ_API_URL = process.env.AVIDAT_QUIZ_API_URL ?? 'https://dg-form-data.avi-dat.de/api/v1/quiz';
const AVIDAT_QUIZ_API_KEY = process.env.AVIDAT_QUIZ_API_KEY ?? '';

/**
 * AVI DAT winterquiz post data
 * Documentation: https://brandung.atlassian.net/browse/DGWRL-837
 */
export type WinterquizPostData = {
  kundennummer: string;
  antworten: {
    frage1: string;
    frage2: string;
    frage3: string;
    frage4: string;
    frage5: string;
  };
};

export async function sendPostRequest(data: WinterquizPostData, isHashed: boolean): Promise<boolean> {
  if (!AVIDAT_QUIZ_API_KEY) {
    console.error('AVI DAT Quiz API key not found. Please add AVIDAT_QUIZ_API_KEY to your .env.local.');
    return false;
  }

  // Encrypt field "kundennummer" with SHA256
  const encryptedData = JSON.stringify({
    ...data,
    kundennummer: isHashed ? data.kundennummer : crypto.createHash('sha256').update(data.kundennummer).digest('hex'),
  });

  try {
    return await wretch(AVIDAT_QUIZ_API_URL)
      .headers({
        'X-API-KEY': AVIDAT_QUIZ_API_KEY,
        'Content-Type': 'application/json',
      })
      .post(encryptedData)
      .res((res) => {
        if (res.status !== 204) {
          console.warn(
            `AVI DAT Winterquiz api warning: Unexpected response status ${res.status}. Status 204 expected.`,
          );
        }
        return res.status === 204;
      });
  } catch (error: any) {
    const message =
      typeof error.message === 'object' && Object.keys(error.message).length > 0
        ? JSON.stringify(error.message)
        : (error.response?.statusText ?? error.toString());

    console.error(`AVI DAT Winterquiz api error status ${error.status}: ${message}`);

    return false;
  }
}
