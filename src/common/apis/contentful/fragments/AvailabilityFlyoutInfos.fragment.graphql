fragment AvailabilityFlyoutInfosFragment on AvailabilityFlyoutInfosComponent {
  ...EntryFragment
  internalName
  businessTelephone
  businessOpeningHours {
    json
    links {
      entries {
        inline {
          sys {
            id
          }
        }
      }
    }
  }
  businessAdvantagesCollection {
    items {
      ... on Entry {
        ...EntryFragment
      }
      ... on BusinessAdvantagesComponent {
        ...BusinessAdvantagesFragment
      }
    }
  }
}
