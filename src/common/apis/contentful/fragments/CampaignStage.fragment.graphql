fragment CampaignStageFragment on CampaignStage {
  ...EntryFragment
  internalName
  provider
  campaignId
  campaignFixed
  headline
  text {
    ...CampaignStageTextFragment
  }
  image {
    ...ImageFragment
  }
  imagePosition
  cta {
    ...CtaFragment
  }
  additionalCta {
    ...CtaFragment
  }
  ctaAlignment
  banner {
    primaryBadge {
      ...BadgeFragment
    }
    secondaryBadge {
      ...BadgeFragment
    }
    color
  }
  layout
}
