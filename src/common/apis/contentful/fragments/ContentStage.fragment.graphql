fragment ContentStageFragment on ContentStage {
  ...EntryFragment
  internalName
  headline
  text {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  image {
    ...ImageFragment
  }
  imagePosition
  linkBox {
    headline
    linksCollection(limit: 10) {
      items {
        ...CtaFragment
      }
    }
  }
  explanationBox {
    headline
    headlineTag
    explanationText
  }
}
