fragment CtaFragment on Cta {
  ...EntryFragment
  label
  icon {
    ...ImageFragment
  }
  variant
  link {
    __typename
    ... on ExternalLinkComponent {
      ...ExternalLinkFragment
    }
    ... on InternalLinkComponent {
      ...InternalLinkFragment
    }
    ... on Page {
      title
      slug
      teaserText
      teaserImage {
        ...ImageFragment
      }
    }
    ... on Document {
      label
      document {
        url
      }
    }
    ... on AcFlyout {
      ...AcFlyoutFragment
    }
  }
}
