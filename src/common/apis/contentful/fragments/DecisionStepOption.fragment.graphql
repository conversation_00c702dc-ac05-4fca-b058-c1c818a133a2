fragment DecisionStepOptionFragment on DecisionStepOption {
  title
  icon {
    url
    description
  }
  nextStep {
    __typename
    ... on DecisionStep {
      ...EntryFragment
    }
    ... on InternalLinkComponent {
      ...EntryFragment
    }
    ... on Form {
      ...EntryFragment
    }
    ... on Accordion {
      ...EntryFragment
    }
    ... on TextButtonTeaser {
      ...EntryFragment
    }
    ... on TextImageButtonTeaserVertical {
      ...EntryFragment
    }
    ... on TextIconTeaserComponent {
      ...EntryFragment
    }
    ... on TextTeaserComponent {
      ...EntryFragment
    }
  }
}
