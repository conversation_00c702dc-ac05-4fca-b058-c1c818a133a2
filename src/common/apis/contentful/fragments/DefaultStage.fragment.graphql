fragment DefaultStageFragment on DefaultStage {
  ...EntryFragment
  internalName
  headline
  subline
  text {
    ...DefaultStageTextFragment
  }
  image {
    ...ImageFragment
  }
  imagePosition
  cta {
    ...CtaFragment
  }
  additionalCta {
    ...CtaFragment
  }
  ctaAlignment
  textColor
  autoPlay
  loop
  mediaDesktop {
    ...MediaFragment
  }
  mediaMobile {
    ...MediaFragment
  }
  layout
}
