fragment GridFragment on GridModule {
  ...EntryFragment
  internalName
  headline
  headlineTag
  introText {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  cta {
    ...CtaFragment
  }
  ctaPosition
  theme
  centerContent
  columns
  elementsCollection(limit: 16) {
    items {
      ...EntryFragment
      ... on TextIconTeaserComponent {
        theme
      }
      ... on TextButtonTeaser {
        theme
      }
      ... on Product {
        variant
      }
    }
  }
}
