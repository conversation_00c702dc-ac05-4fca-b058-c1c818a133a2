fragment InternalLinkFragment on InternalLinkComponent {
  ...EntryFragment
  internalName
  page {
    slug
  }
  module {
    __typename
    ... on Accordion {
      internalName
    }
    ... on AvailabilityCheckModule {
      internalName
    }
    ... on ConsultationAtHomeModule {
      internalName
    }
    ... on DownloadModule {
      internalName
    }
    ... on Form {
      internalName
    }
    ... on GridModule {
      internalName
    }
    ... on InfoGraphicModule {
      internalName
    }
    ... on InteractiveMapModule {
      internalName
    }
    ... on PlaceholderModule {
      internalName
    }
    ... on ModuleProductTable {
      internalName
    }
    ... on ProductBannerModule {
      internalName
    }
    ... on RegionOverviewModule {
      internalName
    }
    ... on SealBarModule {
      internalName
    }
    ... on ServiceAndContactModule {
      internalName
    }
    ... on SliderModule {
      internalName
    }
    ... on TariffInfoModule {
      internalName
    }
    ... on TariffCarouselModule {
      internalName
    }
    ... on TariffOverviewModule {
      internalName
    }
    ... on TariffTeaserModule {
      internalName
    }
    ... on TextButtonModule {
      internalName
    }
    ... on TextImageButtonModule {
      internalName
    }
    ... on VideoModule {
      internalName
    }
  }
}
