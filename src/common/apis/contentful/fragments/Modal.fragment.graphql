fragment ModalFragment on Modal {
  internalName
  label
  content {
    __typename
    ... on TextButtonTeaser {
      ...TextButtonTeaserFragment
    }
    ... on TextIconTeaserComponent {
      ...TextIconTeaserFragment
    }
    ... on TextTeaserComponent {
      ...TextTeaserFragment
    }
    ... on GridModule {
      elementsCollection(limit: 3) {
        items {
          ... on TextTeaserComponent {
            ...TextTeaserFragment
          }
          ... on TextButtonTeaser {
            ...TextButtonTeaserFragment
          }
          ... on TextIconTeaserComponent {
            ...TextIconTeaserFragment
          }
        }
      }
    }
    ... on Video {
      url
    }
  }
}
