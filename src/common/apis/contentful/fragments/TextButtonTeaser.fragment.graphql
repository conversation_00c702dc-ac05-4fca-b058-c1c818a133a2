fragment TextButtonTeaserFragment on TextButtonTeaser {
  ...EntryFragment
  internalName
  headline
  headlineTag
  text {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  textAlignment
  cta {
    ...CtaFragment
  }
  theme
}
