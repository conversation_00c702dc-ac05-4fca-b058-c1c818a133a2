fragment TextIconTeaserFragment on TextIconTeaserComponent {
  ...EntryFragment
  internalName
  headline
  headlineTag
  text {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  textAlignment
  icon {
    ...ImageFragment
  }
  cta {
    ...CtaFragment
  }
  theme
  phonenumber
  hotlineList {
    ...HotlineListFragment
  }
}
