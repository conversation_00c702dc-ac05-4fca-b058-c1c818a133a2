fragment TextButtonImageTeaserFragment on TextImageButtonTeaserVertical {
  ...EntryFragment
  internalName
  headline
  headlineTag
  text {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  textAlignment
  image {
    ...ImageFragment
  }
  cta {
    ...CtaFragment
  }
  hoverText
  ratio
}
