fragment TextTeaserFragment on TextTeaserComponent {
  ...EntryFragment
  internalName
  headline
  headlineTag
  text {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  textAlignment
  mobileLineClampCount
}
