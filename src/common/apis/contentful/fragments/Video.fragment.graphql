fragment VideoFragment on Video {
  ...EntryFragment
  internalName
  headline
  headlineTag
  description {
    __typename
    json
    links {
      assets {
        hyperlink {
          ...HyperlinkAssetFragment
        }
      }
      entries {
        inline {
          ...InlineEntryFragment
        }
        hyperlink {
          ...HyperlinkEntryFragment
        }
      }
    }
  }
  textAlignment
  theme
  url
}
