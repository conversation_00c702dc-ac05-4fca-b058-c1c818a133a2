'use server';

import { draftMode } from 'next/headers';

import contentfulClient from '@/apis/contentful/api';
import type { AvailabilityFlyoutInfosComponent } from '@/apis/contentful/generated/types';

import { parseAvailabilityFlyoutInfos } from '@/components/MainNavigation/helpers/parseAvailabilityFlyoutInfos';

import type { AvailabilityFlyoutInfos } from '../dg/types';

/**
 * Retrieves the availability check infos from Contentful.
 */
export async function getAvailabilityCheckInfos(): Promise<AvailabilityFlyoutInfos | undefined> {
  const { isEnabled } = draftMode();
  const contentful = contentfulClient({ preview: isEnabled });

  const data = await contentful.AvailabilityFlyoutInfosByInternalName({
    name: 'availability-flyout-infos',
    preview: isEnabled,
  });

  const availabilityFlyoutInfos = data?.availabilityFlyoutInfosComponentCollection
    ?.items[0] as AvailabilityFlyoutInfosComponent;

  return parseAvailabilityFlyoutInfos(availabilityFlyoutInfos);
}
