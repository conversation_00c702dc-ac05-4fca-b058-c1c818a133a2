import { draftMode } from 'next/headers';

import { parseFormElementData } from '@/apis/contentful/parser/forms';
import { parseInternalLinkData } from '@/apis/contentful/parser/internalLink';

import type { TeaserProps } from '@/components/Teaser';

import type { Form, FormField, FormGroup, FormType } from '@/types/forms';
import type { InternalLink } from '@/types/internalLink';
import type { TextAlignment } from '@/types/text';

import contentfulClient from './api';
import type {
  AccordionItem,
  DecisionStep as DecisionStepEntry,
  DecisionStepOption as DecisionStepOptionEntry,
  DecisionStepOptionNextStep,
  FormFormElementsItem,
  Maybe,
} from './generated/types';
import { parseContentfulContentImage } from './parser/contentImage';
import { parseCtaData } from './parser/cta';
import { fetchContentById } from './utils/fetchContentById';

export type DecisionStep = {
  internalName: string;
  question?: string;
  label: string;
  variant: 'select' | 'chips';
  options: DecisionStepOption[];
};

export type DecisionStepOption = {
  title: string;
  icon?: {
    url: string;
    description?: string;
  };
  nextStep: DecisionStep | InternalLink | Form | AccordionStep | TeaserProps;
};

export async function getDecisionStep(id: string): Promise<DecisionStep> {
  const { isEnabled } = draftMode();
  const contentful = contentfulClient({ preview: isEnabled });

  const data = (await contentful.DecisionStepById({ id, preview: isEnabled })).decisionStep as DecisionStepEntry;

  return parseDecisionStep(data);
}

export async function parseDecisionStep(initialStep?: Partial<DecisionStepEntry> | null): Promise<DecisionStep> {
  const internalName = initialStep?.internalName ?? 'name-not-set';
  const question = initialStep?.question ?? undefined;
  const label = initialStep?.label ?? 'label-not-set';
  const variant = (initialStep?.variant as 'select' | 'chips') ?? 'select';

  const optionsPromises =
    initialStep?.optionsCollection?.items
      ?.filter((option): option is DecisionStepOptionEntry => !!option)
      .map((option) => parseDecisionStepOption(option)) ?? [];

  const options = await Promise.all(optionsPromises);

  return {
    internalName,
    question,
    label,
    variant,
    options,
  };
}

async function parseDecisionStepOption(option: Partial<DecisionStepOptionEntry>): Promise<DecisionStepOption> {
  return {
    title: option.title ?? '',
    icon: option.icon
      ? {
          url: option.icon.url ?? '',
          description: option.icon.description ?? undefined,
        }
      : undefined,
    nextStep: await parseNextStep(option.nextStep),
  };
}

export type AccordionStep = {
  internalName: string;
  headline: string;
  items: AccordionItem[];
};

async function parseNextStep(
  nextStep?: Partial<DecisionStepOptionNextStep> | null,
): Promise<DecisionStep | InternalLink | Form | AccordionStep | TeaserProps> {
  if (!nextStep) {
    console.warn('Encountered an option with no next step defined.');
    return { internalName: '', question: '', label: '', variant: 'select', options: [] };
  }

  const { __typename, sys } = nextStep;
  const id = sys?.id;
  if (!__typename || !id) {
    console.warn('Encountered a next step with missing __typename or id.');
    return { internalName: '', question: '', label: '', variant: 'select', options: [] };
  }

  if (__typename === 'DecisionStep') {
    return await getDecisionStep(id);
  }

  // Fetch the full data for the nextStep type by id
  const fullData = await fetchContentById({ id, contentType: __typename });

  if (__typename === 'InternalLinkComponent') {
    return parseInternalLinkData(fullData);
  }

  if (__typename === 'Form') {
    const formElements =
      (fullData?.formElementsCollection?.items as Maybe<FormFormElementsItem>[] | undefined)
        ?.map((el) => parseFormElementData(el))
        .filter((el: FormField | FormGroup | null): el is FormField | FormGroup => el !== null) ?? [];

    if (!fullData?.receiver?.sys?.id) {
      console.warn(`Form "${fullData?.internalName || 'Unnamed'}" has no receiver defined.`);
    }

    return {
      formType: fullData.formType as FormType,
      headline: fullData?.headline ?? undefined,
      // Deprecated, use introTextRt instead
      introText: fullData?.introText ?? undefined,
      // RichText field for introText
      introTextRt: fullData?.introTextRt ?? undefined,
      formElements,
      recipient: {
        id: fullData.receiver.sys.id || '',
      },
    };
  }

  if (__typename === 'Accordion') {
    return {
      internalName: fullData?.internalName ?? '',
      headline: fullData?.headline ?? '',
      items: (fullData?.accordionItemsCollection?.items as AccordionItem[]) ?? [],
    };
  }

  if (__typename === 'TextButtonTeaser') {
    return {
      internalName: fullData?.internalName ?? '',
      variant: 'text',
      headline: fullData?.headline ?? '',
      text: fullData?.text ?? '',
      theme: 'gray',
      textAlignment: (fullData?.textAlignment as TextAlignment) ?? 'left',
      cta: parseCtaData(fullData?.cta),
    };
  }

  if (__typename === 'TextImageButtonTeaserVertical') {
    return {
      internalName: fullData?.internalName ?? '',
      image: parseContentfulContentImage(fullData?.image),
      variant: 'image',
      headline: fullData?.headline ?? '',
      text: fullData?.text ?? '',
      theme: 'gray',
      textAlignment: (fullData?.textAlignment as TextAlignment) ?? 'left',
      cta: parseCtaData(fullData?.cta),
    };
  }

  if (__typename === 'TextIconTeaserComponent') {
    return {
      internalName: fullData?.internalName ?? '',
      image: parseContentfulContentImage(fullData?.icon),
      variant: 'icon',
      headline: fullData?.headline ?? '',
      text: fullData?.text ?? '',
      theme: 'gray',
      textAlignment: (fullData?.textAlignment as TextAlignment) ?? 'left',
      phonenumber: fullData?.phonenumber ?? '',
      cta: fullData?.cta ? parseCtaData(fullData?.cta) : undefined,
    };
  }

  if (__typename === 'TextTeaserComponent') {
    return {
      internalName: fullData?.internalName ?? '',
      headline: fullData?.headline ?? '',
      variant: 'text',
      text: fullData?.text ?? '',
      theme: 'gray',
      textAlignment: (fullData?.textAlignment as TextAlignment) ?? 'left',
    };
  }

  console.warn(`Unexpected next step type: ${__typename}`);
  return { internalName: '', question: '', label: '', variant: 'select', options: [] };
}
