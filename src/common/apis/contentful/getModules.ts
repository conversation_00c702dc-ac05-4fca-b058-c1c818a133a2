import { fetchContentById } from '@/apis/contentful/utils/fetchContentById';

export async function getModulesFromBody(bodyCollection: Array<any> | undefined) {
  if (!bodyCollection) return [];

  const modules: any[] = [];

  for (const item of bodyCollection) {
    const contentType = item?.__typename;
    const id = item?.sys?.id;

    modules.push(await fetchContentById({ id, contentType }));
  }
  return modules;
}
