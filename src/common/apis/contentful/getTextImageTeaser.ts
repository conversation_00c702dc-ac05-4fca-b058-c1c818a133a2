import type { TextImageTeaserProps } from '@/modules/TextImageTeaser/index.types';

import { fetchContentById } from '@/apis/contentful/utils/fetchContentById';

import { parseTextImageTeaser } from './parser/textImageTeaser';

export async function getTextImageTeaser(id: string): Promise<TextImageTeaserProps> {
  const teaser = await fetchContentById({ id, contentType: 'TextImageButtonModule' });
  return parseTextImageTeaser(teaser);
}
