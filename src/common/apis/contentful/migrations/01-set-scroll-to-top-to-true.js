module.exports = async function (migration, { makeRequest }) {
  async function fetchEntries(skip = 0, limit = 1000) {
    const response = await makeRequest({
      method: 'GET',
      url: `/entries?content_type=page&skip=${skip}&limit=${limit}&fields.scrollToTop[exists]=false`,
    });
    return response;
  }

  async function updateScrollToTop() {
    let skip = 0;
    const limit = 1000;
    let hasMoreEntries = true;

    while (hasMoreEntries) {
      const response = await fetchEntries(skip, limit);
      const entries = response.items;

      for (const entry of entries) {
        // Set scrollToTop field to true
        entry.fields.scrollToTop = { de: true };

        try {
          const updatedEntry = await makeRequest({
            method: 'PUT',
            url: `/entries/${entry.sys.id}`,
            headers: {
              'X-Contentful-Version': entry.sys.version,
            },
            data: entry,
          });
          console.log(`Updated entry ${updatedEntry.sys.id} successfully.`);
        } catch (error) {
          console.error(`Failed to update entry ${entry.sys.id}:`, error);
        }
      }

      skip += limit;
      hasMoreEntries = entries.length === limit;
    }
  }

  try {
    await updateScrollToTop();
    console.log('Update of scrollToTop field completed successfully.');
  } catch (error) {
    console.error('Error during update:', error);
  }
};
