/** Runs multiple content-typ migrations
 *  exec with: node <scrip-path> --spaceId <space-id> --environmentId <environment-id> --accessToken <access-token>
 */
const { runMigration } = require('contentful-migration');

const { accessToken, environmentId, spaceId } = require('./utils/cli-args');

function withInternalNameMigrationFunction() {
  return (migration) => {
    const contentType = migration.editContentType('textIconTeaserComponent');

    contentType.createField('internalName').name('Internal Name').type('Symbol').required(true);

    contentType.moveField('internalName').beforeField('icon');

    migration.transformEntries({
      contentType: 'textIconTeaserComponent',
      from: ['headline'],
      to: ['internalName'],
      transformEntryForLocale: function (fromFields, currentLocale) {
        return { internalName: fromFields.headline?.[currentLocale] ?? 'Bitte pflegen' };
      },
    });
  };
}

function withEntryTitleMigrationFunction() {
  return (migration) => {
    migration.editContentType('textIconTeaserComponent', {
      displayField: 'internalName',
    });
  };
}

/** Migration context used to call contentful mta endpoint
 *  @param {ReturnType<typeof withMigrationFunction>} migrationFunction - migration function to use
 */
function withOptions(migrationFunction) {
  return {
    migrationFunction,
    spaceId,
    accessToken,
    environmentId,
  };
}

async function migrate() {
  await runMigration(withOptions(withInternalNameMigrationFunction())).catch((e) => {
    console.error(e);
  });
  await runMigration(withOptions(withEntryTitleMigrationFunction()));
}

if (spaceId || environmentId || accessToken) {
  migrate();
}
