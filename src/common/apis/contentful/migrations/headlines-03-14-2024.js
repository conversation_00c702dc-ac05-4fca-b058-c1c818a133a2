/** Runs multiple content-typ migrations
 *  exec with: node <scrip-path> --spaceId <space-id> --environmentId <environment-id> --accessToken <access-token>
 */
const { runMigration } = require('contentful-migration');

const { accessToken, environmentId, spaceId } = require('./utils/cli-args');

const headlines = {
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
};

/** Migrates a single content type
 *  @param {string} contentTypeId - content type id in contentful
 *  @param {string} afterField - insert new headline tag field after this field id
 *  @param {keyof headlines} [defaultValue=h2] - headline default value
 */
function withMigrationFunction(contentTypeId, afterField, defaultValue = headlines.h2) {
  return (migration) => {
    const contentType = migration.editContentType(contentTypeId);

    contentType
      .editField('headlineTag')
      .validations([
        {
          in: Object.keys(headlines),
        },
      ])
      .defaultValue({
        de: defaultValue,
      });

    contentType.moveField('headlineTag').afterField(afterField);

    migration.transformEntries({
      contentType: contentTypeId,
      from: ['headlineTag'],
      to: ['headlineTag'],
      transformEntryForLocale: function () {
        return { headlineTag: defaultValue };
      },
    });
  };
}

/** Migration context used to call contentful mta endpoint
 *  @param {ReturnType<typeof withMigrationFunction>} migrationFunction - migration function to use
 */
function withOptions(migrationFunction) {
  return {
    migrationFunction,
    spaceId,
    accessToken,
    environmentId,
    yes: true,
  };
}

async function migrate() {
  /* Modules */
  await runMigration(withOptions(withMigrationFunction('accordion', 'headline')));
  await runMigration(withOptions(withMigrationFunction('areaSitesModule', 'title')));
  await runMigration(withOptions(withMigrationFunction('availabilityCheckModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('downloadModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('form', 'headline')));
  await runMigration(withOptions(withMigrationFunction('gridModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('infoGraphicModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('interactiveMapModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('productBannerModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('regionOverviewModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('sealBarModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('serviceAndContactModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('shopfinderModule', 'title')));
  await runMigration(withOptions(withMigrationFunction('sliderModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('tariffCarouselModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('tariffOverviewModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('tariffTeaserModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textButtonModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textImageButtonModule', 'headline')));
  await runMigration(withOptions(withMigrationFunction('videoModule', 'headline')));

  /* Components */
  await runMigration(withOptions(withMigrationFunction('explanationBox', 'headline')));
  await runMigration(withOptions(withMigrationFunction('product', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textButtonTeaser', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textIconTeaserComponent', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textImageButtonTeaserVertical', 'headline')));
  await runMigration(withOptions(withMigrationFunction('textTeaserComponent', 'headline')));
}

if (spaceId || environmentId || accessToken) {
  migrate();
}
