require('dotenv').config({ path: '../../../../.env.local' });
const contentful = require('contentful-management');

module.exports = async function (migration, _context) {
  const client = contentful.createClient({
    accessToken: process.env.CONTENTFUL_CMA_TOKEN, // Replace with your management API token
  });

  const spaceId = process.env.CONTENTFUL_SPACE_ID; // Replace with your space ID
  const environmentId = process.env.CONTENTFUL_ENVIRONMENT; // Replace with your environment ID
  const contentTypeId = 'textImageButtonModule'; // The target content type
  const richTextFieldId = 'text'; // The Rich Text field to inspect, make sure this matches the field ID you're working with

  // const entries = await client
  //   .getSpace(spaceId)
  //   .then((space) => space.getEnvironment(environmentId))
  //   .then((environment) => environment.getEntries({ limit: 500, content_type: 'page' }));

  // console.log(entries);

  migration.transformEntries({
    contentType: contentTypeId,
    from: ['text'],
    to: ['text'],
    transformEntryForLocale: async (fromFields, locale, context) => {
      if (JSON.stringify(fromFields) === '{}') {
        return;
      }

      const richTextField = fromFields['text']?.[locale];

      for (const block of richTextField.content) {
        for (const node of block.content) {
          const index = block.content.indexOf(node);
          if (node.nodeType === 'hyperlink' && node.data?.uri.startsWith('https://www.deutsche-glasfaser.de')) {
            const path = `/${node.data.uri.split('/').slice(3).join('/')}`.replace(/\/$/, '');

            if (path.startsWith('/blog')) {
              // console.log(`Skipping hyperlink in text teaser with path: ${path}`);
              return;
            }

            // console.log(`Found hyperlink in text icon teaser with path: ${path}`);

            const entry = await client
              .getSpace(spaceId)
              .then((space) => space.getEnvironment(environmentId))
              .then((environment) => environment.getEntry(context.id));

            // const references = await entry.references();
            console.log('internalName:', entry.fields.internalName?.[locale]);
            console.log('is published:', entry.isPublished());
            // console.log('references:', references);

            // const pageEntry = entries.items.find((entry) => {
            //   // console.log(entry.fields.slug?.[locale]);
            //   // console.log(path);
            //   return entry.fields.slug?.[locale] === path;
            // });
            // if (pageEntry) {
            //   console.log(
            //     `Replacing hyperlink ${node.data?.uri} with entry-hyperlink to entry ID: ${pageEntry.sys.id}`,
            //   );
            //
            //   // Update the node to be an entry-hyperlink
            //   block.content[index] = {
            //     ...node,
            //     nodeType: 'entry-hyperlink',
            //     data: {
            //       target: { sys: { type: 'Link', linkType: 'Entry', id: pageEntry.sys.id } },
            //     },
            //   };
            // } else {
            //   console.log(`No matching entries found for path ${path}`);
            // }
          }
        }
      }

      return { [richTextFieldId]: { [locale]: richTextField } };
    },
  });
};
