/** Runs multiple content-typ migrations
 *  exec with: node <scrip-path> --spaceId <space-id> --environmentId <environment-id> --accessToken <access-token>
 */
const { runMigration } = require('contentful-migration');

const { accessToken, environmentId, spaceId } = require('./utils/cli-args');

const variants = {
  Produkt: 'Produkt',
  Tarif: 'Tarif',
};

/** Migrates a single content type */
function withProductMigrationFunction() {
  return (migration) => {
    const contentType = migration.editContentType('product');

    contentType
      .createField('variant')
      .name('Variant')
      .type('Symbol')
      .validations([
        {
          in: Object.keys(variants),
        },
      ])
      .defaultValue({
        de: variants.Produkt,
      })
      .required(true);

    contentType.moveField('variant').afterField('theme');

    migration.transformEntries({
      contentType: 'product',
      from: ['variant'],
      to: ['variant'],
      transformEntryForLocale: function () {
        return { variant: variants.Produkt };
      },
    });
  };
}

/** Migration context used to call contentful mta endpoint
 *  @param {ReturnType<typeof withMigrationFunction>} migrationFunction - migration function to use
 */
function withOptions(migrationFunction) {
  return {
    migrationFunction,
    spaceId,
    accessToken,
    environmentId,
  };
}

async function migrate() {
  await runMigration(withOptions(withProductMigrationFunction()));
}

if (spaceId || environmentId || accessToken) {
  migrate();
}
