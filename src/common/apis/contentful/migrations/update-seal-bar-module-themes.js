module.exports = function (migration) {
  migration.transformEntries({
    contentType: 'sealBarModule',
    from: ['backgroundColor'],
    to: ['theme'],
    transformEntryForLocale: function (fromFields, currentLocale) {
      // Only process the de locale
      if (currentLocale !== 'de') {
        return;
      }

      if (!fromFields.backgroundColor || !fromFields.backgroundColor[currentLocale]) {
        return;
      }

      const backgroundColor = fromFields.backgroundColor[currentLocale];
      let newTheme;

      // Map backgroundColor values to theme values
      switch (backgroundColor) {
        case 'gray':
          newTheme = 'gray';
          break;
        case 'basalt':
          newTheme = 'basalt';
          break;
        case 'sand':
          newTheme = 'sand';
          break;
        default:
          // If it's an unknown value, default to sand
          newTheme = 'sand';
          break;
      }

      console.log(
        `Transforming sealBarModule entry (locale: ${currentLocale}): backgroundColor "${backgroundColor}" -> theme "${newTheme}"`,
      );

      return {
        theme: newTheme,
      };
    },
  });

  console.log('Migration to update sealBarModule themes from backgroundColor has been defined.');
};
