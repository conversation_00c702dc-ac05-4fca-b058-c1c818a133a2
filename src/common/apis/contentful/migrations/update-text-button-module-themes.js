module.exports = function (migration) {
  migration.transformEntries({
    contentType: 'textButtonModule',
    from: ['theme'],
    to: ['theme'],
    transformEntryForLocale: function (fromFields, currentLocale) {
      // Only process the de locale
      if (currentLocale !== 'de') {
        return;
      }

      if (!fromFields.theme || !fromFields.theme[currentLocale]) {
        return;
      }

      const currentTheme = fromFields.theme[currentLocale];
      let newTheme;

      switch (currentTheme) {
        case 'default':
          newTheme = 'sand';
          break;
        case 'light':
          newTheme = 'gray';
          break;
        case 'dark':
          newTheme = 'basalt';
          break;
        default:
          // If it's already the correct value or an unknown value, don't change it
          return;
      }

      console.log(`Transforming theme for entry (locale: ${currentLocale}): ${currentTheme} -> ${newTheme}`);

      return {
        theme: newTheme,
      };
    },
  });

  console.log('Migration to update textButtonModule themes has been defined.');
};
