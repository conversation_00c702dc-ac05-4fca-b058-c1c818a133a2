module.exports = function (migration) {
  migration.transformEntries({
    contentType: 'video',
    from: ['backgroundTheme'],
    to: ['theme'],
    transformEntryForLocale: function (fromFields, currentLocale) {
      // Only process the de locale
      if (currentLocale !== 'de') {
        return;
      }

      if (!fromFields.backgroundTheme || !fromFields.backgroundTheme[currentLocale]) {
        return;
      }

      const backgroundTheme = fromFields.backgroundTheme[currentLocale];
      let newTheme;

      // Map backgroundTheme values to theme values (converting to lowercase)
      switch (backgroundTheme) {
        case 'Gray':
          newTheme = 'gray';
          break;
        case 'Basalt':
          newTheme = 'basalt';
          break;
        case 'Sand':
          newTheme = 'sand';
          break;
        case 'gray':
          newTheme = 'gray';
          break;
        case 'basalt':
          newTheme = 'basalt';
          break;
        case 'sand':
          newTheme = 'sand';
          break;
        default:
          // If it's an unknown value, default to sand
          newTheme = 'sand';
          break;
      }

      console.log(
        `Transforming video entry (locale: ${currentLocale}): backgroundTheme "${backgroundTheme}" -> theme "${newTheme}"`,
      );

      return {
        theme: newTheme,
      };
    },
  });

  console.log('Migration to update video themes from backgroundTheme has been defined.');
};
