module.exports = function (migration) {
  migration.transformEntries({
    contentType: 'videoModule',
    from: ['theme'],
    to: ['theme'],
    transformEntryForLocale: function (fromFields, currentLocale) {
      // Only process the de locale
      if (currentLocale !== 'de') {
        return;
      }

      if (!fromFields.theme || !fromFields.theme[currentLocale]) {
        return;
      }

      const currentTheme = fromFields.theme[currentLocale];
      let newTheme;

      switch (currentTheme) {
        case 'Gray':
          newTheme = 'gray';
          break;
        case 'Basalt':
          newTheme = 'basalt';
          break;
        case 'Sand':
          newTheme = 'sand';
          break;
        default:
          // If it's already the correct value or an unknown value, don't change it
          return;
      }

      console.log(
        `Transforming theme for videoModule entry (locale: ${currentLocale}): ${currentTheme} -> ${newTheme}`,
      );

      return {
        theme: newTheme,
      };
    },
  });

  console.log('Migration to update videoModule themes has been defined.');
};
