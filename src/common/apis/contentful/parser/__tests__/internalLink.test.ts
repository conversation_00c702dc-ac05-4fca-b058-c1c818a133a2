import type { InternalLinkComponent } from '@/apis/contentful/generated/types';

import { parseInternalLinkData } from '../internalLink';

describe('parseInternalLinkData', () => {
  it('returns correct internal link with page and module', () => {
    const input = {
      page: { slug: 'test-page' },
      module: { internalName: 'TestModule' },
      internalName: 'Test Link',
    } as Partial<InternalLinkComponent>;

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Test Link',
      href: 'test-page#testmodule',
    });
  });

  it('returns correct internal link with page and module with sanitized internal name', () => {
    const input = {
      page: { slug: 'test-page' },
      module: { internalName: '-TestäÜöÖß--123Module- ' },
      internalName: 'Test Link',
    } as Partial<InternalLinkComponent>;

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Test Link',
      href: 'test-page#testaeueoeoess-123module',
    });
  });

  it('returns correct internal link with only module', () => {
    const input = {
      module: { internalName: 'TestModule' },
      internalName: 'Test Link',
    } as Partial<InternalLinkComponent>;

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Test Link',
      href: '#testmodule',
    });
  });

  it('returns correct internal link with only page', () => {
    const input = {
      page: { slug: 'test-page' },
      internalName: 'Test Link',
    } as Partial<InternalLinkComponent>;

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Test Link',
      href: 'test-page',
    });
  });

  it('returns empty href when no slug or id', () => {
    const input = {
      internalName: 'Test Link',
    } as Partial<InternalLinkComponent>;

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Test Link',
      href: '',
    });
  });

  it('uses default label when internalName is missing', () => {
    const input = {};

    expect(parseInternalLinkData(input)).toEqual({
      label: 'Label',
      href: '',
    });
  });

  it('handles null input', () => {
    expect(parseInternalLinkData(null)).toEqual({
      label: 'Label',
      href: '',
    });
  });
});
