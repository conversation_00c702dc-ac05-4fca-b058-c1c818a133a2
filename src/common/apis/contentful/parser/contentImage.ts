import type { Asset } from '@/apis/contentful/generated/types';

import type { ContentImage } from '@/types/contentImage';
import type { ContentMedia } from '@/types/contentMedia';

export function parseContentfulContentImage(asset?: Partial<Asset> | null): ContentImage {
  const imagePlaceholder =
    'https://images.ctfassets.net/cfwjf2vnzww5/3ZbfyT2Y5l2yKbTwQdtT9n/1a7d79d7dc5636e03762a0aba620d381/960x640.svg';

  return {
    src: asset?.url || imagePlaceholder,
    alt: asset?.title || 'Alt placeholder',
    width: asset?.width || 960,
    height: asset?.height || 640,
  };
}

export function parseContentfulContentIcon(asset?: Partial<Asset> | null): ContentImage {
  const imagePlaceholder =
    'https://images.ctfassets.net/cfwjf2vnzww5/3ZbfyT2Y5l2yKbTwQdtT9n/1a7d79d7dc5636e03762a0aba620d381/960x640.svg';

  return {
    src: asset?.url || imagePlaceholder,
    alt: asset?.title || 'Alt placeholder',
    width: asset?.width || 75,
    height: asset?.height || 75,
  };
}

export function parseContentfulContentMedia(asset?: Partial<Asset> | null): ContentMedia {
  const imagePlaceholder =
    'https://images.ctfassets.net/cfwjf2vnzww5/3ZbfyT2Y5l2yKbTwQdtT9n/1a7d79d7dc5636e03762a0aba620d381/960x640.svg';

  return {
    src: asset?.url || imagePlaceholder,
    alt: asset?.title || 'Alt placeholder',
    width: asset?.width || 75,
    height: asset?.height || 75,
    contentType: asset?.contentType || 'image/svg+xml',
  };
}
