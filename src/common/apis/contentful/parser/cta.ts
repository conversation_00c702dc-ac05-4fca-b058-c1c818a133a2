import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as C<PERSON><PERSON><PERSON><PERSON>,
  Modal as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextButtonTeaser,
  TextIconTeaserComponent,
  TextTeaserComponent,
} from '@/apis/contentful/generated/types';
import type { BasePage } from '@/apis/contentful/getPages';

import type { ModalTeaserProps } from '@/components/ModalTeaser';

import type { CTA } from '@/types/cta';
import type { Modal } from '@/types/modal';
import type { RichTextLinks } from '@/types/pageProps';
import type { TextAlignment } from '@/types/text';

import { parseContentfulContentIcon } from './contentImage';
import { parseExternalLinkData } from './externalLink';
import { parseInternalLinkData } from './internalLink';

export function parseCtaData(ctaData?: Partial<CtaEntry | ModalEntry | BasePage | AcFlyout> | null): CTA {
  const ctaType = ctaData?.__typename;

  switch (ctaType) {
    case 'Cta': {
      const label = ctaData?.label || 'CTA Label';
      const linkType = ctaData?.link?.__typename;
      const icon = ctaData?.icon ? parseContentfulContentIcon(ctaData.icon) : undefined;
      const variant =
        ctaData?.variant === 'secondary' ? 'secondary' : ctaData?.variant === 'tertiary' ? 'tertiary' : 'primary';

      switch (linkType) {
        case 'Page':
          return {
            type: 'Link',
            label,
            to: ctaData?.link?.slug || '',
            icon,
            variant,
          };
        case 'InternalLinkComponent': {
          const internalLink = parseInternalLinkData(ctaData?.link);
          return {
            type: 'Link',
            label,
            to: internalLink.href,
            icon,
            variant,
          };
        }
        case 'ExternalLinkComponent': {
          const externalLink = parseExternalLinkData(ctaData?.link);
          return {
            type: 'Link',
            label,
            to: externalLink.href,
            icon,
            variant,
          };
        }
        case 'Document': {
          return {
            type: 'Link',
            label,
            to: ctaData?.link?.document?.url ?? '',
            icon,
            variant,
          };
        }
        case 'AcFlyout': {
          const labelAfterAcPositive = ctaData?.link?.labelAfterAcPositive ?? undefined;
          const labelAfterAcNegative = ctaData?.link?.labelAfterAcNegative ?? undefined;
          return {
            type: 'Flyout',
            variant,
            label,
            labelAfterAcNegative,
            labelAfterAcPositive,
          };
        }
        default:
          return {
            type: 'Link',
            label,
            to: '',
            icon,
            variant,
          };
      }
    }
    case 'Modal': {
      const label = ctaData?.label || 'CTA Label';
      const linkType = ctaData?.content?.__typename;
      const type = 'Modal';

      switch (linkType) {
        case 'TextIconTeaserComponent':
        case 'TextTeaserComponent':
        case 'TextButtonTeaser': {
          const modal: Modal = {
            type: 'Teaser',
            content: {
              internalName: ctaData?.content?.internalName ?? 'undefined-teaser',
              textAlignment: (ctaData?.content?.textAlignment as 'left' | 'center' | 'right' | undefined) ?? 'left',
              links: ctaData?.content?.text?.links as unknown as RichTextLinks,
            },
          };
          return {
            type,
            label,
            to: modal,
          };
        }
        case 'GridModule': {
          const teaserCollection = ctaData?.content?.elementsCollection?.items;
          if (!teaserCollection) {
            const modal: Modal = { type: 'Grid', content: [] };
            return {
              type,
              label,
              to: modal,
            };
          }

          const teaserList: ModalTeaserProps[] = teaserCollection
            .filter(
              (teaser): teaser is TextIconTeaserComponent | TextTeaserComponent | TextButtonTeaser =>
                teaser?.__typename === 'TextIconTeaserComponent' ||
                teaser?.__typename === 'TextTeaserComponent' ||
                teaser?.__typename === 'TextButtonTeaser',
            )
            .map((teaser) => {
              return {
                internalName: teaser.internalName ?? 'undefined-teaser',
                variant: 'text',
                textAlignment: (teaser.textAlignment as TextAlignment) ?? 'left',
                links: (teaser.text?.links as unknown as RichTextLinks) ?? {
                  entries: { hyperlink: [], inline: [] },
                  assets: { hyperlink: [] },
                },
              };
            });

          const modal: Modal = { type: 'Grid', content: teaserList };

          return {
            type,
            label,
            to: modal,
          };
        }
        case 'Video': {
          const modal: Modal = { type: 'Video', content: ctaData?.content?.url || '' };
          return {
            type,
            label,
            to: modal,
          };
        }
        default:
          const modal: Modal = { type: 'Teaser', content: 'undefined' };
          return {
            type,
            label,
            to: modal,
          };
      }
    }
    case 'Page': {
      return { type: 'Link', label: 'Mehr erfahren', to: ctaData?.slug ?? '#', variant: 'primary' };
    }
    default:
      return { type: 'Link', label: 'CTA Label', to: '', variant: 'primary' };
  }
}
