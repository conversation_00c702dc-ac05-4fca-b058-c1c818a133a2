import type { ExternalLinkComponent } from '@/apis/contentful/generated/types';

import type { ExternalLink } from '@/types/externalLink';

export function parseExternalLinkData(externalLinkData?: Partial<ExternalLinkComponent> | null): ExternalLink {
  const url = externalLinkData?.href ?? null;
  const label = externalLinkData?.title ? externalLinkData.title : 'Label';
  const href = url && (url.startsWith('https://') || url.startsWith('/')) ? url : '#';

  return {
    label,
    href,
  };
}
