import type { <PERSON><PERSON>onfig, <PERSON>Field, Form<PERSON>ieldWidth, FormGroup, InputType } from '@/types/forms';
import type { SelectOption } from '@/types/selectOption';
import type { Tooltip } from '@/types/tooltip';

import type { ValidationType } from '@/utils/forms/formValidation';

import type { FormField as FormFieldEntry, FormFormElementsItem, Maybe } from '../generated/types';
import { parseContentfulContentImage } from './contentImage';

export function parseFormElementData(el: Maybe<FormFormElementsItem>): FormField | FormGroup | null {
  if (!el) return null;

  if (el.__typename === 'FormGroup') {
    const fields =
      el.formFieldsCollection?.items?.map(parseFormElementData).filter((f): f is FormField => f !== null) ?? [];

    return {
      headline: el.headline ?? undefined,
      label: el.label ?? undefined,
      description: el.description ?? undefined,
      images: el.imagesCollection?.items?.map((image) => parseContentfulContentImage(image)) ?? [],
      fields,
      isPersistent: el.isPersistent ?? false,
      conditions: el.conditions ?? [],
    };
  }

  if (el.__typename === 'FormField') {
    return parseFormFieldData(el);
  }

  return null;
}

function parseTooltip(tooltip: any): Tooltip | undefined {
  if (!tooltip?.title || !tooltip?.text) return undefined;
  return {
    title: tooltip.title,
    text: tooltip.text,
  };
}

export function parseFormFieldData(field: FormFieldEntry): FormField {
  return {
    label: field.label ?? '',
    fieldName: field.fieldName ?? '',
    inputType: (field.inputType as InputType) ?? 'text',
    required: field.required ?? false,
    isPersistent: field.isPersistent ?? false,
    isMultipleSelect: field.isMultipleSelect ?? false,
    placeholder: field.placeholder ?? '',
    width: (field.width as FormFieldWidth) ?? 'full',
    ...(field.options ? { options: field.options.map((option: any) => parseFormFieldOption(option)) } : {}),
    ...(field.validationRules ? { validationRules: field.validationRules } : {}),
    ...(field.tooltip ? { tooltip: parseTooltip(field.tooltip) } : {}),
  };
}

function parseFormFieldOption(option?: any): SelectOption {
  return {
    label: option?.label ?? '',
    value: option?.value ?? '',
  };
}

export function parseConfigFormElements(config: FormConfig[]): Array<FormField | FormGroup> {
  return config.map((element): FormField | FormGroup => {
    if (element.inputType === 'group' && element.fields) {
      return {
        label: element.label,
        isPersistent: element.isPersistent ?? false,
        fields: element.fields.map((field: FormConfig): FormField => {
          const baseField: FormField = {
            label: field.label,
            fieldName: field.fieldName,
            inputType: field.inputType as InputType,
            required: !!field.config?.required,
            defaultValue: field.defaultValue,
            isPersistent: field.isPersistent ?? false,
            isMultipleSelect: field.isMultipleSelect ?? false,
            width: field.width ?? 'full',
            ...(field.config?.required ? { validationRules: { required: field.config.required } } : {}),
            ...(field.tooltipTitle && field.tooltipText
              ? {
                  tooltip: {
                    title: field.tooltipTitle,
                    text: field.tooltipText,
                  },
                }
              : {}),
          };

          if (field.options) {
            baseField.options = field.options;
          }

          if (field.placeholder) {
            baseField.placeholder = field.placeholder;
          }

          if (field.infoBox) {
            baseField.infoBox = field.infoBox;
          }

          if (field.validation) {
            baseField.validation = field.validation as ValidationType;
          }

          if (field.dependency) {
            baseField.conditions = Array.isArray(field.dependency) ? field.dependency : [field.dependency];
          }

          return baseField;
        }),
        ...(element.dependency
          ? {
              conditions: Array.isArray(element.dependency) ? element.dependency : [element.dependency],
            }
          : {}),
      };
    }

    const baseField: FormField = {
      label: element.label,
      fieldName: element.fieldName,
      inputType: element.inputType as InputType,
      required: !!element.config?.required,
      isPersistent: element.isPersistent ?? false,
      isMultipleSelect: element.isMultipleSelect ?? false,
      defaultValue: element.defaultValue,
      width: element.width ?? 'full',
      ...(element.config?.required ? { validationRules: { required: element.config.required } } : {}),
      ...(element.tooltipTitle && element.tooltipText
        ? {
            tooltip: {
              title: element.tooltipTitle,
              text: element.tooltipText,
            },
          }
        : {}),
    };

    if (element.options) {
      baseField.options = element.options;
    }

    if (element.placeholder) {
      baseField.placeholder = element.placeholder;
    }

    if (element.infoBox) {
      baseField.infoBox = element.infoBox;
    }

    if (element.validation) {
      baseField.validation = element.validation as ValidationType;
    }

    if (element.dependency) {
      baseField.conditions = Array.isArray(element.dependency) ? element.dependency : [element.dependency];
    }

    return baseField;
  });
}
