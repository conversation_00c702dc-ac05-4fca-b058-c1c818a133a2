import type { InternalLinkComponent } from '@/apis/contentful/generated/types';

import type { InternalLink } from '@/types/internalLink';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';

export function parseInternalLinkData(internalLinkData?: Partial<InternalLinkComponent> | null): InternalLink {
  const slug = internalLinkData?.page?.slug ?? null;
  const id = internalLinkData?.module?.internalName ?? null;
  const label = internalLinkData?.internalName ? internalLinkData.internalName : 'Label';
  const href = buildHref(slug, id);

  return {
    label,
    href,
  };
}

function buildHref(slug: string | null, id: string | null): string {
  if (slug && id) {
    return `${slug}#${getSanitizedIdSelector(id)}`;
  }
  if (id) {
    return `#${getSanitizedIdSelector(id)}`;
  }
  if (slug) {
    return slug;
  }
  return '';
}
