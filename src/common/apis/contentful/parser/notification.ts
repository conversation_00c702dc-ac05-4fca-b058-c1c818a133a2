import type { Notification as NotificationEntry } from '@/apis/contentful/generated/types';
import type { Notification, NotificationType } from '@/apis/contentful/getPages';

export function parseNotificationData(notification?: Partial<NotificationEntry> | null): Notification | undefined {
  if (!notification) {
    return undefined;
  }

  return {
    id: notification?.sys?.id || '0',
    type: (notification?.type as NotificationType) || 'info',
    headline: notification?.headline || 'Headline',
    text: notification?.infotext || undefined,
    delay: notification?.delay || 0,
  };
}
