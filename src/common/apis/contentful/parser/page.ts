import { BusinessArea } from '@/app/consts';
import type { PageType } from '@/app/consts';

import type { Page as BasePageEntry, SeoMetadata as SeoMetadataEntry } from '@/apis/contentful/generated/types';
import type { BasePage } from '@/apis/contentful/getPages';
import { parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import { parseNotificationData } from '@/apis/contentful/parser/notification';
import { parseSeoData } from '@/apis/contentful/parser/seo';

export function parseContentfulPage(page?: Partial<BasePageEntry> | null): BasePage | null {
  if (!page || !page.slug) {
    return null;
  }

  return {
    __typename: 'Page',
    internalName: page.internalName || '',
    title: page.title || '',
    slug: page.slug || '/',
    businessArea: (page.businessArea || BusinessArea.Private) as BusinessArea,
    pageType: (page.pageType as PageType) || '',
    notification: parseNotificationData(page.notification),
    stage: page.stage,
    blocks: page.bodyCollection?.items ?? [],
    teaserText: page.teaserText || undefined,
    teaserImage: page.teaserImage ? parseContentfulContentImage(page.teaserImage) : undefined,
    seoData: parseSeoData(page.seoMetadata as SeoMetadataEntry),
    publishedAt: page.sys?.publishedAt,
    scrollToTop: page.scrollToTop || false,
    callButton: page.callButton || false,
  };
}
