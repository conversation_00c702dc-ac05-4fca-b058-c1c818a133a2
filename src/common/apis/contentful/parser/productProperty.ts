import type {
  ProductProductPropertyCollection,
  ProductProperty as ProductPropertyEntry,
} from '@/apis/contentful/generated/types';

import type { ProductProperty } from '@/components/ProductTeaser/types';
import { ProductPropertyType } from '@/components/ProductTeaser/types';

export function parseProductProperties(productPropertyCollection?: Partial<ProductProductPropertyCollection> | null) {
  return productPropertyCollection?.items
    ? productPropertyCollection?.items.map((item) => {
        return parseProductProperty(item);
      })
    : [];
}

function parseProductProperty(prop?: Partial<ProductPropertyEntry> | null): ProductProperty {
  return {
    type: (prop?.type as ProductPropertyType) || ProductPropertyType.Benefit,
    text: prop?.text || 'Option',
    legalText: prop?.legalText || undefined,
  };
}
