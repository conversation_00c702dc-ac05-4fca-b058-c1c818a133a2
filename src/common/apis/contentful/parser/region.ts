import type { Region as RegionEntry } from '@/apis/contentful/generated/types';

import type { Region } from '@/types/region';

export function parseRegion(region?: Partial<RegionEntry> | null): Region {
  return {
    name: region?.name || 'State',
    text: region?.text || 'Description',
    image: region?.image?.url || undefined,
    linkHref:
      region?.link?.__typename === 'Page' && region.link.slug
        ? region.link.slug
        : region?.link?.__typename === 'ExternalLinkComponent' && region.link.href
          ? region.link.href
          : '#',
  };
}
