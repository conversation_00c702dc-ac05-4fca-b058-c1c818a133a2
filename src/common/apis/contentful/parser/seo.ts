import type { SeoMetadata } from '@/types/seo';

import type { SeoMetadata as SeoMetadataEntry } from '../generated/types';

export function parseSeoData(seoData?: Partial<SeoMetadataEntry> | null): SeoMetadata {
  return {
    title: seoData?.seoTitle || null,
    description: seoData?.description || null,
    keywords: (seoData?.keywords || []) as string[],
    noIndex: seoData?.noIndex || false,
    noFollow: seoData?.noFollow || false,
  };
}
