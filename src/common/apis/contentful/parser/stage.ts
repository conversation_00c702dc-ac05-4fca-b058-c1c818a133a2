import type {
  Badge as BadgeEntry,
  Banner as BannerEntry,
  CampaignStage as CampaignStageEntry,
  ContentStage as ContentStageEntry,
  CrossLinksComponent,
  DefaultStage as DefaultStageEntry,
  ExplanationBox,
} from '@/apis/contentful/generated/types';
import { parseContentfulContentImage, parseContentfulContentMedia } from '@/apis/contentful/parser/contentImage';
import { parseCtaData } from '@/apis/contentful/parser/cta';
import { parseLegalNote } from '@/apis/contentful/parser/legalNote';
import type { Provider } from '@/apis/dg/types';

import type { CampaignStageProps } from '@/components/Stages/CampaignStage';
import type { BannerProps } from '@/components/Stages/CampaignStage/Banner';
import type { BannerBadgeTextSize } from '@/components/Stages/CampaignStage/Banner/BannerBadge/BannerBadgeText';
import type { CampaignColor } from '@/components/Stages/CampaignStage/Banner/types';
import type { ContentStageProps } from '@/components/Stages/ContentStage';
import type { CrossLinkBoxProps } from '@/components/Stages/ContentStage/CrossLinkBox';
import type { ExplanationBoxProps } from '@/components/Stages/ContentStage/ExplanationBox';
import type { DefaultStageProps } from '@/components/Stages/DefaultStage';

import type { LegalNotification } from '@/types/legalNote';

export type ImagePosition = 'top' | 'bottom';

export function parseStageData(
  stageData?: Partial<DefaultStageEntry | ContentStageEntry | CampaignStageEntry> | null,
): DefaultStageProps | ContentStageProps | CampaignStageProps | null {
  if (!stageData) {
    return null;
  }

  switch (stageData.__typename) {
    case 'DefaultStage':
      return parseDefaultStageData(stageData);
    case 'ContentStage':
      return parseContentStageData(stageData);
    case 'CampaignStage':
      return parseCampaignStageData(stageData);
    default:
      return null;
  }
}

export function parseDefaultStageData(stageData?: Partial<DefaultStageEntry> | null): DefaultStageProps | null {
  if (!stageData) {
    return null;
  }

  return {
    internalName: stageData.internalName || 'undefined-default-stage',
    layout: (stageData.layout as 'split' | 'full') || 'split',
    headline: stageData.headline || undefined,
    text: stageData.text || undefined,
    textColor: (stageData.textColor as 'basalt' | 'sand') || 'Basalt',
    subline: stageData?.subline || undefined,
    cta: stageData.cta ? parseCtaData(stageData.cta) : undefined,
    additionalCta: stageData.additionalCta ? parseCtaData(stageData.additionalCta) : undefined,
    ctaAlignment: stageData.ctaAlignment === 'horizontal' ? 'horizontal' : 'vertical',
    autoPlay: stageData.autoPlay || false,
    loop: stageData.loop || false,
    mediaDesktop: parseContentfulContentMedia(stageData.mediaDesktop),
    mediaMobile: stageData.mediaMobile
      ? parseContentfulContentMedia(stageData.mediaMobile)
      : parseContentfulContentMedia(stageData.mediaDesktop),
    imagePosition: (stageData.imagePosition as ImagePosition) || undefined,
  };
}

export function parseContentStageData(stageData?: Partial<ContentStageEntry> | null): ContentStageProps | null {
  if (!stageData) {
    return null;
  }

  return {
    internalName: stageData.internalName || 'undefined-stage',
    headline: stageData.headline || 'Headline',
    text: stageData.text || undefined,
    image: parseContentfulContentImage(stageData?.image),
    imagePosition: (stageData.imagePosition as ImagePosition) || undefined,
    crossLinks: parseCrossLinksData(stageData.linkBox) || undefined,
    explanation: parseExplanationData(stageData.explanationBox) || undefined,
  };
}

export function parseCampaignStageData(stageData?: Partial<CampaignStageEntry> | null): CampaignStageProps | null {
  if (!stageData) {
    return null;
  }

  return {
    internalName: stageData.internalName || 'undefined-campaign-stage',
    provider: (stageData.provider as Provider) || 'DGH',
    campaignId: stageData.campaignId || '20231000', // Default campaign id
    campaignFixed: stageData.campaignFixed || false,
    headline: stageData.headline || 'Headline',
    text: stageData.text || undefined,
    image: parseContentfulContentImage(stageData.image),
    imagePosition: (stageData.imagePosition as ImagePosition) || undefined,
    cta: parseCtaData(stageData.cta),
    additionalCta: stageData.additionalCta ? parseCtaData(stageData?.additionalCta) : undefined,
    ctaAlignment: (stageData.ctaAlignment as 'horizontal' | 'vertical' | undefined) || 'vertical',
    banner: stageData.banner ? parseBannerData(stageData.banner) : undefined,
    layout: (stageData.layout as 'split' | 'splitSmall') || 'split',
  };
}

export type Badge = {
  topText?: string;
  centerText: string;
  centerTextSize: BannerBadgeTextSize;
  bottomText?: string;
  color: CampaignColor;
  legalNote?: LegalNotification;
};

function parseBadgeData(badgeData?: Partial<BadgeEntry> | null): Badge {
  return {
    topText: badgeData?.topText || undefined,
    centerText: badgeData?.centerText || 'Text',
    centerTextSize: (badgeData?.centerTextSize as BannerBadgeTextSize) || 'small',
    bottomText: badgeData?.bottomText || undefined,
    color: (badgeData?.color as CampaignColor) || 'Sand',
    legalNote: badgeData?.legalNote ? parseLegalNote(badgeData.legalNote) : undefined,
  };
}

function parseBannerData(bannerData?: Partial<BannerEntry> | null): BannerProps {
  return {
    primaryBadge: parseBadgeData(bannerData?.primaryBadge),
    secondaryBadge: bannerData?.secondaryBadge ? parseBadgeData(bannerData?.secondaryBadge) : undefined,
    backgroundColor: (bannerData?.color as CampaignColor) || '',
  };
}

function parseCrossLinksData(crossLinksData?: Partial<CrossLinksComponent> | null): CrossLinkBoxProps {
  return {
    headline: crossLinksData?.headline || 'Headline',
    links: crossLinksData?.linksCollection?.items.map((data) => parseCtaData(data)) || [],
  };
}

function parseExplanationData(explanationData?: Partial<ExplanationBox> | null): ExplanationBoxProps {
  return {
    headline: explanationData?.headline || 'Headline',
    headlineTag: explanationData?.headlineTag || 'h2',
    text: explanationData?.explanationText || undefined,
  };
}
