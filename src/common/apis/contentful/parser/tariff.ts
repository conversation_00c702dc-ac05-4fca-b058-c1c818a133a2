import type { TariffTeaserProps } from '@/modules/TariffOverview/TariffTable/TariffTeaser';

import { Provider, TariffTeaserTheme } from '@/apis/dg/types';
import type { TariffOption } from '@/apis/dg/types';

import type { Maybe, Product } from '../generated/types';

export function parseTariff(product: Maybe<Product> | null, index: number): TariffTeaserProps {
  const themeMapping: { [key: string]: TariffTeaserTheme } = {
    Basalt: TariffTeaserTheme.Basalt,
    Green: TariffTeaserTheme.Gras,
    Yellow: TariffTeaserTheme.Flower,
    Blue: TariffTeaserTheme.Heaven,
  };

  const bandwidth = product?.headline?.match(/\d+$/);
  const name = product?.headline?.slice(0, bandwidth?.index) ?? 'Headline';
  const tariffNameDashed = product?.headline
    ?.split(' ')
    .map((segment) => segment.toLowerCase())
    .join('-');

  return {
    index,
    name,
    provider: Provider.Business,
    bandwidth: bandwidth?.[0] ?? 'Null',
    theme: themeMapping[(product?.theme ?? 'Basalt') as keyof typeof themeMapping],
    twoYearTariffDetails: {
      cta: {
        label: product?.cta?.internalName ?? 'Label',
        to: product?.cta?.page?.slug ?? '#',
        type: 'Link',
      },
      promotions: [],
      tariffOptions: product?.productPropertyCollection?.items.map((productProperty) => {
        return {
          title: productProperty?.title,
          optionType: productProperty?.type === 'Benefit' ? 1 : 2,
          text: productProperty?.text?.trim(),
          legalNote: productProperty?.legalText
            ? {
                title: 'Hinweis',
                text: productProperty.legalText,
              }
            : undefined,
        };
      }) as TariffOption[],
      scaleAttributes: [],
      tariffDetailsLink: `/geschaeftskunden/business/tarife?tarif=${tariffNameDashed}#${tariffNameDashed}`,
    },
  };
}
