import type {
  TextButtonTeaser,
  TextIconTeaserComponent,
  TextImageButtonTeaserVertical,
  TextTeaserComponent,
} from '@/apis/contentful/generated/types';
import { parseContentfulContentIcon, parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import { parseCtaData } from '@/apis/contentful/parser/cta';

import type { TeaserProps } from '@/components/Teaser';

import type { RichTextLinks } from '@/types/pageProps';
import type { LineClampCount, TeaserTextVariant } from '@/types/teaser';
import { Theme } from '@/types/theme';

import { parseTheme } from './theme';

export function parseBlockToTeaser(
  block: (TextImageButtonTeaserVertical | TextButtonTeaser | TextIconTeaserComponent | TextTeaserComponent) & {
    gridTheme?: Theme;
  },
): TeaserProps {
  const blockType = block.__typename;
  const teaserData: TeaserProps = {
    internalName: block.internalName ?? 'undefined-teaser',
    variant: 'text',
    textAlignment: (block.textAlignment as 'left' | 'center' | 'right' | undefined) ?? 'left',
    links: block.text?.links as unknown as RichTextLinks,
  };

  if (blockType === 'TextImageButtonTeaserVertical') {
    const linkType = block.cta?.link?.__typename;

    if (block.image) {
      teaserData.image = parseContentfulContentImage(block.image);
    } else if (block.cta && block.cta.link && linkType === 'Page') {
      teaserData.image = parseContentfulContentImage(block.cta.link.teaserImage);
    }
    teaserData.variant = 'image';
    teaserData.headline = block.headline ?? (linkType === 'Page' ? (block.cta?.link?.title ?? undefined) : undefined);
    teaserData.headlineTag = block.headlineTag ?? 'h2';
    teaserData.text = (block.text ??
      (linkType === 'Page' ? (block.cta?.link?.teaserText ?? undefined) : undefined)) as TeaserTextVariant;
    teaserData.cta = block.cta ? parseCtaData(block.cta) : undefined;
    teaserData.hoverText = block.hoverText ?? false;
    teaserData.ratio = block.ratio as '3/2' | '21/9' | undefined;
    teaserData.theme = Theme.Gray;
    teaserData.hasInnerPadding = true;
  }

  if (blockType === 'TextButtonTeaser') {
    teaserData.variant = 'text';
    teaserData.headline = block.headline ?? undefined;
    teaserData.headlineTag = block.headlineTag ?? 'h2';
    teaserData.text = (block.text ?? 'Teaser Text') as TeaserTextVariant;
    teaserData.hasInnerPadding = block.gridTheme !== parseTheme(block.theme);
    teaserData.theme = parseTheme(block.theme);
    teaserData.cta = parseCtaData(block.cta);
  }

  if (blockType === 'TextTeaserComponent') {
    teaserData.variant = 'text';
    teaserData.headline = block.headline ?? undefined;
    teaserData.headlineTag = block.headlineTag ?? 'h2';
    teaserData.text = block.text ?? undefined;
    teaserData.mobileLineClampCount = block.mobileLineClampCount as LineClampCount;
    teaserData.theme = block.gridTheme;
  }

  if (blockType === 'TextIconTeaserComponent') {
    teaserData.variant = 'icon';
    teaserData.image = parseContentfulContentIcon(block.icon);
    teaserData.headline = block.headline ?? undefined;
    teaserData.headlineTag = block.headlineTag ?? 'h2';
    teaserData.text = block.text as TeaserTextVariant;
    teaserData.theme = parseTheme(block.theme);
    teaserData.hasInnerPadding = block.gridTheme !== parseTheme(block.theme);
    teaserData.cta = block.cta ? parseCtaData(block.cta) : undefined;
    teaserData.phonenumber = block.phonenumber ?? '';
    teaserData.hotlineList = block.hotlineList ?? undefined;
  }

  return teaserData;
}
