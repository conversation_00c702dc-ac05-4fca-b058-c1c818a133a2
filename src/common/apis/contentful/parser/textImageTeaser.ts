import type { TextImageTeaserProps } from '@/modules/TextImageTeaser/index.types';
import { TextImageTeaserImagePosition } from '@/modules/TextImageTeaser/index.types';

import { parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import { parseCtaData } from '@/apis/contentful/parser/cta';
import { parseTheme } from '@/apis/contentful/parser/theme';

import type { HeadlineProps } from '@/components/Headline';

import type { TextImageButtonModule } from '../generated/types';

export function parseTextImageTeaser(teaser?: Partial<TextImageButtonModule> | null): TextImageTeaserProps {
  const cta = teaser?.cta;
  const isValidCta = cta?.__typename === 'Cta';
  const ctaLink = isValidCta ? cta.link : null;
  const isValidCtaPage = ctaLink?.__typename === 'Page';

  // Extract fallback data from CTA page if available
  const ctaPageFallback =
    isValidCta && isValidCtaPage
      ? {
          title: ctaLink.title,
          teaserText: ctaLink.teaserText,
          teaserImage: ctaLink.teaserImage,
        }
      : null;

  return {
    internalName: teaser?.internalName ?? 'text-bild-teaser',
    headline: teaser?.headline ?? ctaPageFallback?.title ?? undefined,
    headlineTag: teaser?.headlineTag as HeadlineProps['intent'] | undefined,
    text: teaser?.text ?? ctaPageFallback?.teaserText ?? undefined,
    image: parseContentfulContentImage(teaser?.image ?? ctaPageFallback?.teaserImage ?? undefined),
    imagePosition:
      teaser?.imagePosition === 'left' ? TextImageTeaserImagePosition.Left : TextImageTeaserImagePosition.Right,
    theme: parseTheme(teaser?.theme),
    cta: cta ? parseCtaData(cta) : undefined,
    additionalCta: teaser?.additionalCta ? parseCtaData(teaser.additionalCta) : undefined,
    ctaAlignment: teaser?.ctaAlignment === 'horizontal' ? 'horizontal' : 'vertical',
  };
}
