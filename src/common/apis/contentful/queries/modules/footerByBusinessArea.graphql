query FooterByBusinessArea($preview: <PERSON>olean, $businessArea: String!) {
  footerCollection(preview: $preview, limit: 1, where: { businessArea: $businessArea }) {
    items {
      internalName
      businessArea
      linkBlockCollection(limit: 4) {
        items {
          name
          pagesCollection(limit: 8) {
            items {
              ... on Page {
                __typename
                title
                slug
              }
              ... on ExternalLinkComponent {
                __typename
                title
                href
              }
            }
          }
        }
      }
      socialMediaCollection(limit: 6) {
        items {
          title
          url
          icon {
            url
            width
            height
          }
        }
      }
    }
  }
}
