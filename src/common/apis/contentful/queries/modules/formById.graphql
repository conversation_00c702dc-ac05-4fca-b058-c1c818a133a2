query FormById($id: String!, $preview: Boolean!) {
  form(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    headline
    headlineTag
    # introText is deprecated, use introTextRt instead
    introText
    introTextRt {
      __typename
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    infoTeaserCollection(limit: 5) {
      items {
        ...TextIconTeaserFragment
      }
    }
    recipient
    receiver {
      ...EntryFragment # we only fetch the id here, so we can use it on the server to query the form recipient later
    }
    formElementsCollection(limit: 20) {
      items {
        ... on FormField {
          ...FormFieldFragment
        }
        ... on FormGroup {
          ...FormGroupFragment
        }
      }
    }
    formType
    config
  }
}
