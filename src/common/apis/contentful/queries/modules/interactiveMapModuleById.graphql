query InteractiveMapModuleById($id: String!, $preview: Boolean!) {
  interactiveMapModule(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    headline
    headlineTag
    introText {
      __typename
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    theme
    regionsCollection(limit: 16, preview: $preview) {
      items {
        ...RegionFragment
      }
    }
  }
}
