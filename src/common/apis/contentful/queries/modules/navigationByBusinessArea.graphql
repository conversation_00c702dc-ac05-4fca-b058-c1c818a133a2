query NavigationByBusinessArea($businessArea: String!, $preview: Boolean!) {
  mainNavigationCollection(limit: 1, preview: $preview, where: { businessArea: $businessArea }) {
    items {
      internalName
      businessArea
      navigationItemsCollection(limit: 5, preview: $preview) {
        items {
          title
          navigationItemsCollection(limit: 5, preview: $preview) {
            items {
              __typename
              ... on NavigationItem {
                ...NavigationItemFragment
              }
              ... on SubNavigationItem {
                title
                link {
                  __typename
                  ... on Page {
                    slug
                  }
                  ... on ExternalLinkComponent {
                    href
                  }
                }
                navigationItemsCollection(limit: 6, preview: $preview) {
                  items {
                    ...NavigationItemFragment
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
