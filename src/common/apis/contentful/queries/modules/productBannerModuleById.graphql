query ProductBannerModuleById($id: String!, $preview: Boolean!) {
  productBannerModule(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    backgroundColor
    headline
    headlineTag
    subline
    descriptionText {
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    link {
      __typename
      ... on InternalLinkComponent {
        ...InternalLinkFragment
      }
      ... on ExternalLinkComponent {
        ...ExternalLinkFragment
      }
    }
    image {
      ...ImageFragment
    }
  }
}
