query RegionByName($name: String!, $preview: Boolean!) {
  regionCollection(limit: 1, preview: $preview, where: { name: $name }) {
    items {
      ...EntryFragment
      name
      text
      image {
        ...ImageFragment
      }
      link {
        ... on Page {
          __typename
          slug
        }
        ... on ExternalLinkComponent {
          __typename
          href
        }
      }
    }
  }
}
