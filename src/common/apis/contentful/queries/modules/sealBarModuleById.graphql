query SealBarModuleById($id: String!, $preview: Boolean!) {
  sealBarModule(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    headline
    headlineTag
    explanationText {
      __typename
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    sealsCollection {
      items {
        image {
          ...ImageFragment
        }
        imageUrl
        link {
          ... on ExternalLinkComponent {
            href
          }
        }
      }
    }
    backgroundColor
    theme
  }
}
