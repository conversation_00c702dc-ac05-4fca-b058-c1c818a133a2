query SealBarModuleByInternalName($name: String!, $preview: Boolean!) {
  sealBarModuleCollection(where: { internalName: $name }, preview: $preview, limit: 1) {
    items {
      ...EntryFragment
      internalName
      headline
      explanationText {
        __typename
        json
        links {
          assets {
            hyperlink {
              ...HyperlinkAssetFragment
            }
          }
          entries {
            inline {
              ...InlineEntryFragment
            }
            hyperlink {
              ...HyperlinkEntryFragment
            }
          }
        }
      }
      sealsCollection(limit: 10) {
        items {
          image {
            ...ImageFragment
          }
          link {
            ... on ExternalLinkComponent {
              href
            }
          }
        }
      }
      backgroundColor
      theme
    }
  }
}
