query SliderModuleByInternalName($internalName: String!, $preview: Boolean!) {
  sliderModuleCollection(where: { internalName: $internalName }, preview: $preview) {
    items {
      ...EntryFragment
      internalName
      headline
      headlineTag
      introText {
        __typename
        json
        links {
          assets {
            hyperlink {
              ...HyperlinkAssetFragment
            }
          }
          entries {
            inline {
              ...InlineEntryFragment
            }
            hyperlink {
              ...HyperlinkEntryFragment
            }
          }
        }
      }
      centerContent
      theme
      slidesPerView
      elementsCollection(limit: 6) {
        items {
          ...EntryFragment
        }
      }
    }
  }
}
