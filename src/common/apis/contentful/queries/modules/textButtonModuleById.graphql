query TextButtonModuleById($id: String!, $preview: Boolean!) {
  textButtonModule(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    headline
    headlineTag
    text {
      __typename
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    cta {
      __typename
      ... on Cta {
        ...CtaFragment
      }
      ... on Modal {
        ...ModalFragment
      }
    }
    additionalCta {
      __typename
      ... on Cta {
        ...CtaFragment
      }
      ... on Modal {
        ...ModalFragment
      }
    }
    ctaAlignment
    layout
    theme
  }
}
