query TextButtonModuleByInternalName($name: String!, $preview: Boolean!) {
  textButtonModuleCollection(where: { internalName: $name }, limit: 1, preview: $preview) {
    items {
      ...EntryFragment
      internalName
      headline
      headlineTag
      text {
        __typename
        json
        links {
          assets {
            hyperlink {
              ...HyperlinkAssetFragment
            }
          }
          entries {
            inline {
              ...InlineEntryFragment
            }
            hyperlink {
              ...HyperlinkEntryFragment
            }
          }
        }
      }
      cta {
        ...CtaFragment
      }
      additionalCta {
        __typename
        ... on Cta {
          ...CtaFragment
        }
        ... on Modal {
          ...ModalFragment
        }
      }
      ctaAlignment
      layout
      theme
    }
  }
}
