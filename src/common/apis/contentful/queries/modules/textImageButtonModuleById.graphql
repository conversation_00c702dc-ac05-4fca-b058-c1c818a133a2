query TextImageButtonModuleById($id: String!, $preview: Boolean!) {
  textImageButtonModule(id: $id, preview: $preview) {
    ...EntryFragment
    internalName
    headline
    headlineTag
    text {
      __typename
      json
      links {
        assets {
          hyperlink {
            ...HyperlinkAssetFragment
          }
        }
        entries {
          inline {
            ...InlineEntryFragment
          }
          hyperlink {
            ...HyperlinkEntryFragment
          }
        }
      }
    }
    image {
      ...ImageFragment
    }
    imagePosition
    cta {
      __typename
      ... on Cta {
        ...CtaFragment
      }
      ... on Modal {
        ...ModalFragment
      }
    }
    additionalCta {
      __typename
      ... on Cta {
        ...CtaFragment
      }
      ... on Modal {
        ...ModalFragment
      }
    }
    ctaAlignment
    theme
  }
}
