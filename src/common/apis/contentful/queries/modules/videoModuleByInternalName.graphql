query VideoModuleByInternalName($internalName: String!, $preview: Boolean!) {
  videoModuleCollection(where: { internalName: $internalName }, limit: 1, preview: $preview) {
    items {
      ...EntryFragment
      internalName
      headline
      headlineTag
      descriptionText {
        json
        links {
          assets {
            hyperlink {
              ...HyperlinkAssetFragment
            }
          }
          entries {
            inline {
              ...InlineEntryFragment
            }
            hyperlink {
              ...HyperlinkEntryFragment
            }
          }
        }
      }
      videos
    }
  }
}
