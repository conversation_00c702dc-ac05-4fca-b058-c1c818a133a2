import { BusinessArea } from '@/app/consts';

import Accordion from '@/modules/Accordion';
import AvailabilityCheck from '@/modules/AvailabilityCheck';
import ConsultationAtHome from '@/modules/ConsultationAtHome';
import DecisionTree from '@/modules/DecisionTree';
import Download from '@/modules/Download';
import Form from '@/modules/Form';
import Glossary from '@/modules/Glossary';
import { GlossaryVariant } from '@/modules/Glossary/index.types';
import Grid, { GridItemStyle, GridLayout } from '@/modules/Grid';
import InfoGraphic from '@/modules/InfoGraphic';
import InteractiveMap from '@/modules/InteractiveMap';
import Placeholder from '@/modules/Placeholder';
import { isPlaceholderType } from '@/modules/Placeholder/types';
import ProductBanner from '@/modules/ProductBanner';
import ProductTable from '@/modules/ProductTable';
import RegionOverview from '@/modules/RegionOverview';
import SealBar from '@/modules/SealBar';
import type { SealProps } from '@/modules/SealBar/Seal';
import ServiceAndContact from '@/modules/ServiceAndContact';
import Slider from '@/modules/Slider';
import TariffInfoAccordion from '@/modules/TariffInfoAccordion';
import TariffInfoGrid from '@/modules/TariffInfoGrid';
import TariffOverview from '@/modules/TariffOverview';
import type { SpecialTariff } from '@/modules/TariffOverview/TariffTable';
import type { TariffTeaserVariant } from '@/modules/TariffOverview/TariffTable/TariffTeaser';
import TariffStage from '@/modules/TariffStage';
import TariffTeaser from '@/modules/TariffTeaser';
import TextImageTeaser from '@/modules/TextImageTeaser';
import { TextImageTeaserImagePosition } from '@/modules/TextImageTeaser/index.types';
import TextTeaser from '@/modules/TextTeaser';
import { TextTeaserLayout } from '@/modules/TextTeaser/index.types';
import Video from '@/modules/Video';

import type {
  AccordionItem,
  Accordion as AccordionModule,
  AvailabilityCheckModule,
  ConsultationAtHomeModule,
  DecisionTreeModule,
  DownloadModule,
  Form as FormModule,
  Glossary as GlossaryModule,
  GridModule,
  InfoGraphicModule,
  InteractiveMapModule,
  PlaceholderModule,
  ProductBannerModule,
  ModuleProductTable as ProductTableModule,
  RegionOverviewModule,
  SealBarModule,
  ServiceAndContactModule,
  SliderModule,
  SliderModuleElementsItem,
  TariffCarouselModule,
  TariffInfoGrid as TariffInfoGridModule,
  TariffInfoModule,
  TariffOverviewModule,
  TariffTeaserModule,
  TextButtonModule,
  TextImageButtonModule,
  VideoModule,
} from '@/apis/contentful/generated/types';
import { getTeaser } from '@/apis/contentful/getTeaser';
import { parseContentfulContentFile } from '@/apis/contentful/parser/contentFile';
import { parseContentfulContentImage } from '@/apis/contentful/parser/contentImage';
import { parseCtaData } from '@/apis/contentful/parser/cta';
import { parseExternalLinkData } from '@/apis/contentful/parser/externalLink';
import { parseConfigFormElements, parseFormElementData } from '@/apis/contentful/parser/forms';
import { parseInternalLinkData } from '@/apis/contentful/parser/internalLink';
import { parseRegion } from '@/apis/contentful/parser/region';
import { parseBlockToTeaser } from '@/apis/contentful/parser/teaser';
import { parseTheme } from '@/apis/contentful/parser/theme';
import { getSliderOrGridItem } from '@/apis/contentful/utils/getSliderOrGridItem';
import type { Provider } from '@/apis/dg/types';

import Headline from '@/components/Headline';
import type { HeadlineProps } from '@/components/Headline';
import type { TeaserProps } from '@/components/Teaser';

import type { FormField, FormGroup, FormType } from '@/types/forms';
import { Theme } from '@/types/theme';

export type Block =
  | AccordionModule
  | AvailabilityCheckModule
  | ConsultationAtHomeModule
  | DecisionTreeModule
  | DownloadModule
  | FormModule
  | GridModule
  | GlossaryModule
  | InfoGraphicModule
  | InteractiveMapModule
  | PlaceholderModule
  | ProductBannerModule
  | ProductTableModule
  | RegionOverviewModule
  | SealBarModule
  | ServiceAndContactModule
  | SliderModule
  | SliderModuleElementsItem
  | TariffCarouselModule
  | TariffInfoGridModule
  | TariffInfoModule
  | TariffOverviewModule
  | TariffTeaserModule
  | TextButtonModule
  | TextImageButtonModule
  | VideoModule;

type BlockRendererProps = {
  blocks: Block[];
};

async function BlockRenderer({ blocks }: BlockRendererProps) {
  if (!blocks) {
    return null;
  }

  return (
    <>
      {blocks
        .filter((block) => !!block)
        .map((block, i) => {
          switch (block.__typename) {
            case 'Accordion':
              return (
                <Accordion
                  key={i}
                  internalName={block.internalName ?? 'accordion'}
                  headline={block.headline ?? undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  centerHeadline={block.centerHeadline ?? false}
                  items={block.accordionItemsCollection?.items as AccordionItem[]}
                  theme={parseTheme(block.theme)}
                />
              );
            case 'TextImageButtonModule':
              return (
                <TextImageTeaser
                  key={i}
                  internalName={block.internalName ?? 'text-bild-teaser'}
                  headline={
                    block.headline ??
                    (block.cta &&
                    block.cta.__typename === 'Cta' &&
                    block.cta?.link?.__typename === 'Page' &&
                    block.cta.link.title
                      ? block.cta.link.title
                      : undefined)
                  }
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  text={
                    block.text ??
                    (block.cta && block.cta.__typename === 'Cta' && block.cta?.link?.__typename === 'Page'
                      ? block.cta?.link?.teaserText
                      : undefined)
                  }
                  image={parseContentfulContentImage(
                    block.image ??
                      (block.cta && block.cta.__typename === 'Cta' && block.cta?.link?.__typename === 'Page'
                        ? block.cta?.link.teaserImage
                        : undefined),
                  )}
                  imagePosition={
                    block.imagePosition === 'left'
                      ? TextImageTeaserImagePosition.Left
                      : TextImageTeaserImagePosition.Right
                  }
                  theme={parseTheme(block.theme)}
                  cta={block.cta ? parseCtaData(block.cta) : undefined}
                  additionalCta={block.additionalCta ? parseCtaData(block.additionalCta) : undefined}
                  ctaAlignment={block.ctaAlignment === 'horizontal' ? 'horizontal' : 'vertical'}
                />
              );

            case 'TextButtonModule':
              return (
                <TextTeaser
                  key={i}
                  internalName={block.internalName ?? 'text-teaser'}
                  headline={block.headline ?? undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  text={block.text ?? undefined}
                  layout={(block.layout ?? TextTeaserLayout.Default) as TextTeaserLayout}
                  theme={parseTheme(block.theme)}
                  cta={block.cta ? parseCtaData(block.cta) : undefined}
                  additionalCta={block.additionalCta ? parseCtaData(block.additionalCta) : undefined}
                  ctaAlignment={block.ctaAlignment === 'horizontal' ? 'horizontal' : 'vertical'}
                />
              );

            case 'TariffTeaserModule':
              return (
                <TariffTeaser
                  key={i}
                  internalName={block.internalName ?? 'tarife-teaser'}
                  headline={block.headline ?? 'Headline'}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  text={block.text ?? 'Text'}
                  provider={(block.provider as Provider) ?? 'DGH'}
                  tariffId={block.tariffId ?? '0'}
                  campaignId={block.campaignId ?? undefined}
                />
              );

            case 'TariffInfoModule':
              return (
                <TariffInfoAccordion
                  internalName={block.internalName ?? 'tarif-info'}
                  key={i}
                  provider={(block.provider as Provider) ?? 'DGH'}
                  campaignId={block.campaignId ?? undefined}
                />
              );

            case 'GridModule':
              const includesProductTariff = Boolean(
                block.elementsCollection?.items.find(
                  (item) => item?.__typename === 'Product' && item.variant === 'Tarif',
                ),
              );

              // Determine the grid item style based on theme comparison
              const gridItemStyle = Boolean(
                block.elementsCollection?.items.find((item) => {
                  return (
                    item?.__typename === 'TextTeaserComponent' ||
                    ((item?.__typename === 'TextIconTeaserComponent' || item?.__typename === 'TextButtonTeaser') &&
                      ((item.theme === 'sand' && block.theme === 'sand') ||
                        (item.theme === 'gray' && block.theme === 'gray')))
                  );
                }),
              )
                ? GridItemStyle.Flat // Same theme = Flat style (gap 32px, no padding)
                : GridItemStyle.Box; // Different theme = Box style (gap 24px, padding 16px)

              return (
                <Grid
                  key={i}
                  internalName={block.internalName ?? 'grid'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  centerContent={includesProductTariff ? false : (block.centerContent ?? false)}
                  introText={block.introText}
                  cta={block.cta ? parseCtaData(block.cta) : undefined}
                  ctaPosition={block.ctaPosition as 'left' | 'center' | 'right' | null}
                  columns={block.columns}
                  theme={parseTheme(block.theme)}
                  layout={includesProductTariff ? GridLayout.Tariff : GridLayout.Default}
                  gridItemStyle={gridItemStyle}
                >
                  {block.elementsCollection?.items
                    ?.filter((item) => !!item)
                    .map(async (item, index) => {
                      if (!item) return null;
                      const teaser = await getTeaser(item);
                      return getSliderOrGridItem({ item: teaser, index, theme: parseTheme(block.theme) });
                    })}
                </Grid>
              );
            case 'SliderModule':
              return (
                <Slider
                  key={i}
                  internalName={block.internalName ?? 'slider'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  introText={block.introText}
                  centerContent={block.centerContent ?? false}
                  theme={parseTheme(block.theme)}
                  slidesPerView={block.slidesPerView ?? 4}
                >
                  {block.elementsCollection?.items
                    ?.filter((item) => !!item)
                    .map(async (item, index) => {
                      if (!item) return null;
                      const teaser = await getTeaser(item);
                      return getSliderOrGridItem({ item: teaser, index, theme: parseTheme(block.theme) });
                    })}
                </Slider>
              );
            case 'InteractiveMapModule':
              return (
                <InteractiveMap
                  key={i}
                  internalName={block.internalName ?? 'interaktive-map'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  introText={block.introText ? block.introText : undefined}
                  regions={(block?.regionsCollection?.items ?? []).map((region) => parseRegion(region))}
                  theme={parseTheme(block.theme)}
                />
              );
            case 'RegionOverviewModule':
              return (
                <RegionOverview
                  key={i}
                  internalName={block.internalName ?? 'region-uebersicht'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  regions={(block?.regionsCollection?.items ?? []).map((region) => parseRegion(region))}
                />
              );
            case 'TariffCarouselModule':
              return (
                <TariffStage
                  key={i}
                  internalName={block.internalName ?? 'tariff-carousel'}
                  headline={block.headline ?? 'Headline'}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  provider={block.provider ?? ''}
                  campaignId={block.campaignId ?? ''}
                />
              );
            case 'AvailabilityCheckModule':
              const theme = parseTheme(block.theme);
              return (
                <AvailabilityCheck
                  key={i}
                  internalName={block.internalName ?? 'verfuegbarkeitscheck'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  theme={theme === Theme.Sand ? Theme.Gray : theme}
                />
              );
            case 'TariffOverviewModule':
              return block.provider && block.campaignId ? (
                <TariffOverview
                  key={i}
                  internalName={block.internalName ?? 'tarif-übersicht'}
                  headline={block.headline ? block.headline : undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  provider={block.provider as Provider}
                  campaignId={block.campaignId}
                  isReversed={block.isReversed ?? false}
                  specialTariff={(block.specialTariff as SpecialTariff) ?? undefined}
                  specialTariffVariant={(block.specialTariffVariant as TariffTeaserVariant) ?? 'default'}
                />
              ) : (
                <Headline>Provider and/or Campaign ID is missing!</Headline>
              );
            case 'TariffInfoGrid':
              return <TariffInfoGrid key={i} id={block.sys.id} />;
            case 'SealBarModule':
              return (
                <SealBar
                  key={i}
                  internalName={block.internalName ?? 'seal-bar'}
                  headline={block.headline ?? ''}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  explanationText={block.explanationText ? block.explanationText : undefined}
                  seals={(block.sealsCollection?.items ?? []).map((seal) => {
                    return {
                      image: seal?.image ? parseContentfulContentImage(seal?.image) : undefined,
                      imageUrl: seal?.imageUrl ? seal?.imageUrl : undefined,
                      link: seal?.link,
                    } as SealProps;
                  })}
                  theme={parseTheme(block.theme)}
                />
              );
            case 'ProductBannerModule':
              return (
                <ProductBanner
                  key={i}
                  internalName={block.internalName ?? 'produkt-banner'}
                  backgroundColor={block.backgroundColor as Theme}
                  headline={block.headline ?? 'Headline'}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  subline={block.subline ?? undefined}
                  image={parseContentfulContentImage(block.image)}
                  descriptionText={block.descriptionText}
                  link={
                    block.link && block.link.__typename === 'InternalLinkComponent'
                      ? parseInternalLinkData(block.link)
                      : block.link && block.link.__typename === 'ExternalLinkComponent'
                        ? parseExternalLinkData(block.link)
                        : {
                            label: 'Label',
                            href: '',
                          }
                  }
                />
              );
            case 'InfoGraphicModule':
              return (
                <InfoGraphic
                  key={i}
                  internalName={block.internalName ?? 'infografik'}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  headline={block.headline ?? undefined}
                  mobileImage={parseContentfulContentImage(block.mobileImage)}
                  desktopImage={parseContentfulContentImage(block.desktopImage)}
                  theme={parseTheme(block.theme)}
                />
              );
            case 'ServiceAndContactModule':
              return (
                <ServiceAndContact
                  key={i}
                  internalName={block.internalName ?? 'service-und-kontakt'}
                  headline={block.headline ?? undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  links={block.linksCollection?.items.map((link) => parseCtaData(link))}
                />
              );
            case 'DownloadModule':
              return (
                <Download
                  key={i}
                  internalName={block.internalName ?? 'download'}
                  headline={block.headline ?? 'Headline'}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  files={
                    block.filesCollection?.items
                      ? block.filesCollection?.items.map((item) => {
                          return parseContentfulContentFile(item);
                        })
                      : []
                  }
                />
              );
            case 'VideoModule':
              return (
                <Video
                  key={i}
                  internalName={block.internalName ?? 'video'}
                  headline={block.headline ?? undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  descriptionText={block.descriptionText ?? undefined}
                  videos={block.videos ? block.videos?.flatMap((video) => video ?? []) : []}
                  theme={(block.theme as Theme) ?? Theme.Gray}
                />
              );
            case 'Glossary':
              return (
                <Glossary
                  internalName={block.internalName ?? 'glossary'}
                  type={(block.type as GlossaryVariant) ?? GlossaryVariant.BFSG}
                  key={i}
                />
              );
            case 'Form': {
              const {
                internalName,
                formType,
                headline,
                headlineTag,
                // Deprecated, use introTextRt instead
                introText,
                // RichText field for introText
                introTextRt,
                receiver,
                config,
                infoTeaserCollection,
                formElementsCollection,
              } = block;

              const infoTeaser =
                infoTeaserCollection && infoTeaserCollection.items.length > 0
                  ? infoTeaserCollection.items
                      .map((item) => (item ? parseBlockToTeaser(item) : null))
                      .filter((teaser): teaser is TeaserProps => teaser !== null)
                  : undefined;

              const formElements =
                formElementsCollection?.items && formElementsCollection.items.length > 0
                  ? (formElementsCollection?.items
                      .map((el) => parseFormElementData(el))
                      .filter((el): el is FormField | FormGroup => el !== null) ?? [])
                  : parseConfigFormElements(config);

              const recipient = {
                id: receiver?.sys.id ?? '',
              };

              return (
                <Form
                  key={i}
                  internalName={internalName ?? 'form'}
                  recipient={recipient}
                  headline={headline ?? undefined}
                  headlineTag={(headlineTag as HeadlineProps['intent']) ?? 'h2'}
                  introText={introText ?? undefined}
                  introTextRt={introTextRt ?? undefined}
                  formType={(formType as FormType) ?? 'Contact'}
                  infoTeaser={infoTeaser}
                  formElements={formElements}
                />
              );
            }
            case 'DecisionTreeModule': {
              return (
                <DecisionTree
                  key={i}
                  internalName={block.internalName ?? 'decision-tree'}
                  initialStepId={block.initialStep?.sys.id ?? undefined}
                />
              );
            }
            case 'ConsultationAtHomeModule':
              const { businessArea, hotlinePrivate, hotlineProfessional, hotlineBusinessPublic, contactForm } = block;

              if (contactForm) {
                const formElements = parseConfigFormElements(contactForm.config);

                return (
                  <ConsultationAtHome
                    key={i}
                    internalName={block.internalName ?? 'beratung-zuhause'}
                    businessArea={(businessArea as BusinessArea) ?? BusinessArea.Private}
                    hotlinePrivate={hotlinePrivate ?? undefined}
                    hotlineProfessional={hotlineProfessional ?? '02861 - 8133 381'}
                    hotlineBusinessPublic={hotlineBusinessPublic ?? '02861 - 281 2812'}
                    formElements={formElements}
                  />
                );
              } else {
                return <div key={i}>Form not found. Please add a form to the consultation at home module.</div>;
              }

            case 'ModuleProductTable':
              return (
                <ProductTable
                  key={i}
                  internalName={block.internalName ?? 'produkt-tabelle'}
                  variant={block.variant ?? 'Variante'}
                />
              );
            case 'PlaceholderModule': {
              const placeholderType = isPlaceholderType(block.placeholderType) ? block.placeholderType : undefined;
              return (
                <Placeholder
                  key={i}
                  internalName={block.internalName ?? 'placeholder'}
                  headline={block.headline ?? undefined}
                  headlineTag={(block.headlineTag as HeadlineProps['intent']) ?? undefined}
                  placeholderType={placeholderType}
                />
              );
            }
            default:
              return (
                <Headline type="h2" intent="h2" className="text-error" key={i}>
                  {block?.__typename} not implemented
                </Headline>
              );
          }
        })}
    </>
  );
}

export default BlockRenderer;
