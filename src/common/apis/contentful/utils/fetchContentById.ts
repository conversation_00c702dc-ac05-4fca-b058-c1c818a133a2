'use server';

import { draftMode } from 'next/headers';

import contentfulClient from '@/apis/contentful/api';

export async function fetchContentById({ id, contentType }: { id: string; contentType: string }) {
  const { isEnabled } = draftMode();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const contentful = contentfulClient({ preview: isEnabled }) as any; // Use 'as any' for the contentful object

  if (!id || !contentType) return null;

  try {
    const methodName = `${contentType}ById`;
    const data = await contentful[methodName]({ id, preview: isEnabled });
    return data[contentType[0].toLowerCase() + contentType.slice(1)];
  } catch (error) {
    console.error(`Module ${contentType} with ID: ${id} not implemented yet! - Error: ${error}`);
    return {
      __typename: contentType,
      sys: {
        id,
      },
    };
  }
}
