import React, { Suspense } from 'react';

import VideoTeaser from 'src/common/components/VideoTeaser';

import TariffTeaser from '@/modules/TariffOverview/TariffTable/TariffTeaser';

import type {
  BusinessReviewComponent,
  KeyFactBubbleComponent,
  Product,
  TextButtonTeaser,
  TextIconTeaserComponent,
  TextImageButtonTeaserVertical,
  TextTeaserComponent,
  Video,
} from '@/apis/contentful/generated/types';
import { parseInternalLinkData } from '@/apis/contentful/parser/internalLink';
import { parseLegalNote } from '@/apis/contentful/parser/legalNote';
import { parseProductProperties } from '@/apis/contentful/parser/productProperty';
import { parseTariff } from '@/apis/contentful/parser/tariff';
import { parseBlockToTeaser } from '@/apis/contentful/parser/teaser';

import BusinessReview from '@/components/BusinessReview';
import type { KeyFactBubbleColor } from '@/components/KeyFactBubble';
import KeyFactBubble from '@/components/KeyFactBubble';
import ProductTeaser from '@/components/ProductTeaser';
import { ProductTeaserTheme } from '@/components/ProductTeaser/types';
import Teaser from '@/components/Teaser';

import type { TextAlignment } from '@/types/text';
import type { Theme } from '@/types/theme';

import { parseTheme } from '../parser/theme';

type SliderOrGridItemProps = {
  index: number;
  item:
    | TextImageButtonTeaserVertical
    | TextButtonTeaser
    | TextIconTeaserComponent
    | BusinessReviewComponent
    | KeyFactBubbleComponent
    | TextTeaserComponent
    | Product
    | Video;
  theme: Theme;
};

export function getSliderOrGridItem({ item, index, theme }: SliderOrGridItemProps) {
  const contentType = item?.__typename;

  switch (contentType) {
    case 'BusinessReviewComponent':
      return (
        <BusinessReview
          key={index}
          starRating={item.starRating ?? undefined}
          reviewText={item.reviewText ?? ''}
          nameOfAuthor={item.nameOfAuthor ?? ''}
          legalNote={item.legalNote}
        />
      );
    case 'KeyFactBubbleComponent':
      return (
        <KeyFactBubble
          key={index}
          headline={item.headline ?? ''}
          description={item.description}
          theme={theme}
          backgroundColor={item.backgroundColor as KeyFactBubbleColor | undefined}
        />
      );
    case 'Product':
      if (item.variant === 'Produkt') {
        return (
          <ProductTeaser
            key={index}
            internalName={item.internalName ?? 'undefined-product'}
            headline={item.headline ?? 'Headline'}
            headlineTag={item.headlineTag ?? 'h2'}
            subline={item.subline ?? undefined}
            price={item.price ?? undefined}
            legalNote={parseLegalNote(item.legalNote)}
            additionalInfo={item.additionalInfo ?? undefined}
            productProperties={parseProductProperties(item.productPropertyCollection)}
            theme={(item.theme as ProductTeaserTheme) ?? ProductTeaserTheme.Basalt}
            variant={item.variant ?? 'Produkt'}
            visiblePropertyCount={item.visibleProps ?? 3}
            cta={item.cta ? parseInternalLinkData(item.cta) : undefined}
          />
        );
      }

      return (
        <Suspense>
          <TariffTeaser {...parseTariff(item, index)} />
        </Suspense>
      );
    case 'Video':
      return (
        <VideoTeaser
          key={index}
          url={item.url ?? ''}
          headline={item.headline ?? 'Headline'}
          headlineTag={item.headlineTag ?? 'h2'}
          description={item.description ? item.description : undefined}
          theme={parseTheme(item.theme)}
          textAlignment={item.textAlignment as TextAlignment}
          innerPadding={theme !== item.backgroundTheme}
        />
      );
    case 'TextButtonTeaser':
    case 'TextImageButtonTeaserVertical':
    case 'TextIconTeaserComponent':
    case 'TextTeaserComponent':
      return (
        <Teaser
          key={index}
          {...parseBlockToTeaser({
            ...item,
            gridTheme: theme,
          })}
        />
      );
    default:
      return null;
  }
}
