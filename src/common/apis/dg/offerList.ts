'use server';

import { dgClient } from '@/apis/dg/api';
import { parseOfferListData } from '@/apis/dg/parser/offerList';

import { ApiType, Endpoint, Host } from './consts';
import { type OfferList } from './types';

type OfferListFetchOptions = {
  provider: string;
  houseId?: string;
  campaignId?: string;
};

export async function getOfferList({
  provider,
  houseId,
  campaignId,
}: OfferListFetchOptions): Promise<OfferList | null> {
  const query = getQuery(provider, houseId, campaignId);

  // TechnicalDep: https://jira.deutsche-glasfaser.de/browse/ORP-2226
  // see api.ts:52
  const apiType = process.env.DG_USE_DIRECTLY_SERVER_CONNECTION === 'true' ? ApiType.SHOP2_DIRECTLY : ApiType.SHOP2;

  try {
    const data = (await dgClient(Host.NEW, { next: { revalidate: 600 } })
      .get(apiType + Endpoint.OFFER_LIST + query)
      .json()) as DGOfferList;

    if (!data) return null;

    return parseOfferListData(data as DGOfferList, houseId);
  } catch (error: any) {
    console.log('Error fetching offer list', error);
    return null;
  }
}

function getQuery(provider: string, houseId: string | undefined, campaignId: string | undefined) {
  return `?provider=${provider}${houseId ? `&house_id=${houseId}` : campaignId ? `&campaign_id=${campaignId}` : ''}`;
}
