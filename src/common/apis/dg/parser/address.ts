import type { CmsData } from '../types';
import { parseCmsData } from './cms';

export type UserAddress = {
  houseId: string;
  hash: string;
  isHappyFlow: boolean;
  connectionScenario: string;
  zipcode: string;
  city: string;
  street: string;
  houseNumber: number;
  houseNumberExtension: null | string;
  cms?: CmsData | null;
};

const parseHouseNumberExtension = (houseNumberExtension: string | number | null): string | null => {
  if (!houseNumberExtension) return null;
  if (typeof houseNumberExtension === 'number') return houseNumberExtension.toString();
  return houseNumberExtension;
};

export async function parseAddressInfoData(data: DGAddressInfo): Promise<UserAddress> {
  return {
    houseId: data.houseId,
    hash: data.hash,
    isHappyFlow: data.isHappyFlow,
    connectionScenario: data.connectionScenario,
    zipcode: data.zipcode,
    city: data.city,
    street: data.street,
    houseNumber: data.houseNumber,
    houseNumberExtension: parseHouseNumberExtension(data.houseNumberExtension),
    cms: data.cms ? await parseCmsData(data.cms) : undefined,
  };
}
