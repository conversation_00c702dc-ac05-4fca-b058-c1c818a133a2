import { getLegalNote } from '@/apis/dg/legalNote';

import type { Advantage } from '../types';

export async function parseAdvantages(data: DGCampaignAdvantage[]): Promise<Advantage[]> {
  return await Promise.all(
    data.map(async (advantage: DGCampaignAdvantage) => {
      return {
        order: advantage.order,
        legalNote: advantage.legal_note ? await getLegalNote(advantage.legal_note) : undefined,
        icon: advantage.icon,
        text: advantage.text,
        title: advantage.title,
        highlight: advantage.highlight,
        deliveryStatus: advantage.delivery_status,
      };
    }),
  );
}
