import { getAvailabilityCheckInfos } from '@/apis/contentful/getAvailabilityCheckInfos';

import type { CmsData } from '../types';

export async function parseCmsData(data: DGCms): Promise<CmsData> {
  const cmsData: CmsData = {
    title: data.title,
    description: data.description,
    buttonText: data.button_text,
    showForm: data.show_form,
    isIndustrialPark: data.is_industrial_park,
    isIndustrialParkHappyflow: data.is_industrial_park_happyflow,
  };

  if (cmsData.isIndustrialParkHappyflow) {
    cmsData.availabilityFlyoutInfos = await getAvailabilityCheckInfos();
  }

  return cmsData;
}
