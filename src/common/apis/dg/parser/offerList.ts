import { parseAddressInfoData } from '@/apis/dg/parser/address';
import { parseCampaignData } from '@/apis/dg/parser/campaign';
import { parseInfoText } from '@/apis/dg/parser/infoText';
import { parseOffersToTariffs } from '@/apis/dg/parser/offers';
import type { OfferList } from '@/apis/dg/types';

// TODO: check if the houseId is really needed, because it is also passed via data.address_info
export async function parseOfferListData(data: DGOfferList, houseId?: string): Promise<OfferList> {
  return {
    address: data.address_info ? await parseAddressInfoData(data.address_info) : null,
    campaign: data.campaign_data ? await parseCampaignData(data.campaign_data) : null,
    offers: data.offers ? await parseOffersToTariffs(data.offers, houseId) : [],
    infoText: data.project_status_group_code
      ? await parseInfoText(data.project_status_group_code, data.delivery_status)
      : null,
    projectNumber: data.project_number,
    projectSlug: data.project_slug,
    projectStatus: data.project_status,
    projectStatusOriginal: data.project_status_original,
    projectStatusDescription: data.project_status_description,
    projectStatusGroupCode: data.project_status_group_code,
    projectStatusGroupTitle: data.project_status_group_title,
    projectStatusTitle: data.project_status_title,
    projectDemandBundling: data.project_demand_bundling,
    projectName: data.project_name,
    deliveryStatus: data.delivery_status,
    dwellingUnits: data.dwelling_units,
  };
}
