'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { ElementType, ReactNode } from 'react';

import AccordionContext from '@/components/Accordion/AccordionContext';
import { AkkClosedSm, AkkOpenSm } from '@/components/Icons/sm';

export type AccordionType = 'primary' | 'secondary';

type AccordionProps = {
  id: string;
  children: ReactNode;
  defaultExpanded?: string[];
  type?: AccordionType;
  icon?: ElementType;
  expandIcon?: ElementType;
};

function Accordion({
  id,
  children,
  defaultExpanded = [],
  type = 'primary',
  icon = () => <AkkClosedSm width={32} height={32} />,
  expandIcon = () => <AkkOpenSm width={32} height={32} />,
}: AccordionProps) {
  const [activeItems, setActiveItems] = useState(defaultExpanded);
  const localStorageKey = `Accordion-${id}-activeItems`;

  const setToggle = useCallback(
    (value: string) => {
      setActiveItems((prevActiveItems: string[]) => {
        const updatedActiveItems = prevActiveItems.includes(value)
          ? prevActiveItems.filter((item) => item !== value)
          : [...prevActiveItems, value];

        // Store the updated activeItems in sessionStorage
        sessionStorage.setItem(localStorageKey, JSON.stringify(updatedActiveItems));
        return updatedActiveItems;
      });
    },
    [localStorageKey], // removed setActiveItems from dependencies because useCallback takes care of it
  );

  useEffect(() => {
    const storedActiveItems = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem(localStorageKey) : '[]';
    if (storedActiveItems) {
      setActiveItems((prevActiveItems) => [...prevActiveItems, ...JSON.parse(storedActiveItems)]);
    }
  }, [localStorageKey]);

  const value = useMemo(
    () => ({
      activeItems,
      setToggle,
      defaultExpanded,
      type,
      icon,
      expandIcon,
    }),
    [activeItems, setToggle, type, defaultExpanded, icon, expandIcon], // these dependencies are correct
  );

  return (
    <AccordionContext.Provider value={value}>
      <div>{children}</div>
    </AccordionContext.Provider>
  );
}

export default Accordion;
