import type { Meta, StoryObj } from '@storybook/react';

import { projectConstruction, projectDemandBundling } from '@/mock/project/mockedProjects';

import PartnerGroups from './index';

const meta: Meta<typeof PartnerGroups> = {
  title: 'Components/Areasite/Project/PartnerGroups',
  component: PartnerGroups,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/netzausbau/hamburg/albisheim',
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof PartnerGroups>;

export const WithLogos: Story = {
  args: {
    project: projectDemandBundling as unknown as Project,
  },
};

export const WithFallbackLogos: Story = {
  args: {
    project: projectConstruction as unknown as Project,
  },
};
