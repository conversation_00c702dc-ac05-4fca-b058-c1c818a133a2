import { DEFAULT_DG_LOGO, DEFAULT_DG_PARTNER_LOGO } from '@/app/consts';

import Accordion from '@/components/Accordion';
import AccordionItem from '@/components/Accordion/AccordionItem';
import Headline from '@/components/Headline';
import ProjectPartnerCard from '@/components/ProjectPartnerCard';

type PartnerGroups = {
  label: string;
  value: string;
  partners: ProjectPartner[];
  fallbackLogo: string;
};

export function generatePartnerGroupsData(project: Project): PartnerGroups[] {
  const { servicepointPartners, salesPartners, ispPartners } = project;

  return [
    {
      label: '<PERSON><PERSON><PERSON><PERSON> v<PERSON>',
      value: 'fuersievorort',
      partners: servicepointPartners,
      fallbackLogo: DEFAULT_DG_LOGO,
    },
    {
      label: 'Unsere Partner',
      value: 'unserepartner',
      partners: salesPartners,
      fallbackLogo: DEFAULT_DG_PARTNER_LOGO,
    },
    {
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      value: 'dienstanbieter',
      partners: ispPartners,
      fallbackLogo: DEFAULT_DG_LOGO,
    },
  ];
}

export default function PartnerGroups({ project }: { project: Project }) {
  const partnerGroups = generatePartnerGroupsData(project);

  return partnerGroups.some((group) => group.partners.length > 0) ? (
    <div id="Netzausbau - Weitere Informationen" className="px-4 pt-8 pb-10 lg:gap-10 lg:px-8 xl:px-24 2xl:px-48">
      <Headline intent="h2" className="mb-6 lg:text-center">
        Weitere Informationen
      </Headline>
      <Accordion id="construction-infos">
        {partnerGroups
          .filter((group) => group.partners.length > 0)
          .map((partners) => (
            <AccordionItem key={partners.value} label={partners.label} value={partners.value}>
              <div className="flex flex-col gap-5">
                {partners.partners.map((partner: ProjectPartner) => (
                  <ProjectPartnerCard key={partner.name} fallbackLogo={partners.fallbackLogo} {...partner} />
                ))}
              </div>
            </AccordionItem>
          ))}
      </Accordion>
    </div>
  ) : null;
}
