import { DateTime } from 'luxon';

import type { Notification } from '@/apis/contentful/getPages';

import { isDemandBundling } from '@/utils/projects/checkProjectPhase';

export function generateProjectNotification(project: Project): Notification | undefined {
  const {
    deadline,
    projectStatus: { statusGroupCode },
  } = project;
  const demandBundling = isDemandBundling(statusGroupCode);

  let cutoffDate: Date | undefined;
  if (deadline) {
    const projectDeadline = DateTime.fromISO(deadline, { zone: 'CET' });
    const daysDiff = projectDeadline.diff(DateTime.now(), 'days').toObject().days;
    cutoffDate = daysDiff && daysDiff < 10 && daysDiff > 0 ? projectDeadline.toJSDate() : undefined;
  }

  return cutoffDate && demandBundling
    ? {
        id: 'warning-project-notification',
        type: 'Warning',
        headline: 'Stichtag:',
        cutoffDate: cutoffDate,
      }
    : undefined;
}
