import Copy from '@/components/Copy';
import TextLink from '@/components/TextLink';

import { SERVICE_CONTACT_URL } from '@/utils/specialUrls/specialUrlsCatalog';

export default function AvailabilityCheckContact({ className }: { className?: string }) {
  return (
    <Copy className={className}>
      Wenn <PERSON> in einem <strong>Neubaugebiet</strong> wohnen oder denken es liegt ein Fehler im Verfügbarkeitscheck vor,
      klicken Sie bitte <TextLink href={SERVICE_CONTACT_URL}>hier</TextLink>.
    </Copy>
  );
}
