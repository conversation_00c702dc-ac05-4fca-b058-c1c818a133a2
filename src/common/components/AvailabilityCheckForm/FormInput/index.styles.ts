import { cva } from 'class-variance-authority';

export const inputStyles = cva('relative transition-all lg:min-h-[96px]', {
  variants: {
    flyoutMode: {
      false: 'lg:min-w-[200px] xl:min-w-[230px] 2xl:min-w-[150px]',
    },
  },
});

export const inputSelectionBoxStyles = cva(
  'absolute top-[74px] flex max-h-40 w-full flex-col overflow-y-auto border border-gray-200 bg-white text-basalt shadow-button group-[.theme-basalt]:top-[78px]',
);

export const inputHelperTextStyles = cva('px-4 py-2 transition-all');

export const inputLoadingStyles = cva('absolute top-9 right-4 flex h-6 w-6 animate-spin fill-current text-system');

export const listItemStyles = cva('cursor-pointer px-4 py-2 hover:bg-hover hover:text-sand', {
  variants: {
    selected: {
      true: 'bg-basalt text-white',
    },
  },
});
