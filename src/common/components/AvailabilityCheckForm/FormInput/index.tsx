import React, { useEffect, useRef, useState } from 'react';

import { ApiRoutes } from '@/app/consts';

import SvgLoadingSm from '@/components/Icons/sm/LoadingSm';
import TextInput from '@/components/TextInput';

import { useOutsideClick } from '@/hooks/useOutsideClick';
import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import type { CityResult, HouseNumberResult, StreetResult } from '@/stores/types/availabilityCheckStore';
import { searchAddress } from '@/stores/utils/searchAddress';

import type { AcType } from '@/types/availabilityCheck';

import { cn } from '@/utils/cn';

import AvailabilityFormInputOverlay from './FormInputOverlay';
import {
  inputHelperTextStyles,
  inputLoadingStyles,
  inputSelectionBoxStyles,
  inputStyles,
  listItemStyles,
} from './index.styles';

type FormInputName = 'city' | 'street' | 'houseNumber';

type ResultTypeMap = {
  city: CityResult;
  street: StreetResult;
  houseNumber: HouseNumberResult;
};

type FormInputConfig<T extends keyof ResultTypeMap> = {
  label: string;
  placeholder: string;
  minChars: number;
  apiRoute: string;
  getSearchParams: (dependency?: any) => Record<string, string>;
  formatOption: (option: ResultTypeMap[T]) => string;
  trackingContent: (option: ResultTypeMap[T]) => string;
  dependencyKey?: 'citySelected' | 'streetSelected';
};

const inputConfigs: { [K in FormInputName]: FormInputConfig<K> } = {
  city: {
    label: 'PLZ / Ort',
    placeholder: 'PLZ / Ort eingeben',
    minChars: 3,
    apiRoute: ApiRoutes.CITY,
    getSearchParams: (query: string) => ({ query: /^\d/.test(query) ? query.split(' ')[0] : query }),
    formatOption: (option: CityResult) => `${option.zipcode} ${option.city}`,
    trackingContent: (option: CityResult) => option.zipcode.slice(0, 3),
  },
  street: {
    label: 'Straße',
    placeholder: 'Straße eingeben',
    minChars: 3,
    apiRoute: ApiRoutes.STREET,
    getSearchParams: (city: CityResult) => ({ zipcode: city.zipcode, city_id: city.id.toString() }),
    formatOption: (option: StreetResult) => option.name,
    trackingContent: () => 'strasse',
    dependencyKey: 'citySelected',
  },
  houseNumber: {
    label: 'Hausnummer',
    placeholder: 'Hausnummer eingeben',
    minChars: 1,
    apiRoute: ApiRoutes.HOUSE_NR,
    getSearchParams: (street: StreetResult) => ({ street_id: street.id.toString() }),
    formatOption: (option: HouseNumberResult) => `${option.houseNumber}${option.houseNumberExtension || ''}`,
    trackingContent: () => 'hausnummer',
    dependencyKey: 'streetSelected',
  },
};

type FormInputProps<T extends FormInputName> = {
  type: T;
  flyoutMode: boolean;
  textInputRef?: React.RefObject<HTMLInputElement>;
  className?: string;
  onSelected?: () => void;
  id: string;
};

type StoreMethods<T extends FormInputName> = {
  input: string;
  setInput: (value: string) => void;
  results: ResultTypeMap[T][] | undefined;
  setResults: (results: ResultTypeMap[T][] | undefined) => void;
  setSelected: (selected: ResultTypeMap[T] | undefined) => void;
  notFound: boolean;
  setNotFound: (notFound: boolean) => void;
  setAcType: (value: AcType) => void;
};

export function FormInput<T extends FormInputName>({
  id,
  type,
  flyoutMode,
  textInputRef: externalTextInputRef,
  className,
  onSelected,
}: FormInputProps<T>) {
  const config = inputConfigs[type];
  const { trackEvent, trackAcEvent } = useTracking();
  const [options, setOptions] = useState<ResultTypeMap[T][]>([]);
  const [selectedOption, setSelectedOption] = useState<ResultTypeMap[T] | undefined>();
  const [highlightedOption, setHighlightedOption] = useState<ResultTypeMap[T] | undefined>();
  const [showList, setShowList] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [acOpen, setAcOpen] = useState<boolean>(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const internalTextInputRef = useRef<HTMLInputElement>(null);
  const textInputRef = externalTextInputRef || internalTextInputRef;
  const currentInputRef = useRef<string>('');
  const optionRefs = useRef<Map<number, HTMLLIElement>>(new Map());

  const store = useAvailabilityCheckStore();
  const dependency = config.dependencyKey ? store[config.dependencyKey] : undefined;

  // Get store methods based on type
  const getStoreMethods = (): StoreMethods<T> => {
    switch (type) {
      case 'city':
        return {
          input: store.cityInput,
          setInput: store.setCityInput,
          results: store.cityResults as ResultTypeMap[T][] | undefined,
          setResults: store.setCityResults as (results: ResultTypeMap[T][] | undefined) => void,
          setSelected: store.setCitySelected as (selected: ResultTypeMap[T] | undefined) => void,
          notFound: store.cityNotFound,
          setNotFound: store.setCityNotFound,
          setAcType: store.setAcType,
        };
      case 'street':
        return {
          input: store.streetInput,
          setInput: store.setStreetInput,
          results: store.streetResults as ResultTypeMap[T][] | undefined,
          setResults: store.setStreetResults as (results: ResultTypeMap[T][] | undefined) => void,
          setSelected: store.setStreetSelected as (selected: ResultTypeMap[T] | undefined) => void,
          notFound: store.streetNotFound,
          setNotFound: store.setStreetNotFound,
          setAcType: store.setAcType,
        };
      case 'houseNumber':
        return {
          input: store.houseNumberInput,
          setInput: store.setHouseNumberInput,
          results: store.houseNumberResults as ResultTypeMap[T][] | undefined,
          setResults: store.setHouseNumberResults as (results: ResultTypeMap[T][] | undefined) => void,
          setSelected: store.setHouseNumberSelected as (selected: ResultTypeMap[T] | undefined) => void,
          notFound: store.houseNumberNotFound,
          setNotFound: store.setHouseNumberNotFound,
          setAcType: store.setAcType,
        };
      default: {
        const _exhaustiveCheck: never = type;
        throw new Error(`Unhandled input type: ${_exhaustiveCheck}`);
      }
    }
  };

  const { input, setInput, results, setResults, setSelected, notFound, setNotFound, setAcType } = getStoreMethods();

  useOutsideClick(containerRef, handleOnOutsideClick);

  useEffect(() => {
    if (config.dependencyKey && !dependency) {
      setOptions([]);
      setInput('');
      setSelectedOption(undefined);
      setResults(undefined);
      setSelected(undefined);
    }
  }, [config, dependency, setInput, setResults, setSelected]);

  // Add effect to scroll highlighted option into view
  useEffect(() => {
    if (highlightedOption) {
      const index = options.findIndex((option) => option === highlightedOption);
      const element = optionRefs.current.get(index);
      if (element) {
        element.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }, [highlightedOption, options]);

  function filterOptions(data: ResultTypeMap[T][] | undefined | null, query: string): ResultTypeMap[T][] {
    if (!data) return [];
    const queryLower = query.toLowerCase();
    return data.filter((option) => config.formatOption(option).toLowerCase().includes(queryLower));
  }

  async function fetchData(searchValue: string): Promise<ResultTypeMap[T][] | undefined> {
    const params = new URLSearchParams(
      type === 'city' ? config.getSearchParams(searchValue) : config.getSearchParams(dependency),
    ).toString();
    try {
      const data = await searchAddress(`${config.apiRoute}?${params}`);
      return data || [];
    } catch (error) {
      console.error('Error fetching data:', error);
      return [];
    }
  }

  function handleOnOutsideClick() {
    if (options.length > 0 && !selectedOption) {
      const option = options[0];
      const inputString = config.formatOption(option);
      setSelectedOption(option);
      setSelected(option);
      setInput(inputString);
      setOptions(results ? filterOptions(results, inputString) : []);
      handleOnSelection();
    }
  }

  function handleOnInputChange(event: React.ChangeEvent<HTMLInputElement>) {
    const value = event.target.value;
    currentInputRef.current = value;
    setSelectedOption(undefined);
    setSelected(undefined);
    setInput(value);

    if (!isEditing) {
      setIsEditing(true);
      trackEvent({ category: 'Input', action: 'edit', element: event.currentTarget, content: config.label });
    }

    // Clear everything if below minChars
    if (value.length < config.minChars) {
      setShowList(false);
      setOptions([]);
      setResults(undefined);
      setNotFound(false);
      return;
    }

    setShowList(true);

    // Determine if we need to fetch new data
    const shouldFetchNewData =
      (!config.dependencyKey || dependency) && // Dependencies satisfied
      (!results || // No results yet
        value.length === config.minChars || // Exactly at minChars
        (value.length > config.minChars && !value.startsWith(input))); // Pasted new text

    if (shouldFetchNewData) {
      setIsFetching(true);
      fetchData(value).then((data) => {
        if (data) {
          setResults(data);
          // Use the latest input value from our ref
          const filteredOptions = filterOptions(data, currentInputRef.current);
          setOptions(filteredOptions);
          setNotFound(filteredOptions.length === 0);
          setSelectedOption(undefined);
        } else {
          setOptions([]);
          setNotFound(true);
        }
        setIsFetching(false);
      });
    } else if (results) {
      // If we have results, just filter them client-side
      const filteredOptions = filterOptions(results, value);
      setOptions(filteredOptions);
      setNotFound(filteredOptions.length === 0);
    }
  }

  function handleOnFocus(event: React.FocusEvent<HTMLInputElement>) {
    const isTabKey = event.relatedTarget === null || event.relatedTarget === undefined;
    const isClick = event.target === document.activeElement;

    // Reset dependent fields if they exist
    if (type === 'city') {
      // When focusing back on city, disable and reset street and house number
      store.setStreetSelected(undefined);
      store.setStreetResults(undefined);
      store.setStreetNotFound(false);
      store.setHouseNumberSelected(undefined);
      store.setHouseNumberResults(undefined);
      store.setHouseNumberNotFound(false);

      // Only track view if it's a direct click or tab, not from street input
      if (isClick || isTabKey) {
        trackEvent({ category: 'Input', action: 'view', element: event.currentTarget, content: config.label });
      }
    } else if (type === 'street') {
      store.setHouseNumberSelected(undefined);
      store.setHouseNumberResults(undefined);
      store.setHouseNumberNotFound(false);

      // Only track view if explicitly clicked
      if (isClick) {
        trackEvent({ category: 'Input', action: 'view', element: event.currentTarget, content: config.label });
      }
    } else if (type === 'houseNumber') {
      // Only track view if explicitly clicked
      if (isClick) {
        trackEvent({ category: 'Input', action: 'view', element: event.currentTarget, content: config.label });
      }
    }

    setSelectedOption(undefined);
    setSelected(undefined);
    setHighlightedOption(undefined);
    setShowList(true);
  }

  // Moved from hook
  function handleOnBlur() {
    setTimeout(() => {
      setShowList(false);
      setIsEditing(false);
    }, 150);
  }

  function handleOnSelection() {
    if (onSelected) onSelected();
  }

  function handleOnOptionClick(option: ResultTypeMap[T], event: React.MouseEvent<HTMLLIElement>) {
    trackEvent({
      category: 'Input',
      action: 'select',
      element: event.currentTarget,
      content: config.trackingContent(option),
    });
    setSelectedOption(option);
    setSelected(option);
    setInput(config.formatOption(option));
    handleOnSelection();
  }

  function handleKeyDown(event: React.KeyboardEvent<HTMLInputElement>) {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      const index = options.findIndex((option) => option === highlightedOption);
      setHighlightedOption(options[(index + 1) % options.length]);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      const index = options.findIndex((option) => option === highlightedOption);
      setHighlightedOption(options[(index - 1 + options.length) % options.length]);
    } else if (event.key === 'Enter' && options.length > 0 && !notFound) {
      const option = highlightedOption || options[0];
      handleOptionOnKeyDown(option, event.currentTarget);
      handleOnSelection();
    } else if (event.key === 'Tab' && options.length > 0 && !notFound) {
      const option = options[0];
      handleOptionOnKeyDown(option, event.currentTarget);
      event.preventDefault();
      handleOnSelection();
    }
  }

  function handleOptionOnKeyDown(option: ResultTypeMap[T], currentTarget: EventTarget & HTMLInputElement) {
    const inputString = config.formatOption(option);
    trackEvent({
      category: 'Input',
      action: 'select',
      element: currentTarget,
      content: config.trackingContent(option),
    });
    setSelectedOption(option);
    setSelected(option);
    setInput(inputString);
    setOptions(results ? filterOptions(results, inputString) : []);
  }

  function handleOnInputClick() {
    if (!flyoutMode && !acOpen && type === 'city') {
      setAcOpen(true);
      setAcType('page');
      trackAcEvent({ acType: 'page', acInteraction: 'open' });
    }
  }

  function onHandleReset() {
    setSelectedOption(undefined);
    setSelected(undefined);
    setInput('');
    setOptions([]);
    setResults(undefined);
    setNotFound(false);
  }

  const label = config.label;
  const placeholder = config.placeholder;

  return (
    <div className={cn(inputStyles({ flyoutMode }), className)} ref={containerRef}>
      {config.dependencyKey && <AvailabilityFormInputOverlay enabled={!dependency} />}
      <TextInput
        id={id}
        ref={textInputRef}
        label={label}
        placeholder={placeholder}
        value={input}
        invalid={notFound}
        invalidText="Keine Übereinstimmung gefunden"
        onChange={handleOnInputChange}
        onKeyDown={handleKeyDown}
        onFocus={handleOnFocus}
        onBlur={handleOnBlur}
        onClick={handleOnInputClick}
        onClickErrorIcon={onHandleReset}
        autoComplete="new-password"
        disabled={config.dependencyKey && !dependency}
        hotjarSuppress={true}
      />
      {isFetching && !notFound && <SvgLoadingSm className={inputLoadingStyles()} />}
      {showList && options.length > 0 && (
        <ul className={inputSelectionBoxStyles()} data-hj-suppress="">
          {options.map((option, index) => (
            <li
              key={index}
              ref={(el) => {
                if (el) {
                  optionRefs.current.set(index, el);
                } else {
                  optionRefs.current.delete(index);
                }
              }}
              onClick={(event) => handleOnOptionClick(option, event)}
              className={listItemStyles({ selected: option === highlightedOption })}
            >
              {config.formatOption(option)}
            </li>
          ))}
        </ul>
      )}
      {showList && input.length < config.minChars && (
        <div className={inputSelectionBoxStyles()}>
          <div className={inputHelperTextStyles()}>Geben Sie mind. {config.minChars} Zeichen an</div>
        </div>
      )}
    </div>
  );
}
