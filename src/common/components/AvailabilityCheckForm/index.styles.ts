import { cva } from 'class-variance-authority';

export const availabilityCheckInputGroupStyles = cva('grid w-full grid-cols-1 gap-x-4 xl:gap-x-6', {
  variants: {
    flyoutMode: {
      true: 'lg:grid-cols-3 lg:grid-rows-[auto_auto] lg:gap-y-4',
      false: 'lg:grid-cols-4',
    },
  },
});

export const availabilityCheckButtonStyles = cva('mt-[25px]', {
  variants: {
    buttonMobileVisible: {
      true: '',
      false: 'hidden lg:flex',
    },
    flyoutMode: {
      true: 'lg:col-span-3 lg:justify-self-center xl:mt-0',
      false: 'xl:mt-[25px]',
    },
  },
});
