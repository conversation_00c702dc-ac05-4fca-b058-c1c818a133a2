'use client';

import { useRef, useState } from 'react';

import { getOfferList } from '@/apis/dg/offerList';

import Button from '@/components/Button';
import { ArrowRightSm, LoadingSm } from '@/components/Icons/sm';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useUserStore } from '@/stores/UserStore';

import { FormInput } from './FormInput';
import { availabilityCheckButtonStyles, availabilityCheckInputGroupStyles } from './index.styles';

type AvailabilityCheckFormProps = {
  id: string;
  flyoutMode?: boolean;
  openFlyout?: boolean;
  standAlone?: boolean;
};

export default function AvailabilityCheckForm({
  id,
  flyoutMode = false,
  openFlyout = false,
}: AvailabilityCheckFormProps) {
  const { trackAcEvent } = useTracking();

  const acType = useAvailabilityCheckStore.use.acType();
  const citySelected = useAvailabilityCheckStore.use.citySelected();
  const streetSelected = useAvailabilityCheckStore.use.streetSelected();
  const houseNumberSelected = useAvailabilityCheckStore.use.houseNumberSelected();
  const isAvailabilityModalOpen = useAvailabilityCheckStore.use.isAcModalOpen();
  const resetSearch = useAvailabilityCheckStore.use.resetSearch();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();

  const loading = useUserStore.use.loading();
  const provider = useUserStore.use.provider();
  const setOfferList = useUserStore.use.setOfferList();
  const setAcAddress = useUserStore.use.setAcAddress();
  const setLoading = useUserStore.use.setLoading();
  const setError = useUserStore.use.setError();
  const setHouseId = useUserStore.use.setHouseId();

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const streetInputRef = useRef<HTMLInputElement>(null);
  const houseNrInputRef = useRef<HTMLInputElement>(null);
  const [buttonMobileVisible, setButtonMobileVisible] = useState(false);

  async function onSubmit() {
    setLoading(true);
    if (citySelected && streetSelected && houseNumberSelected) {
      const houseId = houseNumberSelected.id;

      if (typeof provider !== 'string' || !provider) {
        console.error('Provider is missing or invalid. Cannot fetch offer list.');
        setError('Provider configuration error.');
        setLoading(false);
        return;
      }

      try {
        const data = await getOfferList({ provider, houseId });

        if (!data) {
          throw new Error('Failed to fetch offer list or no data returned.');
        }

        const acInteraction =
          data.address?.isHappyFlow || data.address?.cms?.isIndustrialParkHappyflow ? 'positive' : 'negative';
        setHouseId(houseId);
        setOfferList(data);
        setAcAddress({
          zipCode: citySelected.zipcode,
          city: citySelected.city,
          street: streetSelected.name,
          houseNumber: houseNumberSelected.houseNumber,
          houseNumberExtension: houseNumberSelected.houseNumberExtension,
        });
        resetSearch();
        setLoading(false);
        trackAcEvent({ acType, acInteraction, offerList: data });
        if (!isAvailabilityModalOpen && openFlyout) {
          setAcModalOpen(true);
        }
      } catch (err) {
        console.error('Error during offer list fetch or processing:', err);
        setLoading(false);
        setError('An error occurred while fetching data');
      }
    }
  }

  function onCitySelected() {
    if (streetInputRef.current) {
      (streetInputRef.current as HTMLInputElement).disabled = false;
      (streetInputRef.current as HTMLInputElement).closest('.hidden')?.classList.remove('hidden');
      (streetInputRef.current as HTMLInputElement).focus();
    }
  }

  function onStreetSelected() {
    if (houseNrInputRef.current) {
      (houseNrInputRef.current as HTMLInputElement).disabled = false;
      (houseNrInputRef.current as HTMLInputElement).closest('.hidden')?.classList.remove('hidden');
      (houseNrInputRef.current as HTMLInputElement).focus();
    }
  }

  function onHouseNumberSelected() {
    if (submitButtonRef.current) {
      (submitButtonRef.current as HTMLButtonElement).disabled = false;
      submitButtonRef.current.classList.remove('hidden');
      (submitButtonRef.current as HTMLButtonElement).focus();
      setButtonMobileVisible(true);
    }
  }

  const SubmitButtonIcon = loading ? <LoadingSm className={'animate-spin'} /> : <ArrowRightSm />;

  return (
    <div className={availabilityCheckInputGroupStyles({ flyoutMode })}>
      <FormInput<'city'> id={`${id}-city-input`} type="city" flyoutMode={flyoutMode} onSelected={onCitySelected} />
      <FormInput<'street'>
        id={`${id}-street-input`}
        className={'hidden lg:block'}
        type="street"
        flyoutMode={flyoutMode}
        textInputRef={streetInputRef}
        onSelected={onStreetSelected}
      />
      <FormInput<'houseNumber'>
        id={`${id}-house-number-input`}
        className={'hidden lg:block'}
        type="houseNumber"
        flyoutMode={flyoutMode}
        textInputRef={houseNrInputRef}
        onSelected={onHouseNumberSelected}
      />

      <Button
        ref={submitButtonRef}
        className={availabilityCheckButtonStyles({ buttonMobileVisible, flyoutMode })}
        icon={SubmitButtonIcon}
        variant={'primary'}
        label={flyoutMode ? 'Zum nächsten Schritt' : 'Verfügbarkeit prüfen'}
        disabled={!citySelected || !streetSelected || !houseNumberSelected || loading}
        onClick={onSubmit}
        data-testid="ac-submit-button"
      />
    </div>
  );
}
