import Button from '@/components/Button';
import TextLink from '@/components/TextLink';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useUserStore } from '@/stores/UserStore';

import { getAvailabilityInfo } from '@/utils/availabilityCheck/getAvailabilityInfo';
import { getOfferCta } from '@/utils/availabilityCheck/getOfferCta';

type ActionBarProps = {
  variant: 'a' | 'b';
  currentStepIndex: number;
  isStepValid: boolean;
  onBack: () => void;
  onNext: () => void;
};

export default function ActionBar({ variant, isStepValid, onBack, onNext }: ActionBarProps) {
  const offerList = useUserStore.use.offerList();
  const provider = useUserStore.use.provider();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const livingSituation = useUserStore.use.livingSituation();

  function handleCloseModal() {
    setAcModalOpen(false);
  }

  if (!offerList || !provider) {
    return null;
  }

  const { isAvailable } = getAvailabilityInfo(offerList);

  if (!livingSituation && variant === 'b' && provider === 'DGH') {
    return (
      <div className="sticky bottom-0 z-10 flex h-20 items-center justify-between bg-gray-50 px-4 py-4 shadow-[0_-2px_4px_rgba(0,0,0,0.1)] xl:static xl:px-12 xl:shadow-none">
        <div>
          <TextLink onClick={onBack}>Zurück</TextLink>
        </div>
        {isAvailable && (
          <div>
            <Button label={'Zum Ergebnis'} variant="primary" onClick={onNext} disabled={!isStepValid} />
          </div>
        )}
      </div>
    );
  }

  const { label, href } = getOfferCta(provider, offerList);
  const projectSlug = offerList?.projectSlug ? `/netzausbau/gebiete/${offerList.projectSlug}` : '/netzausbau';

  return (
    <div className="sticky bottom-0 z-10 flex h-20 items-center justify-between bg-gray-50 px-4 py-4 shadow-[0_-2px_4px_rgba(0,0,0,0.1)] xl:static xl:px-12 xl:shadow-none">
      <div>
        <TextLink onClick={onBack}>Zurücksetzen</TextLink>
      </div>
      {isAvailable && (
        <div className="flex items-center gap-4">
          <TextLink href={projectSlug} onClick={handleCloseModal} className="hidden xl:block">
            Mehr zum Ausbau
          </TextLink>
          {livingSituation?.personType !== 'landlord' && (
            <Button label={label} variant="primary" href={href} onClick={handleCloseModal} />
          )}
        </div>
      )}
    </div>
  );
}
