'use client';

import React from 'react';

import AddressNotFoundInfo from '@/components/AddressNotFoundInfo';
import AvailabilityCheckForm from '@/components/AvailabilityCheckForm';
import ContentIntro from '@/components/AvailabilityCheckModal/FormWizard/ContentIntro';
import Copy from '@/components/Copy';
import { CheckMd } from '@/components/Icons/md';
import TextLink from '@/components/TextLink';

import { useUserStore } from '@/stores/UserStore';

export default function AvailabilityCheckStep() {
  const provider = useUserStore.use.provider();
  const isPrivateCustomer = provider === 'DGH';

  return (
    <>
      <div className="space-y-4 rounded-lg bg-sand p-4 xl:space-y-8 xl:rounded-none xl:bg-transparent xl:p-0">
        <ContentIntro
          headline="Adresse"
          text="Wir sind gerne für Sie da und beraten Sie individuell! Einfach Anschrift angeben und Ihre Glasfaser Verfügbarkeit prüfen."
          icon={<CheckMd width={80} height={80} className="hidden xl:block" />}
        />

        <div className="space-y-2 rounded-lg bg-sand xl:rounded-[20px] xl:p-8">
          <AvailabilityCheckForm id="ac-modal" flyoutMode={true} />
          <AddressNotFoundInfo />
        </div>
      </div>

      <div className="mt-8 flex flex-col items-center justify-center gap-2">
        {isPrivateCustomer ? (
          <>
            <Copy>Sie wollen Glasfaser für Ihr Unternehmen buchen?</Copy>
            <TextLink href="/geschaeftskunden">Zu Selbstständige & Unternehmen</TextLink>
          </>
        ) : (
          <>
            <Copy>Sie wollen Glasfaser als Privatperson buchen?</Copy>
            <TextLink href="/">Zu Privatkunden</TextLink>
          </>
        )}
      </div>
    </>
  );
}
