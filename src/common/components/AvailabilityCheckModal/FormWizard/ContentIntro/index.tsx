import React, { useEffect, useRef } from 'react';

import Copy from '@/components/Copy';
import Headline from '@/components/Headline';
import { InfoQSm } from '@/components/Icons/sm';
import ModalWrapper from '@/components/ModalWrapper';

import { cn } from '@/utils/cn';

type ContentIntroProps = {
  icon: React.ReactNode;
  headline: string;
  text: string;
  info?: React.ReactNode;
  className?: string;
};

function ContentIntro({ icon, headline, text, info, className }: ContentIntroProps) {
  const textContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      textContainerRef.current?.focus();
    }, 300); // Match animation duration
    return () => clearTimeout(timeout);
  }, []);

  return (
    <div className={cn('flex items-center gap-4 xl:p-0', className)}>
      {icon}
      <div>
        <Headline intent="h5">{headline}</Headline>
        <div
          ref={textContainerRef}
          tabIndex={-1}
          className="flex items-start gap-2 outline-none focus:outline-none xl:items-center"
        >
          <Copy>{text}</Copy>
          {info && (
            <ModalWrapper
              label={headline}
              labelHidden
              inline
              icon={<InfoQSm />}
              className="rounded-t-lg bg-white xl:rounded-[20px]"
            >
              {info}
            </ModalWrapper>
          )}
        </div>
      </div>
    </div>
  );
}

export default ContentIntro;
