import useSWR from 'swr';

import type { TextButtonModule } from '@/apis/contentful/generated/types';
import { fetchContentById } from '@/apis/contentful/utils/fetchContentById';

import Headline from '@/components/Headline';
import { RichText } from '@/components/RichText';

import type { RichTextLinks } from '@/types/pageProps';

function LivingSituationInfo() {
  const { data } = useSWR<TextButtonModule>(
    { id: '4OPpAVMuIN7yK7amnSDaQE', contentType: 'TextButtonModule' },
    fetchContentById,
  );

  if (!data) return null;

  const { headline, text } = data;

  return (
    <div>
      <Headline intent="h3">{headline}</Headline>
      {text && <RichText document={text.json} links={text.links as unknown as RichTextLinks} />}
    </div>
  );
}

export default LivingSituationInfo;
