'use client';

import React, { useRef } from 'react';

import ContentIntro from '@/components/AvailabilityCheckModal/FormWizard/ContentIntro';
import GridSelect from '@/components/GridSelect';
import {
  EinfamilienhausMd,
  HomeCheckMd,
  HomePersonMd,
  MehrfamilienhausMd,
  MieterMd,
  VermieterMd,
} from '@/components/Icons/md';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';

import type { TrackingElement } from '@/types/tracking';

import LivingSituationInfo from './LivingSituationInfo';

export const personTypeForTracking = {
  owner: 'eigentuemer',
  tenant: 'mieter',
  landlord: 'vermieter',
};

export default function LivingSituationStep() {
  const { trackEvent } = useTracking();

  const personTypeSelection = useAvailabilityCheckStore.use.personTypeSelection();
  const setPersonTypeSelection = useAvailabilityCheckStore.use.setPersonTypeSelection();
  const housingTypeSelection = useAvailabilityCheckStore.use.housingTypeSelection();
  const setHousingTypeSelection = useAvailabilityCheckStore.use.setHousingTypeSelection();

  const personTypeRef = useRef<HTMLDivElement>(null);
  const housingTypeRef = useRef<HTMLDivElement>(null);

  const iconStyles = 'h-8 w-8 shrink-0 xl:h-10 xl:w-10';

  function handleSelectionChange(newValue: string | null, type: 'person' | 'housing') {
    if (type === 'person') {
      setPersonTypeSelection(newValue);
      if (newValue === 'landlord') {
        setHousingTypeSelection(null);
      }
      if (newValue && personTypeRef.current) {
        trackEvent({
          category: 'Select',
          action: 'select',
          element: personTypeRef.current as TrackingElement,
          content: `Ich bin - ${personTypeForTracking[newValue as keyof typeof personTypeForTracking]}`,
        });
      }
    } else {
      setHousingTypeSelection(newValue);
      if (newValue && housingTypeRef.current) {
        trackEvent({
          category: 'Select',
          action: 'select',
          element: housingTypeRef.current as TrackingElement,
          content: `Ich wohne in - ${newValue}`,
        });
      }
    }
  }

  return (
    <div className="space-y-4 rounded-lg bg-sand p-4 xl:space-y-8 xl:rounded-none xl:bg-transparent xl:p-0">
      <ContentIntro
        icon={<HomeCheckMd width={80} height={80} className="hidden xl:block" />}
        headline="Wohnsituation"
        text="Bitte geben Sie die Ihrer Wohnsituation entsprechenden Daten an."
        info={<LivingSituationInfo />}
      />

      <div className="space-y-6 xl:rounded-[20px] xl:bg-sand xl:p-8">
        <div className="space-y-4">
          <div ref={personTypeRef}>
            <GridSelect
              id="person-type"
              label="Ich bin …"
              value={personTypeSelection ?? null}
              onChange={(e) => handleSelectionChange(e.target.value, 'person')}
              options={[
                {
                  value: 'owner',
                  label: 'Eigentümer (Eigennutzung)',
                  icon: <HomePersonMd width={40} height={40} className={iconStyles} />,
                },
                {
                  value: 'tenant',
                  label: 'Mieter',
                  icon: <MieterMd width={40} height={40} className={iconStyles} />,
                },
                {
                  value: 'landlord',
                  label: 'Vermieter',
                  icon: <VermieterMd width={40} height={40} className={iconStyles} />,
                },
              ]}
            />
          </div>
        </div>

        {personTypeSelection !== 'landlord' && (
          <div className="space-y-4">
            <div ref={housingTypeRef}>
              <GridSelect
                id="housing-type"
                label="Ich wohne in …"
                value={housingTypeSelection ?? null}
                onChange={(e) => handleSelectionChange(e.target.value, 'housing')}
                options={[
                  {
                    value: 'single',
                    label: 'Ein-/Zweifamilienhaus',
                    icon: <EinfamilienhausMd width={40} height={40} className={iconStyles} />,
                  },
                  {
                    value: 'multi',
                    label: 'Mehrfamilienhaus',
                    icon: <MehrfamilienhausMd width={40} height={40} className={iconStyles} />,
                  },
                ]}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
