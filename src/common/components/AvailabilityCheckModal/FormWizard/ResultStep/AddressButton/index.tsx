import React from 'react';

import { PencilSm } from '@/components/Icons/sm';

import useTracking from '@/hooks/useTracking';

import { cn } from '@/utils/cn';

type AddressButtonProps = {
  userAddress: string;
  className?: string;
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
};

export default function AddressButton({ userAddress, onClick, className }: AddressButtonProps) {
  const { trackEvent } = useTracking();

  function handleClick(e: React.MouseEvent<HTMLButtonElement>) {
    trackEvent({
      category: 'Button',
      action: 'click',
      element: e.currentTarget,
      content: 'address-reset',
    });
    onClick(e);
  }

  return (
    <button
      className={cn('flex h-12 items-center justify-between rounded-[20px] bg-sand px-4 py-2', className)}
      onClick={handleClick}
    >
      <span className="truncate font-semibold">{userAddress}</span>
      <span className="ml-4 flex gap-1 font-semibold">
        ändern <PencilSm />
      </span>
    </button>
  );
}
