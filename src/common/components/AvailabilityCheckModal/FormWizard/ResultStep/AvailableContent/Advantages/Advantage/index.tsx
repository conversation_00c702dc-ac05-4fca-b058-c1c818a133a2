import type { Advantage } from '@/apis/dg/types';

import Copy from '@/components/Copy';
import { SuccessSm } from '@/components/Icons/sm';
import LegalNote from '@/components/LegalNote';

import { cn } from '@/utils/cn';

type AdvantageProps = {
  advantage: Advantage;
};

function Advantage({ advantage }: AdvantageProps) {
  const { title, text, legalNote } = advantage;

  return (
    <div className={cn('grid grid-cols-[auto_1fr] items-start gap-2')}>
      <SuccessSm width={32} height={32} className="lg:size-6" />
      <div>
        <Copy weight="bold">{title}</Copy>
        <Copy>
          {text}
          {legalNote && <LegalNote text={legalNote.text} title={legalNote.title} textType={legalNote.textType} />}
        </Copy>
      </div>
    </div>
  );
}

export default Advantage;
