import { cn } from '@/utils/cn';

import Advantage from './Advantage';

type AdvantagesProps = {
  advantages: Advantage[] | undefined;
  className?: string;
};

function Advantages({ advantages, className }: AdvantagesProps) {
  if (!advantages) return null;

  return (
    <div className={cn('rounded-lg bg-sand p-4 xl:rounded-[20px]', className)}>
      <h3 className="mb-3 font-semibold xl:text-center">Ihre Vorteile</h3>
      <div className="grid grid-cols-1 gap-3 text-sm md:grid-cols-3 md:gap-4">
        {advantages.map((advantage) => (
          <Advantage key={advantage.title} advantage={advantage} />
        ))}
      </div>
    </div>
  );
}

export default Advantages;
