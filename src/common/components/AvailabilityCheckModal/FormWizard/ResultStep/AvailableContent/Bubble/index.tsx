import Copy from '@/components/Copy';
import { AvatarMd } from '@/components/Icons/md';
import LegalNote from '@/components/LegalNote';

import type { LegalNotification } from '@/types/legalNote';

import { cn } from '@/utils/cn';

type BubbleProps = {
  text?: string;
  legalNote?: LegalNotification;
  className?: string;
};

function Bubble({ text, legalNote, className }: BubbleProps) {
  return (
    <div className={cn('flex max-w-[355px] items-start', className)}>
      {/* Speech bubble pointer */}
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="26" viewBox="0 0 18 26" fill="none">
        <path d="M1.93978 0C0.339777 0 -0.570223 1.82 0.399777 3.1L17.4898 25.75V0H16.5998H1.93978Z" fill="#F4F4F0" />
      </svg>

      {/* Main bubble */}
      <div className="relative z-10 -ml-1 flex w-full items-center gap-2 rounded-r-[10px] rounded-b-[10px] bg-sand p-3">
        <AvatarMd width={50} height={50} className="flex-shrink-0" />
        <Copy className="font-medium">
          <span>
            {text}
            {legalNote && <LegalNote title={legalNote.title} text={legalNote.text} textType={legalNote.textType} />}
          </span>
        </Copy>
      </div>
    </div>
  );
}

export default Bubble;
