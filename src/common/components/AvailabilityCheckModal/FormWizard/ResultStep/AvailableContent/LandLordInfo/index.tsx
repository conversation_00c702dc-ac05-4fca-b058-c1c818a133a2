import React from 'react';

import Button from '@/components/Button';
import Copy from '@/components/Copy';

import { cn } from '@/utils/cn';

type LandlordInfoProps = {
  className?: string;
};

export default function LandlordInfo({ className }: LandlordInfoProps) {
  return (
    <div className={cn('flex flex-col items-center gap-2 rounded-lg bg-sand p-4 xl:rounded-[20px]', className)}>
      <Copy weight="bold">Ich bin Vermieter</Copy>
      <Copy>
        Damit Ihre Mieter einen Vertrag abschließen können, müssen Sie als Vermieter einen Gestattungsvertrag bei uns
        hinterlegen, der den Ausbau ermöglicht.
      </Copy>
      <Button variant="primary" label="Zum Vermieterportal" href="/wohnungswirtschaft/gestattungsvertraege" />
    </div>
  );
}
