import useSWR from 'swr';

import type {
  AccordionItem as AccordionItemComponent,
  Accordion as AccordionModule,
} from '@/apis/contentful/generated/types';
import { fetchContentById } from '@/apis/contentful/utils/fetchContentById';

import Accordion from '@/components/Accordion';
import AccordionItem from '@/components/Accordion/AccordionItem';
import Headline from '@/components/Headline';
import { RichText } from '@/components/RichText';

import type { RichTextLinks } from '@/types/pageProps';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';

function PhasesFaq({ currentPhase }: { currentPhase: string }) {
  const { data } = useSWR<AccordionModule>(
    { id: '3YB2n9CfRpPfhi94fK3juJ', contentType: 'Accordion' },
    fetchContentById,
  );

  if (!data || !data.accordionItemsCollection) return null;

  const defaultExpanded = getSanitizedIdSelector(currentPhase);

  return (
    <div className="max-h-[calc(100vh-150px)] space-y-4 overflow-y-auto xl:h-auto">
      <Headline intent={'h3'} className="xl:text-center">
        {data.headline}
      </Headline>
      <Accordion id={'phases-faq-accordion'} defaultExpanded={[defaultExpanded]}>
        {data.accordionItemsCollection.items
          .filter((item): item is AccordionItemComponent => item !== null)
          .map((item, index: number) => (
            <AccordionItem
              key={index}
              label={item.label ?? 'accordion-item'}
              value={getSanitizedIdSelector(item.label ?? '')}
            >
              {item?.content && (
                <RichText document={item.content.json} links={item.content.links as unknown as RichTextLinks} />
              )}
            </AccordionItem>
          ))}
      </Accordion>
    </div>
  );
}

export default PhasesFaq;
