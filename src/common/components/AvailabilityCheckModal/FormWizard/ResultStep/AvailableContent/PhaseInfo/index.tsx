import React from 'react';

import Copy from '@/components/Copy';
import { InfoQSm } from '@/components/Icons/sm';
import ModalWrapper from '@/components/ModalWrapper';

import { cn } from '@/utils/cn';

import PhasesFaq from './PhasesFaq';

type PhaseInfoProps = {
  currentPhase: string | null;
  className?: string;
};

function PhaseInfo({ currentPhase, className }: PhaseInfoProps) {
  if (!currentPhase) return null;

  return (
    <div className={cn('flex items-center justify-center gap-3', className)}>
      <Copy weight="bold">Ihre Phase: {currentPhase}</Copy>
      <ModalWrapper
        label={`${currentPhase}-Hilfe`}
        labelHidden
        inline
        icon={<InfoQSm />}
        className="bg-white xl:w-[1158px]"
      >
        <PhasesFaq currentPhase={currentPhase} />
      </ModalWrapper>
    </div>
  );
}

export default PhaseInfo;
