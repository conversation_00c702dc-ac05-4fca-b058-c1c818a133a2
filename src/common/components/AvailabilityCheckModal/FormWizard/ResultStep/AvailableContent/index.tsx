import type { LivingSituation, OfferList } from '@/apis/dg/types';

import TextLink from '@/components/TextLink';

import Advantages from './Advantages';
import Bubble from './Bubble';
import LandlordInfo from './LandLordInfo';
import PhaseInfo from './PhaseInfo';

type AvailableContentProps = {
  offerList: OfferList;
  livingSituation?: LivingSituation | null;
};

export default function AvailableContent({ offerList, livingSituation }: AvailableContentProps) {
  const { projectStatusGroupTitle, address, infoText } = offerList;

  const advantages = address?.cms?.availabilityFlyoutInfos?.advantages ?? offerList.campaign?.advantages;
  const bubbleText =
    address?.cms?.availabilityFlyoutInfos?.phone ??
    infoText?.text ??
    'Jetzt Vertrag abschließen & Glasfaser-Internet ins Zuhause bringen.';
  const legalNote = offerList.infoText?.legalNote;

  return (
    <>
      <Bubble text={bubbleText} legalNote={legalNote} className="order-5 xl:order-none" />
      <PhaseInfo currentPhase={projectStatusGroupTitle} className="order-3 xl:order-none xl:col-span-2" />
      {livingSituation?.personType === 'landlord' ? (
        <LandlordInfo className="order-4 xl:order-none xl:col-span-2" />
      ) : (
        <Advantages advantages={advantages} className="order-4 xl:order-none xl:col-span-2" />
      )}

      <TextLink
        href={offerList?.projectSlug ? `/netzausbau/${offerList.projectSlug}` : '#'}
        className="order-last justify-self-center xl:hidden"
      >
        Mehr zum Ausbau
      </TextLink>
    </>
  );
}
