import {
  NOT_<PERSON><PERSON><PERSON><PERSON>LE_CONTENT_BUTTON_TEXT,
  NOT_AVAILABLE_CONTENT_DESCRIPTION,
  NOT_AVAILABLE_CONTENT_TITLE,
} from '@/app/consts';

import type { OfferList } from '@/apis/dg/types';

import AvailabilityCheckContact from '@/components/AvailabilityCheckContact';
import Button from '@/components/Button';
import Headline from '@/components/Headline';
import { InfoCircleSm } from '@/components/Icons/sm';

import { INTERESTED_PERSON_URL } from '@/utils/specialUrls/specialUrlsCatalog';

type NotAvailableContentProps = {
  provider: string;
  offerList: OfferList;
};

export default function NotAvailableContent({ provider, offerList }: NotAvailableContentProps) {
  const title = offerList.address?.cms?.title ?? NOT_AVAILABLE_CONTENT_TITLE;
  const description = offerList.address?.cms?.description ?? NOT_AVAILABLE_CONTENT_DESCRIPTION;
  const buttonText = offerList.address?.cms?.buttonText ?? NOT_AVAILABLE_CONTENT_BUTTON_TEXT;

  return (
    <>
      <div className="flex gap-2 self-center">
        <InfoCircleSm width={32} height={32} className="flex-shrink-0" />
        <AvailabilityCheckContact />
      </div>
      <div className="space-y-2 border-t border-gray-200 pt-4 xl:border-t-0 xl:px-10 xl:pt-0">
        <Headline intent="h5" type="p" className="px-10 text-center xl:p-0 xl:text-left">
          {title}
        </Headline>
        <div className="mb-6 px-10 xl:p-0" dangerouslySetInnerHTML={{ __html: description }} />
        {/* Dont show to business customer */}
        {provider === 'DGH' && <Button variant="primary" label={buttonText} href={INTERESTED_PERSON_URL} />}
      </div>
    </>
  );
}
