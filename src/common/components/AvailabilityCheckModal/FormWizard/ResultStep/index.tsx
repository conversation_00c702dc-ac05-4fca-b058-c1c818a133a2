import React from 'react';

import ContentIntro from '@/components/AvailabilityCheckModal/FormWizard/ContentIntro';
import ConditionalWrapper from '@/components/ConditionalWrapper';
import { KeinAnschlussMd, ThumbMd } from '@/components/Icons/md';

import { useUserStore } from '@/stores/UserStore';

import { getAvailabilityInfo } from '@/utils/availabilityCheck/getAvailabilityInfo';

import AddressButton from './AddressButton';
import AvailableContent from './AvailableContent';
import NotAvailableContent from './NotAvailableContent';

export default function AvailabilityResult() {
  const provider = useUserStore.use.provider();
  const offerList = useUserStore.use.offerList();
  const resetOfferList = useUserStore.use.resetOfferList();
  const livingSituation = useUserStore.use.livingSituation();
  const getUserAddressAsString = useUserStore.use.getUserAddressAsString();

  if (!offerList) {
    return null;
  }

  const userAddress = getUserAddressAsString();

  const { isAvailable } = getAvailabilityInfo(offerList);
  const { projectStatusGroupCode } = offerList;

  // Determine which icon and text to display based on projectStatusGroupCode
  let icon = <KeinAnschlussMd width={80} height={80} className="hidden xl:block" />;
  let text = 'Ohje! Ein Glasfaser-Anschluss ist derzeit nicht möglich.';

  if (isAvailable && projectStatusGroupCode !== null && projectStatusGroupCode >= 1) {
    icon = <ThumbMd width={80} height={80} className="hidden xl:block" />;
    text = 'Super! Ein Glasfaser-Anschluss ist geplant.';

    if (projectStatusGroupCode >= 4 && projectStatusGroupCode < 5) {
      text = 'Super! Glasfaser wird ausgebaut.';
    } else if (projectStatusGroupCode >= 5) {
      text = 'Super! Glasfaser ist verfügbar.';
    }
  }

  function handleResetAvailabilityCheck() {
    resetOfferList();
  }

  return (
    <div className="grid grid-cols-1 gap-4 xl:grid-cols-2">
      <ContentIntro
        icon={icon}
        headline="Ergebnis"
        text={text}
        className={isAvailable ? 'order-1 xl:order-none xl:col-span-2' : ''}
      />
      <AddressButton
        userAddress={userAddress}
        onClick={handleResetAvailabilityCheck}
        className={isAvailable ? 'order-2 xl:order-none' : ''}
      />

      <ConditionalWrapper
        condition={isAvailable}
        wrapper={() => <AvailableContent offerList={offerList} livingSituation={livingSituation} />}
      >
        <NotAvailableContent provider={provider} offerList={offerList} />
      </ConditionalWrapper>
    </div>
  );
}
