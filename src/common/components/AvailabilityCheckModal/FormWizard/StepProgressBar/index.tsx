'use client';

import React from 'react';

import { CheckedSm } from '@/components/Icons/sm';

import { cn } from '@/utils/cn';

type Step = {
  id: number;
  label: string;
};

type StepProgressBarProps = {
  steps: Step[];
  currentStep: number;
};

function StepProgressBar({ steps, currentStep }: StepProgressBarProps) {
  const totalSteps = steps.length;

  return (
    <div className="relative mx-auto w-full px-4 xl:static xl:px-12">
      <ol className="flex w-full justify-center">
        {steps.map((step, index) => {
          const displayStepNumber = index + 1;
          const isCompleted = displayStepNumber < currentStep;
          const isActive = displayStepNumber === currentStep;
          const isFirst = index === 0;
          const isLast = index === totalSteps - 1;

          return (
            <li key={step.id} className="flex max-w-32 flex-1 flex-col items-center">
              {/* Bar Element Div (Contains Circle) */}
              <div
                className={cn(
                  'flex items-center bg-basalt p-[2px]',
                  isFirst || isLast ? 'w-[60%]' : 'w-full',
                  isFirst ? 'ml-auto' : '',
                  isLast ? 'mr-auto' : '',
                  isFirst ? 'rounded-l-full' : '',
                  isLast ? 'rounded-r-full' : '',
                  isFirst ? 'justify-start' : '',
                  !isFirst && !isLast ? 'justify-center' : '',
                  isLast ? 'justify-end' : '',
                )}
              >
                {/* Circle Div */}
                <div
                  className={cn(
                    'flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full font-semibold',
                    isCompleted || isActive ? 'bg-yellow-400' : 'bg-gray-50',
                  )}
                >
                  {isCompleted ? (
                    <CheckedSm className="h-5 w-5 text-basalt" aria-hidden="true" />
                  ) : (
                    <span className={cn(isActive ? 'text-basalt' : 'text-white')}>{displayStepNumber}</span>
                  )}
                </div>
              </div>

              {/* Label Span (Below Bar Element) */}
              <span className={cn('mt-2 text-sm text-basalt', isActive ? 'font-semibold' : 'font-medium')}>
                {isCompleted && <span className="sr-only">Abgeschlossen: </span>}
                {isActive && <span className="sr-only">Aktuell: </span>}
                {step.label}
              </span>
            </li>
          );
        })}
      </ol>
    </div>
  );
}

export default StepProgressBar;
