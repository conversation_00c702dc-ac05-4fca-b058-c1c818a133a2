import React from 'react';

import '@testing-library/jest-dom';
import { act, render, screen, waitFor, within } from '@testing-library/react';

import type { OfferList } from '@/apis/dg/types';

import { useUserStore } from '@/stores/UserStore';

import FormWizard from '..';

// Mock child components
jest.mock('../AvailabilityCheckStep', () => {
  const Mock = () => <div data-testid="availability-check-step">AvailabilityCheckStep Mock</div>;
  Mock.displayName = 'MockAvailabilityCheckStep';
  return Mock;
});

jest.mock('../LivingSituationStep', () => {
  const Mock = () => (
    <div data-testid="living-situation-step">
      LivingSituationStep Mock
      <button>Zurück</button>
      <button>Weiter</button>
    </div>
  );
  Mock.displayName = 'MockLivingSituationStep';
  return Mock;
});

jest.mock('../ResultStep', () => {
  const Mock = () => (
    <div data-testid="result-step">
      AvailabilityResult Mock
      <button>Zurück</button>
      <button><PERSON><PERSON></button>
    </div>
  );
  Mock.displayName = 'MockResultStep';
  return Mock;
});

jest.mock('../StepProgressBar', () => {
  const Mock = ({ steps, currentStep }: { steps: any[]; currentStep: number }) => (
    <div data-testid="step-progress-bar">
      Steps: {steps.length}, Current: {currentStep}
      <div data-testid="progress-steps">{JSON.stringify(steps)}</div>
    </div>
  );
  Mock.displayName = 'MockStepProgressBar';
  return Mock;
});

// Mock Zustand store
jest.mock('@/stores/UserStore');

// Mock framer-motion to avoid animation timing issues
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock getAvailabilityInfo to always return isAvailable: true
jest.mock('@/utils/availabilityCheck/getAvailabilityInfo', () => ({
  getAvailabilityInfo: () => ({ isAvailable: true }),
}));

// Cast the imported hook to its mocked function type
const mockUseUserStore = useUserStore as jest.MockedFunction<typeof useUserStore>;

// Create a store state object to track changes
let storeState: {
  offerList: OfferList | null | undefined;
  livingSituation: any;
  provider: 'DGH' | 'DGP' | undefined;
} = {
  offerList: undefined,
  livingSituation: undefined,
  provider: undefined,
};

// Mock functions
const mockResetOfferList = jest.fn();
const mockSetLivingSituation = jest.fn();

// Helper function to set up mock implementation
const setupMockStore = () => {
  // Mock the store's use property with dynamic getters for each selector
  (mockUseUserStore as any).use = {
    offerList: () => storeState.offerList,
    livingSituation: () => storeState.livingSituation,
    provider: () => storeState.provider,
    resetOfferList: () => mockResetOfferList,
    setLivingSituation: () => mockSetLivingSituation,
  };
};

describe('FormWizard Component', () => {
  beforeEach(() => {
    // Reset store state and mocks
    storeState = {
      offerList: undefined,
      livingSituation: undefined,
      provider: undefined,
    };
    mockResetOfferList.mockClear();
    mockSetLivingSituation.mockClear();
    setupMockStore();

    // Clean up window property
    delete (window as any).vcSplitTestVariante;
  });

  afterEach(() => {
    // Clean up window property
    delete (window as any).vcSplitTestVariante;
    jest.clearAllMocks();
  });

  describe('Variant A (window.vcSplitTestVariante = "a")', () => {
    it('navigates from Step 1 directly to Step 3 when offerList is populated', async () => {
      // Set up variant before rendering
      Object.defineProperty(window, 'vcSplitTestVariante', {
        value: 'a',
        writable: true,
        configurable: true,
      });

      const { rerender } = render(<FormWizard />);

      // Wait for initial render on step 1
      await waitFor(() => {
        expect(screen.getByTestId('availability-check-step')).toBeInTheDocument();
        expect(screen.getByTestId('step-progress-bar')).toHaveTextContent('Steps: 2, Current: 1');
      });

      // Update store state and trigger re-render
      await act(async () => {
        storeState.offerList = { offers: [{ id: '1' }] } as any;
        setupMockStore();
        rerender(<FormWizard />);
      });

      // Wait for transition to step 3
      await waitFor(() => {
        expect(screen.getByTestId('result-step')).toBeInTheDocument();
        expect(screen.queryByTestId('availability-check-step')).not.toBeInTheDocument();
        expect(screen.getByTestId('step-progress-bar')).toHaveTextContent('Steps: 2, Current: 2');
      });

      // Check buttons on Step 3 - there should be a "Zurück" button and a "Weiter" button
      expect(screen.getByRole('button', { name: /Zurück/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Weiter/i })).toBeInTheDocument();
    });
  });

  // --- Variant 'b' Tests ---
  describe('Variant B (window.vcSplitTestVariante = "b")', () => {
    beforeEach(() => {
      Object.defineProperty(window, 'vcSplitTestVariante', {
        value: 'b',
        writable: true,
        configurable: true,
      });
    });

    it('renders Step 1 initially with 3 steps in progress bar', () => {
      render(<FormWizard />);
      expect(screen.getByTestId('availability-check-step')).toBeInTheDocument();
      expect(screen.queryByTestId('living-situation-step')).not.toBeInTheDocument();

      // Check progress bar props (mocked)
      const progressBar = screen.getByTestId('step-progress-bar');
      expect(progressBar).toHaveTextContent('Steps: 3, Current: 1');
      expect(screen.getByTestId('progress-steps')).toHaveTextContent(
        JSON.stringify([
          { id: 1, label: 'Adresse', name: 'address' },
          { id: 2, label: 'Wohnsituation', name: 'livingSituation' },
          { id: 3, label: 'Ergebnis', name: 'result' },
        ]),
      );

      // Check buttons
      expect(screen.queryByRole('button', { name: /Weiter/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /Zurück/i })).not.toBeInTheDocument();
    });

    it('navigates from Step 1 to Step 2 when offerList is populated', async () => {
      const { rerender } = render(<FormWizard />);

      // Wait for initial render
      await waitFor(() => {
        expect(screen.getByTestId('availability-check-step')).toBeInTheDocument();
      });

      // Update store state and trigger re-render
      await act(async () => {
        storeState.offerList = { offers: [{ id: '1' }] } as any;
        storeState.provider = 'DGH'; // Ensure provider is not 'DGP'
        storeState.livingSituation = undefined; // Ensure livingSituation is not set
        setupMockStore();
        rerender(<FormWizard />);
      });

      // Wait for step transition
      await waitFor(() => {
        expect(screen.getByTestId('living-situation-step')).toBeInTheDocument();
        expect(screen.queryByTestId('availability-check-step')).not.toBeInTheDocument();
        expect(screen.getByTestId('step-progress-bar')).toHaveTextContent('Steps: 3, Current: 2');
      });

      // Check buttons on Step 2
      const livingSituationStep = screen.getByTestId('living-situation-step');
      expect(within(livingSituationStep).getByRole('button', { name: /Weiter/i })).toBeInTheDocument();
      expect(within(livingSituationStep).getByRole('button', { name: /Zurück/i })).toBeInTheDocument();
    });
  });
});
