'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';

import { vcSplitTestEventName } from '@/app/consts';

import ActionBar from '@/components/AvailabilityCheckModal/FormWizard/ActionBar';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useUserStore } from '@/stores/UserStore';

import { getAvailabilityInfo } from '@/utils/availabilityCheck/getAvailabilityInfo';

import AvailabilityCheckStep from './AvailabilityCheckStep';
import LivingSituationStep, { personTypeForTracking } from './LivingSituationStep';
import AvailabilityResult from './ResultStep';
import StepProgressBar from './StepProgressBar';

// Define all possible steps
const allSteps = [
  { id: 1, label: '<PERSON><PERSON><PERSON>', name: 'address' },
  { id: 2, label: 'Wohnsituation', name: 'livingSituation' },
  { id: 3, label: 'Ergebnis', name: 'result' },
];

export default function FormWizard() {
  const { trackAcEvent } = useTracking();
  const trackedSteps = useRef<Set<string>>(new Set());

  const [currentStepId, setCurrentStepId] = useState<number>(1);
  const [variant, setVariant] = useState<'a' | 'b'>('a');
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');

  const offerList = useUserStore.use.offerList();
  const resetOfferList = useUserStore.use.resetOfferList();
  const livingSituation = useUserStore.use.livingSituation();
  const setLivingSituation = useUserStore.use.setLivingSituation();
  const provider = useUserStore.use.provider();

  const personTypeSelection = useAvailabilityCheckStore.use.personTypeSelection();
  const setPersonTypeSelection = useAvailabilityCheckStore.use.setPersonTypeSelection();
  const housingTypeSelection = useAvailabilityCheckStore.use.housingTypeSelection();
  const setHousingTypeSelection = useAvailabilityCheckStore.use.setHousingTypeSelection();
  const isAvailabilityModalOpen = useAvailabilityCheckStore.use.isAcModalOpen();

  const trackingModule = 'availability-check-modal';
  const trackingId = `step-${allSteps[currentStepId - 1]?.name}`;

  const wizardSteps = useMemo(() => {
    if (variant !== 'b' || provider === 'DGP') {
      return allSteps.filter((step) => step.id !== 2);
    } else {
      return allSteps;
    }
  }, [variant, provider]);

  // Find the index of the current step ID within the active wizardSteps
  const currentStepIndex = useMemo(() => {
    return wizardSteps.findIndex((step) => step.id === currentStepId);
  }, [currentStepId, wizardSteps]);

  // Determine if the current step (Living Situation) is valid (for button disable)
  const isStepValid = useMemo(() => {
    if (variant === 'a') return true;
    if (personTypeSelection === 'landlord') return true;
    return !!(personTypeSelection && housingTypeSelection);
  }, [personTypeSelection, housingTypeSelection, variant]);

  function handleNext() {
    if (currentStepId === 2) {
      if (!personTypeSelection) {
        console.error('Cannot proceed: Person Type selection is missing.');
        return;
      }

      setLivingSituation({
        personType: personTypeSelection,
        housingType: housingTypeSelection,
      });

      setPersonTypeSelection(null);
      setHousingTypeSelection(null);
    }

    const nextStepIndex = currentStepIndex + 1;
    if (nextStepIndex < wizardSteps.length) {
      setDirection('forward');
      setCurrentStepId(wizardSteps[nextStepIndex].id);
    }
  }

  function handleBack() {
    setDirection('backward');
    void resetOfferList();
  }

  // Effect to listen for trbo split test variant event
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleVariantReady = () => {
      const splitTestVariant = (window as Window).vcSplitTestVariante;
      if (splitTestVariant === 'a' || splitTestVariant === 'b') {
        setVariant(splitTestVariant);
      } else {
        setVariant('a');
      }
    };

    window.addEventListener(vcSplitTestEventName, handleVariantReady, false);

    // Check if variant is already available (in case event fired before we started listening)
    const existingVariant = (window as Window).vcSplitTestVariante;
    if (existingVariant === 'a' || existingVariant === 'b') {
      setVariant(existingVariant);
    }

    // Cleanup
    return () => {
      window.removeEventListener(vcSplitTestEventName, handleVariantReady, false);
    };
  }, []);

  // Effect to handle modal state changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    if (isAvailabilityModalOpen) {
      // When modal opens, check if we can get a variant from trbo
      const splitTestVariant = (window as Window).vcSplitTestVariante;

      if (splitTestVariant === 'a' || splitTestVariant === 'b') {
        setVariant(splitTestVariant);
      } else {
        // Signal to trbo that it's too late for A/B test
        (window as Window).noVcSplitTest = true;
      }
    } else {
      // When modal closes, clear tracked steps
      trackedSteps.current.clear();
    }
  }, [isAvailabilityModalOpen]);

  // Effect to track step events
  useEffect(() => {
    if (!isAvailabilityModalOpen) return;

    const trackingKey = `${currentStepId}-${isAvailabilityModalOpen}`;

    if (trackedSteps.current.has(trackingKey)) return;

    const trackStepEvent = () => {
      switch (currentStepId) {
        case 1:
          if (!offerList) {
            trackAcEvent({
              acInteraction: 'step-address',
            });
          }
          break;
        case 2:
          trackAcEvent({
            acInteraction: 'step-living-situation',
          });
          break;
        case 3:
          trackAcEvent({
            acInteraction: 'step-result',
            livingSituation:
              personTypeForTracking[livingSituation?.personType as keyof typeof personTypeForTracking] || 'na',
            dwellingUnitCluster: livingSituation?.housingType || 'na',
          });
          break;
      }

      trackedSteps.current.add(trackingKey);
    };

    trackStepEvent();
  }, [offerList, currentStepId, isAvailabilityModalOpen, livingSituation, trackAcEvent]);

  // Effect to handle offer list changes
  useEffect(() => {
    const isOfferListReset = !offerList;

    if (isOfferListReset && currentStepId !== 1) {
      trackedSteps.current.clear();
      setDirection('backward');
      setCurrentStepId(1);
    } else if (offerList && currentStepId === 1) {
      const { isAvailable, isIndustrialPark } = getAvailabilityInfo(offerList);
      setDirection('forward');
      if (variant !== 'b' || provider === 'DGP' || isIndustrialPark || !isAvailable) {
        setCurrentStepId(3);
      } else {
        if (livingSituation) {
          setCurrentStepId(3);
        } else {
          setCurrentStepId(2);
        }
      }
    }
  }, [offerList, currentStepId, variant, livingSituation, provider, resetOfferList]);

  // Define animation variants for enter/exit based on direction
  const variants = {
    enter: (direction: 'forward' | 'backward') => ({
      x: direction === 'forward' ? '100%' : '-100%',
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: 'forward' | 'backward') => ({
      zIndex: 0,
      x: direction === 'forward' ? '-100%' : '100%',
      opacity: 0,
    }),
  };

  const renderStepContent = () => {
    switch (currentStepId) {
      case 1:
        return <AvailabilityCheckStep />;
      case 2:
        return variant === 'b' && provider !== 'DGP' ? <LivingSituationStep /> : null;
      case 3:
        return <AvailabilityResult />;
      default:
        return null;
    }
  };

  return (
    <div
      data-tracking-module={trackingModule}
      data-tracking-id={trackingId}
      className="flex h-[calc(90vh-48px)] min-h-[400px] flex-col text-basalt xl:h-[calc(90vh-80px)] xl:max-h-[640px] xl:w-[990px]"
    >
      <div className="flex-shrink-0">
        <StepProgressBar steps={wizardSteps} currentStep={currentStepIndex + 1} />
      </div>

      <div className="relative min-h-0 flex-1 overflow-hidden">
        <div className="pointer-events-none absolute top-0 left-0 z-10 h-8 w-full bg-gradient-to-b from-white to-transparent" />
        <AnimatePresence initial={false} mode="popLayout" custom={direction}>
          <motion.div
            key={currentStepId}
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: 'tween', ease: 'easeOut', duration: 0.3 },
              opacity: { duration: 0.2 },
            }}
            className="h-full overflow-y-auto px-4 py-4 xl:px-12 xl:py-8 xl:pb-8"
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>
        <div className="pointer-events-none absolute bottom-0 left-0 z-10 h-8 w-full bg-gradient-to-t from-white to-transparent" />
      </div>

      <div className="flex-shrink-0">
        <ActionBar
          variant={variant}
          currentStepIndex={currentStepIndex}
          isStepValid={isStepValid}
          onBack={handleBack}
          onNext={handleNext}
        />
      </div>
    </div>
  );
}
