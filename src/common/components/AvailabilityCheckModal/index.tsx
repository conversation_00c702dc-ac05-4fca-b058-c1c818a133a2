'use client';

import React from 'react';

import Modal from '@/components/Modal';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';

import FormWizard from './FormWizard';

export default function AvailabilityCheckModal() {
  const isAvailabilityModalOpen = useAvailabilityCheckStore.use.isAcModalOpen();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();

  function handleClose() {
    setAcModalOpen(false);
  }

  return (
    <Modal
      id="availability-check-modal"
      hasCloseBtn={true}
      isOpen={isAvailabilityModalOpen}
      onClose={handleClose}
      className="bg-white"
      closeOnOutsideClick={false}
    >
      <FormWizard />
    </Modal>
  );
}
