import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import <PERSON><PERSON><PERSON> from './index';

const meta: Meta<typeof BrandLogo> = {
  title: 'UI/BrandLogo',
  component: BrandLogo,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof BrandLogo>;

export const Default: Story = {
  args: {
    href: '/',
  },
  parameters: {
    nextjs: {
      appDirectory: true,
      // Avoid error onClick since useTracking is implemented in the component
      navigation: {
        pathname: '/',
      },
    },
  },
};
