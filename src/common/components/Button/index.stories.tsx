import type { <PERSON>a, StoryObj } from '@storybook/react';

import { PlusSm as PlusIcon } from '@/components/Icons/sm';

import Button from '.';

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  tags: ['autodocs'],

  argTypes: {
    variant: {
      control: {
        intent: 'select',
        options: ['primary', 'secondary', 'tertiary'],
      },
    },
    onClick: { action: 'clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <div className={'flex flex-col gap-4 md:flex-row'}>{children}</div>
);

const BasaltWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className={'theme-basalt group flex flex-col gap-4 bg-basalt p-4 md:flex-row'}>{children}</div>
);

export const Primary: Story = {
  render: () => (
    <Wrapper>
      <Button
        variant="primary"
        label="Primary Button"
        onClick={() => {
          console.log('clicked');
        }}
      />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" labelHidden onClick={() => {}} />
    </Wrapper>
  ),
};

export const PrimaryOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button
        variant="primary"
        label="Primary Button"
        onClick={() => {
          console.log('clicked');
        }}
      />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" labelHidden onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const PrimaryDisabledOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button
        variant="primary"
        label="Primary Button"
        disabled
        onClick={() => {
          console.log('clicked');
        }}
      />
      <Button icon={<PlusIcon />} variant="primary" disabled label="Primary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="primary" disabled label="Primary Button" labelHidden onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const Secondary: Story = {
  render: () => (
    <div className={'flex flex-col gap-4 md:flex-row'}>
      <Button variant="secondary" label="Secondary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="secondary" label="Secondary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="secondary" label="Secondary Button" labelHidden onClick={() => {}} />
    </div>
  ),
};

export const SecondaryOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button
        variant="secondary"
        label="Secondary Button"
        onClick={() => {
          console.log('clicked');
        }}
      />
      <Button icon={<PlusIcon />} variant="secondary" label="Secondary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="secondary" label="Secondary Button" labelHidden onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const SecondaryDisabledOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button
        variant="secondary"
        label="Secondary Button"
        disabled
        onClick={() => {
          console.log('clicked');
        }}
      />
      <Button icon={<PlusIcon />} variant="secondary" disabled label="Secondary Button" onClick={() => {}} />
      <Button
        icon={<PlusIcon />}
        variant="secondary"
        disabled
        label="Secondary Button"
        labelHidden
        onClick={() => {}}
      />
    </BasaltWrapper>
  ),
};

export const Tertiary: Story = {
  render: () => (
    <Wrapper>
      <Button variant="tertiary" label="Tertiary Button" onClick={() => {}} />
    </Wrapper>
  ),
};

export const TertiaryOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button variant="tertiary" label="Tertiary Button" onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const TertiaryDisabledOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button variant="tertiary" label="Tertiary Button" disabled onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const Highlight: Story = {
  render: () => (
    <Wrapper>
      <Button variant="highlight" label="Highlight Button" onClick={() => {}} />
    </Wrapper>
  ),
};

export const HighlightOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button variant="highlight" label="Highlight Button" onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const HighlightDisabledOnBasalt: Story = {
  render: () => (
    <BasaltWrapper>
      <Button variant="highlight" label="Highlight Button" disabled onClick={() => {}} />
    </BasaltWrapper>
  ),
};

export const Truncated: Story = {
  render: () => (
    <Wrapper>
      <Button
        variant="tertiary"
        className="w-[200px]!"
        label="Tertiary Button truncated because it is too long"
        truncateLabel
        onClick={() => {}}
      />
      <Button icon={<PlusIcon />} variant="tertiary" label="Tertiary Button" onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="tertiary" label="Primary Button" labelHidden onClick={() => {}} />
    </Wrapper>
  ),
};

export const Disabled: Story = {
  render: () => (
    <Wrapper>
      <Button variant="primary" label="Primary Button" disabled onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" disabled onClick={() => {}} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" labelHidden disabled onClick={() => {}} />
    </Wrapper>
  ),
};

export const Link: Story = {
  render: () => (
    <Wrapper>
      <Button variant="primary" label="Primary Button" href={'/'} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" href={'/'} />
      <Button icon={<PlusIcon />} variant="primary" label="Primary Button" labelHidden href={'/'} />
    </Wrapper>
  ),
};
