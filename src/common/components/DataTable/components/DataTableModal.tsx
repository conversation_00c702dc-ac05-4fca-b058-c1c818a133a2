import Headline from '@/components/Headline';
import Modal from '@/components/Modal';

import type { PopupInfoProps } from '../types';

type DataTableModalProps = {
  popupInfo?: PopupInfoProps;
  isOpen: boolean;
  onClose: () => void;
};

export function DataTableModal({ popupInfo, isOpen, onClose }: DataTableModalProps) {
  if (!popupInfo) return null;
  const { headline, description } = popupInfo;
  if (!headline && !description) return null;

  return (
    <Modal
      id="data-table-modal"
      hasCloseBtn
      isOpen={isOpen}
      onClose={onClose}
      className="space-y-4 px-4 py-8 lg:px-[120px] lg:py-20"
    >
      {headline && (
        <Headline intent="h2" className="text-center">
          {headline}
        </Headline>
      )}
      {description && <div dangerouslySetInnerHTML={{ __html: description }} />}
    </Modal>
  );
}
