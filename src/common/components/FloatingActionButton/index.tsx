import React from 'react';

import { BusinessArea } from '@/app/consts';
import type { BusinessArea as BusinessAreaType } from '@/app/consts';

import { isOutsideOfWorkingHours } from '@/hooks/isOutsideOfWorkingHours';

import { useHotlineListStore } from '@/stores/HotlineListStore';

import ScrollToTop from '../ScrollToTop';
import StickyCallButton from '../StickyCallButton';

type FloatingActionButtonProps = {
  businessArea?: BusinessAreaType;
  pageType?: string;
  hasScrollToTop?: boolean;
  hasCallButton?: boolean;
};

export default function FloatingActionButton({
  businessArea,
  pageType,
  hasScrollToTop,
  hasCallButton,
}: FloatingActionButtonProps) {
  if (!hasCallButton && !hasScrollToTop) return null;

  if (!hasCallButton && hasScrollToTop) return <ScrollToTop />;

  const workingHours =
    businessArea === BusinessArea.Professional
      ? { workdays: '08:00-18:00', saturday: null, sundayBankHolidays: null }
      : useHotlineListStore.use.hotlineList()?.workingHours;

  // Don't render if not Professional area and outside working hours
  if (isOutsideOfWorkingHours(workingHours)) {
    return <ScrollToTop />;
  }

  return <StickyCallButton businessArea={businessArea} pageType={pageType} />;
}
