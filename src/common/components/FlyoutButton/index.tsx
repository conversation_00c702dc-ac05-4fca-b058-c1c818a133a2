'use client';

import Button from '@/components/Button';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useUserStore } from '@/stores/UserStore';

import type { CTA } from '@/types/cta';

import { getAvailabilityInfo } from '@/utils/availabilityCheck/getAvailabilityInfo';

type FlyoutButtonProps = CTA;

// TODO: Name is misleading, should be renamed to AvailabilityCheckButton or something similar
function FlyoutButton({ label, labelAfterAcPositive, labelAfterAcNegative, variant = 'primary' }: FlyoutButtonProps) {
  const { trackAcEvent } = useTracking();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const setAcType = useAvailabilityCheckStore.use.setAcType();
  const offerList = useUserStore.use.offerList();

  let computedLabel = label;
  if (offerList) {
    computedLabel = getAvailabilityInfo(offerList).isAvailable
      ? (labelAfterAcPositive ?? label)
      : (labelAfterAcNegative ?? label);
  }

  function onButtonClick() {
    setAcModalOpen(true);
    setAcType('button');
    trackAcEvent({ acType: 'button', acInteraction: 'open' });
  }

  return <Button label={computedLabel} variant={variant} onClick={onButtonClick} />;
}

export default FlyoutButton;
