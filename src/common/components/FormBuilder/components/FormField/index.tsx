import React, { useState } from 'react';

import type { FieldError } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';

import Checkbox from '@/components/Checkbox';
import FileUpload from '@/components/FileUpload';
import FormGroup from '@/components/FormGroup';
import NumberInput from '@/components/NumberInput';
import RadioButton from '@/components/RadioButton';
import Select from '@/components/Select';
import TextArea from '@/components/TextArea';
import TextInput from '@/components/TextInput';

import useTracking from '@/hooks/useTracking';

import type { FormField as FormFieldElement, FormFieldWidth, InputType } from '@/types/forms';
import type { TrackingAction } from '@/types/tracking';

import { cn } from '@/utils/cn';
import { getMaxLength, validationConfig } from '@/utils/forms/formValidation';
import { stripHtml } from '@/utils/stripHtml';
import { truncateString } from '@/utils/truncateString';

import { formFieldStyles } from './index.styles';

type EventElement = HTMLInputElement | HTMLDivElement | HTMLTextAreaElement | HTMLButtonElement | HTMLSelectElement;

type FormFieldProps = FormFieldElement & {
  error?: FieldError;
};

export default function FormField({
  fieldName,
  label,
  placeholder,
  defaultValue,
  disabled,
  required,
  validationRules,
  validation,
  inputType,
  isMultipleSelect,
  options,
  width,
  readonly,
  tooltip,
  error,
}: FormFieldProps) {
  const { register, watch } = useFormContext();
  const { trackEvent } = useTracking();
  const [editing, setEditing] = useState(false);
  const [isActive, setIsActive] = useState(false);

  const value = watch(fieldName);

  const inputProps = {
    id: fieldName,
    label,
    placeholder,
    defaultValue,
    disabled,
    invalid: !!error,
    hotjarSuppress: true,
    tooltip,
    readonly,
  };

  const validationRulesConfig = {
    required: required ? 'Das ist ein Pflichtfeld' : false,
    ...validationRules,
    ...(validationRules?.pattern && {
      pattern: {
        value: new RegExp(validationRules.pattern.value.toString().replace(/^\/|\/$/g, '')),
        message: validationRules.pattern.message,
      },
    }),
    // TODO: Remove this once the old forms have been migrated to the new forms
    ...(validation && {
      ...validationConfig[validation],
    }),
  };

  function isValidInput(type: InputType): type is keyof typeof InputMap {
    return [
      'text',
      'textarea',
      'select',
      'number',
      'checkbox',
      'radio',
      'file',
      'date',
      'email',
      'tel',
      'hidden',
    ].includes(type);
  }

  function handleEvent<E extends React.SyntheticEvent<EventElement>>(
    event: E,
    action: TrackingAction,
    contentModifier = '',
  ) {
    const content = truncateString(stripHtml(label), 54);
    const isTextInput = event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement;

    switch (action) {
      case 'view':
        if (isActive) return;
        setIsActive(isTextInput);
        trackEvent({
          category: 'Form',
          action,
          element: event.currentTarget,
          content: `${content}${contentModifier}`,
        });
        break;
      case 'edit':
        if (editing) return;
        setEditing(isTextInput);
        trackEvent({
          category: 'Form',
          action,
          element: event.currentTarget,
          content: `${content}${contentModifier}`,
        });
        break;
      default:
        trackEvent({
          category: 'Form',
          action,
          element: event.currentTarget,
          content: `${content}${contentModifier}`,
        });
        break;
    }
  }

  function handleBlur() {
    setIsActive(false);
    setEditing(false);
  }

  const getCommonTextInputProps = (type: string) => ({
    type,
    required: !!validationRulesConfig.required,
    ...inputProps,
    ...register(fieldName, {
      ...validationRulesConfig,
      setValueAs: (value) => (typeof value === 'string' ? value.trim() : value),
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleEvent(e, 'edit'),
      onBlur: handleBlur,
      disabled,
    }),
    onKeyUp: (e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Tab' && handleEvent(e, 'view'),
    onClick: (e: React.MouseEvent<HTMLInputElement>) => handleEvent(e, 'view'),
    invalidText: error?.message?.toString(),
  });

  const getCommonRegisterProps = (
    onChangeHandler: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void,
  ) => ({
    ...validationRulesConfig,
    onChange: onChangeHandler,
    onBlur: handleBlur,
    disabled,
  });

  const getCheckboxRegisterProps = () => ({
    ...validationRulesConfig,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleEvent(e, e.target.checked ? 'check' : 'uncheck'),
    onBlur: handleBlur,
    disabled,
  });

  const InputMap = {
    text: () => <TextInput {...getCommonTextInputProps('text')} />,
    email: () => <TextInput {...getCommonTextInputProps('email')} />,
    tel: () => <TextInput {...getCommonTextInputProps('tel')} />,
    date: () => <TextInput {...getCommonTextInputProps('date')} />,
    select: () => (
      <Select
        {...inputProps}
        {...register(fieldName, {
          ...validationRulesConfig,
          onChange: (e) => handleEvent(e, 'select'),
          disabled,
        })}
        value={value}
        multiple={isMultipleSelect}
        options={options ?? []}
        onKeyUp={(e) => e.key === 'Tab' && handleEvent(e, 'view')}
        onClick={(e) => handleEvent(e, 'view')}
        required={!!validationRulesConfig.required}
        invalidText={error?.message?.toString()}
      />
    ),
    number: () => (
      <NumberInput
        {...inputProps}
        {...register(
          fieldName,
          getCommonRegisterProps((e) => {
            const currentValue = String(value || '');
            return e.target.value.length > currentValue.length && handleEvent(e, 'edit');
          }),
        )}
        min={typeof validationRulesConfig.min === 'number' ? validationRulesConfig.min : undefined}
        max={typeof validationRulesConfig.max === 'number' ? validationRulesConfig.max : undefined}
        onKeyUp={(e) => e.key === 'Tab' && handleEvent(e, 'view')}
        onClick={(e) => handleEvent(e, 'view')}
        required={!!validationRulesConfig.required}
        invalidText={error?.message?.toString()}
      />
    ),
    textarea: () => (
      <TextArea
        {...inputProps}
        {...register(
          fieldName,
          getCommonRegisterProps((e) => handleEvent(e, 'edit')),
        )}
        maxCount={getMaxLength(validationRulesConfig.maxLength || 3000)}
        required={!!validationRulesConfig.required}
        onKeyUp={(e) => e.key === 'Tab' && handleEvent(e, 'view')}
        onClick={(e) => handleEvent(e, 'view')}
        invalidText={error?.message?.toString()}
      />
    ),
    file: () => (
      <FileUpload
        {...inputProps}
        {...register(fieldName, {
          ...validationRulesConfig,
          disabled,
        })}
        value={value}
        acceptedFileTypes={['JPG', 'PNG', 'PDF', 'DOC', 'DOCX']}
        required={!!validationRulesConfig.required}
        onClick={(e) => handleEvent(e, 'click', '-add')}
        onAdd={(e) => handleEvent(e, 'click', '-edit')}
        onRemove={(e) => handleEvent(e, 'click', '-remove')}
        invalidText={error?.message?.toString()}
      />
    ),
    checkbox: () => (
      <Checkbox
        {...inputProps}
        {...register(fieldName, getCheckboxRegisterProps())}
        invalidText={error?.message?.toString()}
      />
    ),
    radio: () => (
      <FormGroup
        label={inputProps.label}
        direction="row"
        required={!!validationRulesConfig.required}
        isRadioGroup={true}
        invalidText={error?.message?.toString()}
        id={`radio-group-${fieldName}`}
        disabled={disabled}
      >
        {(options ?? []).map((option) => (
          <RadioButton
            key={option.value}
            {...inputProps}
            {...register(fieldName, getCheckboxRegisterProps())}
            id={`${fieldName}-${option.value}`}
            label={option.label}
            value={option.value}
          />
        ))}
      </FormGroup>
    ),
    hidden: () => <input type="hidden" {...register(fieldName)} defaultValue={inputProps.defaultValue} />,
  };

  return (
    <div
      className={cn(
        formFieldStyles({
          width: (width as FormFieldWidth) ?? 'full',
        }),
      )}
    >
      {isValidInput(inputType) ? InputMap[inputType]() : null}
    </div>
  );
}
