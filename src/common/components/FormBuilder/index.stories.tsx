import type { <PERSON>a, StoryObj } from '@storybook/react';

// TODO: Need to mock nodemailer to avois errors "Can't resolve 'dns'", Can't resolve 'tls', Can't resolve 'net'?
// import Form from './Form';
// import { mockedFormData } from '@/mock/mockedFormData';
// import FormBuilder from './index';
// // TODO: Need to mock nodemailer to avois errors "Can't resolve 'dns'", Can't resolve 'tls', Can't resolve 'net'?
// import Form from './index';
// const meta: Meta<typeof FormBuilder> = {
//   title: 'UI/Form/FormBuilder',
//   component: FormBuilder,
//   tags: ['autodocs'],
//   parameters: {
//     nextjs: {
//       appDirectory: true,
//     },
//     navigation: {
//       pathname: '/',
//       query: {
//         telljaid: '12345678',
//       },
//     },
//   },
// };
// export default meta;
// type Story = StoryObj<typeof FormBuilder>;
// export const Default: Story = {
//   render: (args) => (
//     <div className={'lg:m-auto lg:w-1/2'}>
//       <FormBuilder {...args} />
//     </div>
//   ),
//   args: {
//     formElements,
//     formType: 'PKOwnerDataAviDat',
//     defaultValues: undefined,
//     recipient: { id: '1234' },
//     subject: 'Test Form',
//     onSuccess: () => {
//       console.log('success');
//     },
//   },
// };
// TODO: Remove when Form story above is fixed
import TextInput from '@/components/TextInput';

const meta: Meta<typeof TextInput> = {
  title: 'UI/Form',
  component: TextInput,
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof TextInput>;

export const PlaceholderTextInput: Story = {
  args: {
    id: 'input',
    label: 'Label',
    placeholder: 'Placeholder Text',
  },
};
