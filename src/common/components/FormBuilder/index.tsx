'use client';

import { useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { BusinessArea } from '@/app/consts';

import FormSuccess from '@/components/FormBuilder/components/FormSuccess';
import Modal from '@/components/Modal';
import ReCaptcha from '@/components/ReCaptcha';

import { useUserStore } from '@/stores/UserStore';

import type { Form, FormData, FormRecipient, FormType } from '@/types/forms';

import FormElements from './components/FormElements';
import FormErrorMessage from './components/FormErrorMessage';
import { RequiredFieldsNote } from './components/RequiredFieldsNots';
import { useFormInitialization } from './hooks/useFormInitialization';
import { useFormSubmission } from './hooks/useFormSubmission';

export type FormBuilderProps = Form & {
  defaultValues?: FormData;
  onSuccess?: () => void;
};

export default function FormBuilder({
  formType,
  formElements,
  defaultValues,
  recipient,
  subject,
  onSuccess,
}: FormBuilderProps) {
  const acAddress = useUserStore.use.acAddress();
  const provider = useUserStore.use.provider() || BusinessArea.Private;

  const formMethods = useForm({
    defaultValues: useMemo(() => {
      return defaultValues;
    }, [defaultValues]),
  });

  const {
    handleSubmit,
    reset,
    setValue,
    formState: { isSubmitting },
  } = formMethods;

  const {
    onSubmit,
    success,
    submitErrorMessage,
    setSubmitErrorMessage,
    recaptchaLoaded,
    asyncScriptOnLoad,
    reCaptchaRef,
    formRef,
    closeModal,
    handleNewFormClick,
  } = useFormSubmission({
    formType: formType as FormType,
    formElements,
    recipient: recipient as FormRecipient,
    subject,
    onSuccess,
    reset,
  });

  const { telljaId, odpParams, orderId } = useFormInitialization({
    formType: formType as FormType,
    formElements,
    acAddress: acAddress || null,
    setValue,
    reset,
  });

  const handleFormSubmit = (data: FormData) => {
    return onSubmit(data, telljaId, odpParams, orderId, provider);
  };

  return (
    <form ref={formRef} className={'space-y-4'} onSubmit={handleSubmit(handleFormSubmit)}>
      <FormErrorMessage submitErrorMessage={submitErrorMessage} setSubmitErrorMessage={setSubmitErrorMessage} />
      <FormProvider {...formMethods}>
        <RequiredFieldsNote />
        <FormElements formElements={formElements} isSubmitting={isSubmitting} recaptchaLoaded={recaptchaLoaded} />
      </FormProvider>

      <ReCaptcha ref={reCaptchaRef} asyncScriptOnLoad={asyncScriptOnLoad} />
      <Modal id="form-success-modal" isOpen={success} onClose={closeModal}>
        <FormSuccess onNewFormClick={handleNewFormClick} />
      </Modal>
    </form>
  );
}
