import React from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import FormLabel from './index';

const meta: Meta<typeof FormLabel> = {
  title: 'UI/Form/FormLabel',
  component: FormLabel,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof FormLabel>;

export const Default: Story = {
  args: {
    children: 'Label',
    htmlFor: 'test',
  },
};

export const WithJSXChildren: Story = {
  args: {
    children: <span>Span Label</span>,
    htmlFor: 'test',
  },
};

export const Disabled: Story = {
  args: {
    children: 'Test disabled',
    disabled: true,
  },
};

export const Hidden: Story = {
  args: {
    children: 'Test hidden',
    hidden: true,
  },
};

export const Required: Story = {
  args: {
    children: 'Test required',
    required: true,
  },
};
