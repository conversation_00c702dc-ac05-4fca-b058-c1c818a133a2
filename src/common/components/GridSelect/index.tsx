import type { KeyboardEvent } from 'react';
import React, { useEffect, useRef, useState } from 'react';

import { cn } from '@/utils/cn';

import GridSelectOption, { type GridSelectOptionData } from './GridSelectOption';

type GridSelectProps = {
  id: string;
  options: GridSelectOptionData[];
  value: string | null;
  onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  label: string;
  className?: string;
};

export default function GridSelect({ id, options, value, onChange, label, className }: GridSelectProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const groupRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const selectRef = useRef<HTMLSelectElement>(null);
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);

  useEffect(() => {
    optionRefs.current = optionRefs.current.slice(0, options.length);
  }, [options.length]);

  useEffect(() => {
    const currentValIndex = options.findIndex((o) => o.value === value);
    if (currentValIndex !== -1) {
      setFocusedIndex(currentValIndex);
    } else {
      setFocusedIndex(-1);
    }
  }, [value, options]);

  useEffect(() => {
    if (focusedIndex !== -1 && optionRefs.current[focusedIndex]) {
      const timer = setTimeout(() => {
        optionRefs.current[focusedIndex]?.focus();
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [focusedIndex]);

  function handleOptionSelect(optionValue: string) {
    if (selectRef.current && value !== optionValue) {
      selectRef.current.value = optionValue;
      // Dispatch the change event on the real select element (same as Select component)
      selectRef.current.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  function handleGroupKeyDown(e: KeyboardEvent<HTMLDivElement>) {
    if (!options || options.length === 0) return;

    const totalOptions = options.length;
    let currentFocusedIndex = focusedIndex;

    if (currentFocusedIndex === -1) {
      if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
        currentFocusedIndex = 0;
      } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
        currentFocusedIndex = totalOptions - 1;
      } else {
        return;
      }
      setFocusedIndex(currentFocusedIndex);
      e.preventDefault();
      return;
    }

    let nextIndex = currentFocusedIndex;

    switch (e.key) {
      case 'ArrowRight':
        e.preventDefault();
        nextIndex = currentFocusedIndex + 1;
        if (nextIndex >= totalOptions) {
          nextIndex = 0; // Wrap to beginning
        }
        break;
      case 'ArrowLeft':
        e.preventDefault();
        nextIndex = currentFocusedIndex - 1;
        if (nextIndex < 0) {
          nextIndex = totalOptions - 1; // Wrap to end
        }
        break;
      case 'ArrowDown':
        e.preventDefault();
        nextIndex = currentFocusedIndex + 1;
        if (nextIndex >= totalOptions) {
          nextIndex = 0; // Wrap to beginning
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        nextIndex = currentFocusedIndex - 1;
        if (nextIndex < 0) {
          nextIndex = totalOptions - 1; // Wrap to end
        }
        break;
      default:
        return;
    }

    if (nextIndex !== currentFocusedIndex && nextIndex >= 0 && nextIndex < totalOptions) {
      setFocusedIndex(nextIndex);
    }
  }

  return (
    <div ref={containerRef} className={cn('relative space-y-4', className)}>
      {/* Hidden select for event compatibility */}
      <select
        ref={selectRef}
        value={value || ''}
        onChange={onChange}
        style={{ display: 'none' }}
        tabIndex={-1}
        aria-hidden="true"
      >
        <option value="" aria-label="Bitte wählen" />
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <div id={`${id}-label`} className="text-lg font-semibold text-basalt">
        {label}
      </div>

      <div
        ref={groupRef}
        id={`${id}-group`}
        role="radiogroup"
        aria-labelledby={`${id}-label`}
        onKeyDown={handleGroupKeyDown}
        tabIndex={-1}
        className={'flex flex-wrap gap-4'}
      >
        {options.map((option, index) => {
          let isTabbable;
          if (focusedIndex === -1) {
            isTabbable = index === 0;
          } else {
            isTabbable = index === focusedIndex;
          }
          return (
            <GridSelectOption
              key={option.value}
              ref={(el) => {
                optionRefs.current[index] = el;
              }}
              option={option}
              isSelected={option.value === value}
              onClick={() => handleOptionSelect(option.value)}
              tabIndex={isTabbable ? 0 : -1}
            />
          );
        })}
      </div>
    </div>
  );
}
