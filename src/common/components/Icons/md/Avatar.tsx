import * as React from 'react';
import type { SVGProps } from 'react';

const SvgAvatar = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={50} height={51} fill="none" viewBox="0 0 50 51" {...props}>
    <mask
      id="a"
      width={51}
      height={51}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'luminance',
      }}
    >
      <path fill="#fff" d="M25.004 50.5c13.807 0 25-11.193 25-25s-11.193-25-25-25-25 11.193-25 25 11.193 25 25 25" />
    </mask>
    <g mask="url(#a)">
      <path
        fill="#14A0DC"
        d="M24.938 56.769c17.036 0 30.846-13.81 30.846-30.847S41.974-4.924 24.938-4.924-5.908 8.886-5.908 25.922 7.902 56.77 24.938 56.77"
      />
      <path
        fill="#9D9D9B"
        d="M7.006 89.248c.014.11.06.21.128.289H1.242l2.786-42.534 7.158 18.392-1.932 28.226m33.972-2.097c-.014.11.069-.078 0 0l5.3-.165-2.18-46.012-7.158 18.392 1.931 28.226"
      />
      <path fill="#7A7A79" d="m11.162 65.395-.16 3.432-6.97-21.824z" />
      <path
        fill="#AFAFAC"
        d="M32.378 37.417v.055c.106 4.272-3.042 7.833-7.048 7.957-4.02.12-7.393-3.267-7.521-7.567l1.569-1.147c.482-.349.757-.936.734-1.56l-.128-3.29h-.005l-.069-1.822c.152.16.317.312.482.445a1 1 0 0 0 .069.06c.046.045.096.082.142.123a.1.1 0 0 0 .041.028c.019.018.028.023.046.036a1 1 0 0 0 .152.11c.087.065.174.13.266.184.06.041.11.078.17.11q.214.126.436.244.128.069.257.123.118.063.243.11c.023.005.046.019.069.024.151.064.312.123.472.17.11.036.225.068.335.1a.5.5 0 0 0 .12.028c.023.004.055.014.082.018h.005c.055.014.105.023.16.037.046.014.101.018.147.023a4 4 0 0 0 .317.055c.289.037.578.055.858.055.046 0 .096 0 .142-.005.046.005.097 0 .147-.004q.091 0 .188-.014h.019a.2.2 0 0 0 .096-.005c.165-.013.317-.027.477-.06h.014q.178-.027.349-.068h.004s.023 0 .028-.005c.018-.004.037-.004.055-.013a.4.4 0 0 0 .128-.037c.042-.005.083-.018.129-.028h.014l.105-.036c.014-.005.019-.005.028-.014.013 0 .018 0 .027-.005l.023-.004q.032-.008.065-.023c.004 0 .013 0 .018-.005.004 0 .018 0 .023-.004.014-.005.023-.005.037-.014.04-.014.082-.023.119-.041q.027-.008.055-.023c.014-.005.023-.005.037-.014.04-.014.082-.023.119-.041.027-.014.06-.023.087-.037q.08-.028.16-.069c.028-.014.06-.023.088-.037.005-.004.018-.004.023-.014.082-.036.16-.078.243-.119.005 0 .005-.004.014-.004a.1.1 0 0 0 .027-.019h.005c.069-.037.133-.069.202-.11.036-.018.064-.041.1-.06a.1.1 0 0 0 .047-.027c.023-.014.046-.023.069-.041.004-.005.013-.005.018-.014.037-.018.064-.041.1-.064.047-.023.088-.055.13-.083l.013-.014c.023-.014.046-.023.069-.046a1 1 0 0 0 .078-.055.3.3 0 0 1 .069-.046c.078-.055.147-.11.215-.174.019-.005.037-.023.056-.041a.03.03 0 0 0 .022-.019.5.5 0 0 0 .078-.064l.014-.014a.3.3 0 0 0 .06-.046q.033-.027.06-.055l.036-.036c.014-.005.023-.019.041-.037.014-.005.019-.018.028-.028.037-.023.064-.055.101-.087.027-.023.055-.046.064-.069.014-.004.019-.013.019-.018.078-.064.142-.133.201-.206.005-.005.014-.005.014-.014.014-.005.019-.019.023-.023h.005s.004-.014.014-.018h.004v.036l.014.455.096 4.506c.014.624.326 1.193.826 1.519l1.643 1.046z"
      />
      <path
        fill="#7A7A79"
        d="M29.8 29.855c-1.455 1.61-3.603 2.505-5.875 2.211a7 7 0 0 1-.628-.114c.344.766.94 1.656 1.968 2.244 2.148 1.234 4.644.302 4.644.302l-.1-4.639h-.01z"
      />
      <path
        fill="#4C5054"
        d="M23.547 20.016a3.17 3.17 0 0 1-3.166 3.166 3.17 3.17 0 0 1-3.166-3.166 3.167 3.167 0 0 1 6.333 0m8.566-.133a3.17 3.17 0 0 1-3.166 3.166 3.17 3.17 0 0 1-3.167-3.166 3.174 3.174 0 0 1 3.167-3.171 3.17 3.17 0 0 1 3.166 3.17"
      />
      <path
        fill="#F4F4F0"
        d="M16.11 20.677a.25.25 0 0 1-.248-.142c-.473-1.06-2.891-7.273 2.895-12.459 3.346-2.996 8.315-1.868 9.339-5.149.055-.179.289-.234.413-.091 1.523 1.707 8.035 9.567 5.015 16.17a.26.26 0 0 1-.21.148l-17.209 1.523z"
      />
      <path
        fill="#C0C0BD"
        d="M23.547 20.017a3.17 3.17 0 0 1-3.166 3.166 3.17 3.17 0 0 1-3.166-3.166 3.167 3.167 0 0 1 6.333 0m8.566-.133a3.17 3.17 0 0 1-3.166 3.166 3.17 3.17 0 0 1-3.167-3.166 3.174 3.174 0 0 1 3.167-3.171 3.17 3.17 0 0 1 3.166 3.17"
      />
      <path
        fill="#C0C0BD"
        d="M24.667 9.683c-.94.05-3.056.293-5.135 1.766-2.987 2.116-3.749 5.245-3.919 6.03q-.019.254-.018.514c-.014.496.018.977.087 1.455l.096.578c.042.225.097.445.152.66v.014q.184.669.459 1.29l1.018 2.895.744 2.107c.082.289.183.564.294.835v.014a7.4 7.4 0 0 0 1.463 2.207l.482.445q.106.095.211.184.044.035.088.064c.045.041.1.078.151.11q.13.098.266.184c.06.04.11.078.17.11q.214.125.436.243.129.069.257.124.118.062.243.11c.023.004.046.018.069.023.151.064.312.124.473.17.11.036.224.068.334.1a1 1 0 0 0 .202.042l.005.004c.055.014.105.023.16.037.047.014.102.018.148.023q.157.034.316.055.436.055.858.055c.16 0 .317-.004.477-.023a.3.3 0 0 0 .11-.004c.166-.014.317-.028.478-.06h.014q.178-.027.348-.069c.028 0 .06-.014.087-.018a.4.4 0 0 0 .129-.037c.096-.018.184-.046.275-.078a.1.1 0 0 0 .055-.014c.046-.013.097-.027.143-.046a1 1 0 0 0 .17-.064c.082-.023.16-.055.243-.087.055-.018.105-.041.16-.069a3 3 0 0 0 .354-.165c.004 0 .004-.005.013-.005a.1.1 0 0 0 .028-.018h.005q.159-.081.298-.17a.1.1 0 0 0 .046-.027c.023-.014.046-.023.069-.042.06-.036.119-.068.174-.119.078-.041.151-.096.23-.151a3 3 0 0 0 .288-.225c.019-.005.037-.023.055-.041a.03.03 0 0 0 .023-.019.5.5 0 0 0 .078-.064 1 1 0 0 0 .166-.147c.018-.005.027-.023.046-.046a1 1 0 0 0 .123-.105.2.2 0 0 0 .065-.07c.013-.004.018-.013.018-.017.078-.065.142-.134.202-.207a.2.2 0 0 0 .046-.055 7.1 7.1 0 0 0 1.431-2.39v-.02q.166-.458.271-.944l.601-2.143.725-2.58c.257-.647.455-1.33.57-2.042q.042-.29.078-.587a10 10 0 0 0-.042-2.285 9.86 9.86 0 0 0-3.345-5.09 9.85 9.85 0 0 0-5.41-2.096zm4.281 13.954A3.76 3.76 0 0 1 25.2 19.71l-1.078.037c.005.087.014.184.014.275a3.76 3.76 0 0 1-3.754 3.754 3.76 3.76 0 0 1-3.754-3.754 3.76 3.76 0 0 1 3.754-3.753 3.746 3.746 0 0 1 3.648 2.9l1.244-.046a3.76 3.76 0 0 1 3.675-2.997 3.76 3.76 0 0 1 3.754 3.759 3.76 3.76 0 0 1-3.753 3.753"
      />
      <path
        fill="#464646"
        d="M20.664 20.359a.7.7 0 0 0 .68-.714.697.697 0 0 0-.722-.671.697.697 0 0 0-.68.714.7.7 0 0 0 .722.67m7.809-.238a.7.7 0 0 0 .68-.713.7.7 0 0 0-.723-.672.697.697 0 0 0-.68.714.7.7 0 0 0 .723.672"
      />
      <path
        fill="#7A7A79"
        d="M16.39 21.985a9 9 0 0 1-.454-1.29v-.008a9 9 0 0 1-.261-1.34 3.16 3.16 0 0 0-1.084 2.496c.051 1.629 1.29 2.928 2.823 3.033l-1.019-2.89zm17.074-2.84a9 9 0 0 1-.073.596 9.7 9.7 0 0 1-.569 2.042l-.72 2.575c1.326-.363 2.28-1.634 2.234-3.116a3.17 3.17 0 0 0-.867-2.097zm12.859 26.858-6.245 27.41-.666-4.75z"
      />
      <path
        fill="#EBB411"
        d="m10.382 89.546 4.75-13.032L4.045 47.012c.078-2.056 1.372-3.891 3.331-4.75l10.45-4.395c.128 4.3 3.5 7.686 7.52 7.567 4.006-.124 7.154-3.685 7.049-7.957l.133-.06 10.274 3.758c2.015.735 3.414 2.497 3.62 4.539l-.068.243-.014.055-5.061 17.86-3.694 13.05s6.213 4.038 6.438 11.316v1.313h-8.448"
      />
      <path
        fill="#464646"
        d="M28.95 16.12a3.76 3.76 0 0 0-3.676 2.997l-1.243.046a3.74 3.74 0 0 0-3.648-2.9 3.76 3.76 0 0 0-3.754 3.753 3.76 3.76 0 0 0 3.754 3.754 3.76 3.76 0 0 0 3.753-3.754q0-.144-.014-.275l1.079-.037q-.006.092-.005.174a3.76 3.76 0 0 0 3.754 3.754 3.76 3.76 0 0 0 3.754-3.754 3.765 3.765 0 0 0-3.754-3.758m-8.567 7.062a3.17 3.17 0 0 1-3.167-3.166 3.167 3.167 0 1 1 3.166 3.166m8.567-.133a3.17 3.17 0 0 1-3.166-3.166 3.174 3.174 0 0 1 3.166-3.17 3.17 3.17 0 0 1 3.166 3.17 3.167 3.167 0 0 1-3.166 3.166"
      />
      <path
        fill="#7A7A79"
        d="M24.04 24.073a.294.294 0 0 1-.01-.588l1.547-.055c.156-.018.298.12.303.285a.293.293 0 0 1-.284.303l-1.547.055zm.43 3.919c-.78 0-1.5-.175-2.368-.547a.293.293 0 0 1-.156-.385.293.293 0 0 1 .385-.156c1.262.541 2.143.629 3.488.335a.295.295 0 0 1 .349.225.295.295 0 0 1-.225.349 7 7 0 0 1-1.478.178z"
      />
      <path
        fill="#F5F5F0"
        d="M15.669 19.346s1.794-5.726 5.355-7.26c0 0 3.933 1.905 8.242 2.277s4.198 4.782 4.198 4.782l.606-2.878L31.335 9.6 25.04 7.07l-3.767 1.203-2.859 2.11-1.74 1.822-1.325 3.313.32 3.823z"
      />
    </g>
  </svg>
);
export default SvgAvatar;
