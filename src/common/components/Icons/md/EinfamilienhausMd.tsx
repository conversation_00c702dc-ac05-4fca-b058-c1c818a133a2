import * as React from 'react';
import type { SVGProps } from 'react';

const SvgEinfamilienhausMd = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={38} height={38} fill="none" viewBox="0 0 38 38" {...props}>
    <path fill="#14A0DC" d="M10.255 22.45H5.75V36.8h4.505z" />
    <path fill="#EBB40F" d="M37 15.115V5.15H19.875l10.145 9.965zM1.5 14.38l9.075-9.23H1.5z" />
    <path
      fill="#464646"
      d="M18.86 4.15 15.145.5l-3.59 3.65H.5V37.8H38V4.15zm-17.36 1h9.075L1.5 14.38zM12.25 36.8h-6.5V22.45h6.5zm17.05 0H13.25V21.45h-8.5V36.8H1.5v-21L15.155 1.915 29.3 15.8zm7.7 0h-6.7V16.115H37zm0-21.685h-7L19.875 5.15H37z"
    />
    <path fill="#14A0DC" d="M22.55 22.45h-3.8v6.7h3.8z" />
    <path fill="#464646" d="M25.55 30.15h-7.8v-8.7h7.8zm-6.8-1h5.8v-6.7h-5.8z" />
    <path fill="#32C864" d="M30.325 16.115V36.8H37V16.115z" />
  </svg>
);
export default SvgEinfamilienhausMd;
