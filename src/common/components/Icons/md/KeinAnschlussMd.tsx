import * as React from 'react';
import type { SVGProps } from 'react';

const SvgKeinAnschlussMd = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={70} height={71} fill="none" viewBox="0 0 70 71" {...props}>
    <path fill="#FFA933" d="M9.998 37.376h9.161V62H9.998z" />
    <path
      fill="#464646"
      d="M9.999 37.376h13.977V62H9.999zm27.138-27.84h16.114v15.307zM27.625.5 0 26.743V64h36.999v-2H25.974V35.375H8.001V62h-6V27.604L27.625 3.258l25.626 24.346V37.5h2V7.535h-20.22z"
    />
    <path
      fill="#464646"
      d="M27.624 27.877a5.66 5.66 0 0 1-5.65-5.65 5.657 5.657 0 0 1 5.65-5.65 5.657 5.657 0 0 1 5.65 5.65 5.66 5.66 0 0 1-5.65 5.65m0-9.3a3.653 3.653 0 0 0-3.65 3.65 3.654 3.654 0 0 0 3.65 3.65 3.654 3.654 0 0 0 3.65-3.65 3.654 3.654 0 0 0-3.65-3.65"
    />
    <path fill="#32C864" d="M55.25 13.948V37.5c6.503 0 11.775-5.273 11.775-11.775 0-6.505-5.272-11.777-11.775-11.777" />
    <path fill="#464646" d="m41 44.621 2.12-2.121 25.774 25.774-2.121 2.121z" />
    <path fill="#464646" d="m67 42.621 2.121 2.121-25.774 25.774-2.121-2.12z" />
  </svg>
);
export default SvgKeinAnschlussMd;
