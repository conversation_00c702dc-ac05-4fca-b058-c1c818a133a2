import * as React from 'react';
import type { SVGProps } from 'react';

const SvgMehrfamilienhausMd = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={33} height={38} fill="none" viewBox="0 0 33 38" {...props}>
    <path fill="#464646" d="M32.2 38H0V14.135L16.1.5l16.1 13.635zM1 37h30.2V14.6L16.1 1.81 1 14.6z" />
    <path fill="#14A0DC" d="M18.39 29.25h-4.575V37h4.575z" />
    <path fill="#32C864" d="M25.85 13.25h-3.5v5h3.5z" />
    <path fill="#464646" d="M26.655 18.25H21.54v1h5.115z" />
    <path fill="#EBB40F" d="M17.85 13.25h-3.5v5h3.5z" />
    <path fill="#464646" d="M18.655 18.25H13.54v1h5.115zm0-7.55H13.54v1h5.115z" />
    <path fill="#32C864" d="M9.85 13.25h-3.5v5h3.5z" />
    <path fill="#464646" d="M10.655 18.25H5.54v1h5.115z" />
    <path fill="#EBB40F" d="M25.85 20.75h-3.5v5h3.5z" />
    <path fill="#464646" d="M26.655 25.75H21.54v1h5.115z" />
    <path fill="#32C864" d="M17.85 20.75h-3.5v5h3.5z" />
    <path fill="#464646" d="M18.655 25.75H13.54v1h5.115z" />
    <path fill="#EBB40F" d="M9.85 20.75h-3.5v5h3.5z" />
    <path fill="#464646" d="M10.655 25.75H5.54v1h5.115z" />
    <path fill="#EBB40F" d="M25.85 28.25h-3.5v5h3.5z" />
    <path fill="#464646" d="M26.655 33.25H21.54v1h5.115z" />
    <path fill="#32C864" d="M9.85 28.25h-3.5v5h3.5z" />
    <path fill="#464646" d="M10.655 33.25H5.54v1h5.115z" />
    <path fill="#32C864" d="M17.85 7.2h-3.5v3.5h3.5z" />
    <path fill="#EBB40F" d="M19.64 3.5H32.2v10.635zm-7.08 0H0v10.635z" />
  </svg>
);
export default SvgMehrfamilienhausMd;
