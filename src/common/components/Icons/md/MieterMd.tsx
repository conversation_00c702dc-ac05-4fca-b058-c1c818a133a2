import * as React from 'react';
import type { SVGProps } from 'react';

const SvgMieterMd = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={30} height={37} fill="none" viewBox="0 0 30 37" {...props}>
    <path
      fill="#464646"
      d="M15.017 8.795V5.24A4.744 4.744 0 0 0 10.277.5a4.744 4.744 0 0 0-4.74 4.74v3.555a4.72 4.72 0 0 0 2.315 4.07v1.35A21.5 21.5 0 0 0 .437 16.45l-.27.14v7.455h1v-6.84a20.4 20.4 0 0 1 9.11-2.125c1.39 0 2.785.14 4.14.42l.2-.98a23 23 0 0 0-1.92-.3v-1.35a4.71 4.71 0 0 0 2.32-4.07zm-4.74 5.28c-.475 0-.95.02-1.42.05v-1.86l-.275-.14a3.73 3.73 0 0 1-2.045-3.33V5.24a3.74 3.74 0 0 1 7.48 0v3.555a3.73 3.73 0 0 1-2.045 3.33l-.275.14v1.865a23 23 0 0 0-1.42-.055"
    />
    <path fill="#EBB40F" d="M10.277 1.5a3.744 3.744 0 0 0-3.74 3.74v.865h7.48V5.24c0-2.06-1.68-3.74-3.74-3.74" />
    <path
      fill="#14A0DC"
      d="M10.277 15.075c-3.185 0-6.25.715-9.11 2.125v6.84H.682h7.015c-.33-5.05 2.675-8.615 6.72-8.545a20.5 20.5 0 0 0-4.14-.42"
    />
    <path fill="#32C864" d="M14.102 22.825a1.515 1.515 0 1 0 0-3.03 1.515 1.515 0 0 0 0 3.03" />
    <path
      fill="#464646"
      d="M23.022 34.395v-1.3h-1.6v-1.75h-1.45l-2.25-2.3a6.6 6.6 0 0 1-6.75-1.6 6.5 6.5 0 0 1-1.45-7.05 6.6 6.6 0 0 1 8.55-3.65 6.74 6.74 0 0 1 3.6 3.55c.6 1.5.65 3.165.15 4.7l7.25 7.25v4.05h-4.15zm-.6-4.05v1.75h1.6v1.9l1.3 1.3h2.05zm-1.8-5.15.15-.3a5.35 5.35 0 0 0-.05-4.2 5.3 5.3 0 0 0-3.05-3 5.45 5.45 0 0 0-4.25.05 5.6 5.6 0 0 0-2.95 3 5.5 5.5 0 0 0 1.2 6 5.61 5.61 0 0 0 6 1.25l.35-.15 2.4 2.5h2l-2.85-2.85.7-.7 7.8 7.8v-1.95zm-6.5-1.85a2 2 0 1 1-.001-3.999 2 2 0 0 1 .001 3.999m-1-2c0 .55.45 1 1 1s1-.45 1-1-.45-1-1-1-1 .45-1 1"
    />
  </svg>
);
export default SvgMieterMd;
