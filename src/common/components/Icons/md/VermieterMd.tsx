import * as React from 'react';
import type { SVGProps } from 'react';

const SvgVermieterMd = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={37} height={37} fill="none" viewBox="0 0 37 37" {...props}>
    <path
      fill="#EBB40F"
      d="m17.248 20.5 2.56 2.345-3.19 3.945-3.24.09-.33 2.62-2.485.665-.095 2.44-3.38 3.24H1.083l-.14-.515 15.765-15.11z"
    />
    <path fill="#32C864" d="m15.863 19.094-2.015-2.02L.708 29.23l.085 4.585z" />
    <path fill="#14A0DC" d="M27.103 12.43a2.935 2.935 0 1 0 0-5.87 2.935 2.935 0 0 0 0 5.87" />
    <g fill="#464646">
      <path d="M35.203 7.845c-2.43-5.99-9.24-8.895-15.25-6.51-2.88 1.2-5.18 3.47-6.42 6.33a12.3 12.3 0 0 0-.27 8.385L.333 28.98v7.225h7.4l3.39-3.39v-2.32h2.855v-3.12h2.585l4.015-4.105c4.23 1.405 8.89.3 12.04-2.855A11.59 11.59 0 0 0 35.203 7.84zm-3.335 11.54c-2.81 2.805-7.11 4.345-10.805 2.9l-.62-.21-4.21 4.39-3.145.01-.01.47-.025 2.56-2.9-.01-.14 2.815-2.975 2.94-5.04.02 15.25-14.76-.72-.74L1.108 34.8l.01-5.105 13.595-13.46-.27-.535c-.995-2.405-.8-5.265.25-7.65.98-2.47 3.465-4.865 5.95-5.805 2.435-.995 6-.595 8.41.46 2.385 1.03 4.145 3.445 5.14 5.84 1.52 3.67.49 8.035-2.325 10.84" />
      <path d="M26.998 5.97a3.564 3.564 0 0 0-3.565 3.565 3.564 3.564 0 0 0 3.565 3.565 3.564 3.564 0 0 0 3.565-3.565 3.564 3.564 0 0 0-3.565-3.565m0 6.17a2.601 2.601 0 0 1 0-5.2 2.601 2.601 0 0 1 0 5.2" />
    </g>
  </svg>
);
export default SvgVermieterMd;
