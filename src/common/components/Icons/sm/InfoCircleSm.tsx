import * as React from 'react';
import type { SVGProps } from 'react';

const SvgInfoCircleSm = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={32} height={33} fill="none" viewBox="0 0 32 33" {...props}>
    <path
      fill="#464646"
      fillRule="evenodd"
      d="M4.634 24.639c2.708 3.708 6.941 5.674 11.239 5.674 2.85 0 5.729-.864 8.208-2.661 6.189-4.483 7.571-13.193 3.082-19.416l-1.128.814c4.04 5.603 2.799 13.441-2.77 17.475-5.6 4.057-13.454 2.843-17.506-2.707C1.723 18.292 2.97 10.42 8.53 6.274a12.47 12.47 0 0 1 14.664 0l.817-1.125a13.86 13.86 0 0 0-16.305.005C1.529 9.758.152 18.498 4.634 24.639m8.125-1.932h6.278v-1.39h-2.444V13.31H13.47v1.39h1.731v6.617h-2.443zm2.105-12.134a1.045 1.045 0 0 0 2.087 0 1.044 1.044 0 0 0-2.087 0"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgInfoCircleSm;
