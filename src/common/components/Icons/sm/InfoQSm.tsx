import * as React from 'react';
import type { SVGProps } from 'react';

const SvgInfoQSm = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={20} height={21} fill="none" viewBox="0 0 20 21" {...props}>
    <circle cx={10} cy={10.5} r={10} fill="#0793CF" />
    <path
      fill="#F5F5F0"
      d="M7.943 8.932 6 8.646C6.212 6.896 7.552 5.5 9.695 5.5 11.627 5.5 13 6.678 13 8.475c0 1.647-1.195 2.597-2.367 2.815v1.144H8.78V10.11c1.362 0 2.21-.435 2.21-1.487 0-.836-.547-1.385-1.417-1.385-.95 0-1.508.687-1.63 1.693m.67 4.371h2.199V15.5h-2.2z"
    />
  </svg>
);
export default SvgInfoQSm;
