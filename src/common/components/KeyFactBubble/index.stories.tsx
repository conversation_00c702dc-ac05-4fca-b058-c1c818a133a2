import type { Meta, StoryObj } from '@storybook/react';

import { Theme } from '@/types/theme';

import KeyFactBubble, { KeyFactBubbleColor } from '.';

const meta: Meta<typeof KeyFactBubble> = {
  title: 'Component/KeyFactBubble',
  component: KeyFactBubble,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof KeyFactBubble>;

const descriptionText = {
  json: {
    nodeType: 'document',
    data: {},
    content: [
      {
        nodeType: 'paragraph',
        data: {},
        content: [
          {
            nodeType: 'text',
            value: '<b>Eine Beschreibung für die KeyFactBubble</b>',
            marks: [],
            data: {},
          },
        ],
      },
    ],
  },
  links: {
    assets: { hyperlink: [], block: [] },
    entries: { inline: [], hyperlink: [], block: [] },
    resources: { inline: [], hyperlink: [], block: [] },
  },
};

const Wrapper = ({ children, theme }: { children: React.ReactNode; theme: Theme }) => (
  <div className={`flex flex-col gap-4 bg-${theme}`}>{children}</div>
);

// Default is transparent
const KeyFactBubbleDefault = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble headline="1,5 MIO." description={descriptionText} theme={theme} />
);

const KeyFactBubbleTransparent = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble
    headline="1,5 MIO."
    description={descriptionText}
    backgroundColor={KeyFactBubbleColor.Transparent}
    theme={theme}
  />
);

const KeyFactBubbleBlue = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble
    headline="1,5 MIO."
    description={descriptionText}
    backgroundColor={KeyFactBubbleColor.Blue}
    theme={theme}
  />
);

const KeyFactBubbleGreen = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble
    headline="1,5 MIO."
    description={descriptionText}
    backgroundColor={KeyFactBubbleColor.Green}
    theme={theme}
  />
);

const KeyFactBubbleYellow = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble
    headline="1,5 MIO."
    description={descriptionText}
    backgroundColor={KeyFactBubbleColor.Yellow}
    theme={theme}
  />
);

const KeyFactBubbleBasalt = ({ theme }: { theme: Theme }) => (
  <KeyFactBubble
    headline="1,5 MIO."
    description={descriptionText}
    backgroundColor={KeyFactBubbleColor.Basalt}
    theme={theme}
  />
);

const AllBubbles = ({ theme }: { theme: Theme }) => (
  <>
    <KeyFactBubbleDefault theme={theme} />
    <KeyFactBubbleTransparent theme={theme} />
    <KeyFactBubbleBlue theme={theme} />
    <KeyFactBubbleGreen theme={theme} />
    <KeyFactBubbleYellow theme={theme} />
    <KeyFactBubbleBasalt theme={theme} />
  </>
);

export const DefaultWithThemeGray: Story = {
  render: () => (
    <Wrapper theme={Theme.Gray}>
      <AllBubbles theme={Theme.Gray} />
    </Wrapper>
  ),
};

export const DefaultWithThemeSand: Story = {
  render: () => (
    <Wrapper theme={Theme.Sand}>
      <AllBubbles theme={Theme.Sand} />
    </Wrapper>
  ),
};

export const DefaultWithThemeBasalt: Story = {
  render: () => (
    <Wrapper theme={Theme.Basalt}>
      <AllBubbles theme={Theme.Basalt} />
    </Wrapper>
  ),
};
