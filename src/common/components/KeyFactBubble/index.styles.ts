import { cva } from 'class-variance-authority';

const containerStyles = cva(
  'mx-auto flex aspect-square h-auto max-h-[267px] w-[267px] max-w-[267px] flex-col items-center justify-center gap-4 overflow-hidden rounded-full border border-1 px-12 text-center lg:w-full',
  {
    variants: {
      backgroundColor: {
        yellow: 'bg-flower',
        green: 'bg-gras',
        blue: 'bg-heaven',
        basalt: 'bg-basalt',
      },
      allWhite: {
        true: 'border-white',
        false: 'border-basalt',
      },
    },
  },
);

const headlineStyles = cva('text-4xl leading-none lg:text-4xl xl:text-6xl', {
  variants: {
    white: {
      true: 'text-white',
      false: 'text-basalt',
    },
  },
});

const descriptionStyles = cva('text-sm leading-[21px] lg:text-base lg:leading-6', {
  variants: {
    white: {
      true: 'text-white',
      false: 'text-basalt',
    },
  },
});

export { containerStyles, headlineStyles, descriptionStyles };
