import { render } from '@testing-library/react';

import { useAvailabilityCheckStoreBase } from '@/stores/AvailabilityCheckStore';

import AvailabilityCheckButton from '../index';

describe('AvailabilityCheckButton visibility based on global state', () => {
  beforeEach(() => {
    useAvailabilityCheckStoreBase.getState().setIsAcButtonHidden(false);
  });

  test.each([[false], [true]])('isAcButtonHidden: %s - component should render correctly', (isAcButtonHidden) => {
    useAvailabilityCheckStoreBase.getState().setIsAcButtonHidden(isAcButtonHidden);

    const { getByText } = render(<AvailabilityCheckButton label="Test Label" />);

    expect(getByText('Test Label')).toBeInTheDocument();
  });
});
