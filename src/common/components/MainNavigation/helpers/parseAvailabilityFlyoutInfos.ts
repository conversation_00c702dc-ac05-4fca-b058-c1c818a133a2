import type { AvailabilityFlyoutInfosComponent } from '@/apis/contentful/generated/types';
import type { Advantage, AvailabilityFlyoutInfos } from '@/apis/dg/types';

export function parseAvailabilityFlyoutInfos(
  availabilityFlyoutInfos?: AvailabilityFlyoutInfosComponent | null,
): AvailabilityFlyoutInfos | undefined {
  if (!availabilityFlyoutInfos) return undefined;
  const { businessTelephone, businessOpeningHours, businessAdvantagesCollection } = availabilityFlyoutInfos;
  if (!businessTelephone || !businessOpeningHours || !businessAdvantagesCollection) return undefined;

  return {
    phone: businessTelephone || '',
    openingHours: businessOpeningHours.json || '',
    advantages: businessAdvantagesCollection?.items.map((advantage) => ({
      icon: advantage?.icon || '',
      title: advantage?.title || '',
      text: advantage?.text || '',
      highlight: advantage?.highlight || false,
    })) as Advantage[] | undefined,
  };
}
