'use client';

import React, { Suspense, useEffect, useState } from 'react';

import { isMobile, isTablet } from 'react-device-detect';

import { CLOSE_BUTTON_LABEL, PageType, businessAreaMap } from '@/app/consts';
import type { BusinessArea } from '@/app/consts';

import type { Notification } from '@/apis/contentful/getPages';

import BrandLogo from '@/components/BrandLogo';
import ErrorMessage from '@/components/ErrorMessage';
import { AccountSm, CloseSm, ContactSm, MenuSm, SearchSm } from '@/components/Icons/sm';
import AvailabilityCheckButton from '@/components/MainNavigation/AvailabilityCheckButton';
import SubNavigationDesktop from '@/components/MainNavigation/SubNavigation/SubNavigationDesktop';
import SubNavigationMobile from '@/components/MainNavigation/SubNavigation/SubNavigationMobile';
import {
  acButtonStyles,
  desktopNavContainerStyles,
  desktopNavigationButtonStyles,
  desktopNavigationListStyles,
  mainNavigationContainerStyles,
  mobileNavContainerStyles,
  mobileNavigationListStyles,
} from '@/components/MainNavigation/index.styles';
import NavigationHotline from '@/components/NavigationHotline';
import NotificationBar from '@/components/NotificationBar';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useFlyoutStore } from '@/stores/FlyoutStore';
import { showNotification, useNotificationStore } from '@/stores/NotificationStore';
import { useUserStore } from '@/stores/UserStore';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';
import { getCountdownNotification, showCountdownNotification } from '@/utils/showCountdownNotification';

import AccountFlyout from './AccountFlyout';
import MainNavigationBackdrop from './Backdrop';
import ContactFlyout from './ContactFlyout';
import IconButton from './IconButton';
import MainNavigationDrawer from './MainNavigationDrawer';
import MainNavigationFlyout from './MainNavigationFlyout';
import SearchBar from './SearchBar';
import type { MainNavigationItem, NavigationLink, SubNavigationItem } from './types';

export type MainNavigationProps = {
  businessArea: BusinessArea;
  navigationItems?: MainNavigationItem[];
  initialNotification?: Notification;
  pageType?: PageType;
};

export default function MainNavigation({
  businessArea,
  navigationItems,
  initialNotification,
  pageType = PageType.Content,
}: MainNavigationProps) {
  const { flyout, setFlyout } = useFlyoutStore();
  const { trackEvent, trackAcEvent } = useTracking();

  const offerList = useUserStore.use.offerList();
  const acAddress = useUserStore.use.acAddress();
  const loading = useUserStore.use.loading();
  const getUserAddressAsString = useUserStore.use.getUserAddressAsString();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const setAcType = useAvailabilityCheckStore.use.setAcType();
  const isAcButtonHidden = useAvailabilityCheckStore.use.isAcButtonHidden();
  const notification = useNotificationStore.use.notification();
  const setNotification = useNotificationStore.use.setNotification();

  const [drawer, setDrawer] = useState('closed');
  const [searchOpen, setSearchOpen] = useState(false);
  const [mainNavigationIndex, setMainNavigationIndex] = useState(0);
  const [subNavigationIndex, setSubNavigationIndex] = useState(0);

  const businessAreaUrl = businessAreaMap.get(businessArea)?.url ?? '/';
  const showMenu = ![PageType.Campaign, PageType.LandingPage].includes(pageType);
  const userAddress = getUserAddressAsString();

  const setIsAcButtonHidden = useAvailabilityCheckStore.use.setIsAcButtonHidden();

  useEffect(() => {
    // Show notification on page load
    const displayCountdownNotification =
      acAddress && offerList?.campaign?.countdown
        ? showCountdownNotification(offerList.campaign?.countdown)
        : undefined;
    if (displayCountdownNotification && offerList?.campaign?.countdown) {
      getCountdownNotification(String(offerList.campaign.id), offerList.campaign.countdown);
    } else if (initialNotification) {
      void showNotification({ ...initialNotification });
    } else {
      setNotification(null);
    }
  }, [initialNotification, acAddress, offerList, setNotification]);

  function onAvailabilityCheckClick(event: React.MouseEvent<HTMLButtonElement>) {
    event.stopPropagation();
    if (flyout !== 'availabilityCheck') {
      setAcModalOpen(true);
      setAcType('flyOut');
      trackAcEvent({ acType: 'flyOut', acInteraction: 'open' });
    } else {
      // setFlyout('closed');
      setAcModalOpen(false);
      // TODO: TBD: What should be tracked if the user closes the ac flyout?
    }
  }

  function onSelectSubmenu(level: number, index: number) {
    if (level === 0) {
      setMainNavigationIndex(index);
      setDrawer('secondLevelMenu');
    }
    if (level === 1) {
      setSubNavigationIndex(index);
      setDrawer('thirdLevelMenu');
    }
  }

  function onMouseEnterMainNavigationItem(event: React.MouseEvent<HTMLButtonElement>, title: string) {
    event.preventDefault();

    if (!(isMobile || isTablet)) {
      setFlyout(title);
    }
  }

  function onMouseLeaveMainNavigationItem() {
    if (flyout !== 'availabilityCheck' && flyout !== 'error') setFlyout('closed');
  }

  // Type guard functions
  function isSubNavigation(item: SubNavigationItem | NavigationLink): item is SubNavigationItem {
    return item && (item as SubNavigationItem).navigationItems !== undefined;
  }

  function getSecondLevelNavigationItems(navigationItem: MainNavigationItem): (SubNavigationItem | NavigationLink)[] {
    if (
      !navigationItem ||
      !(navigationItem.navigationItems.length > 0) ||
      !(navigationItems && navigationItems[mainNavigationIndex].navigationItems)
    )
      return [];
    return navigationItems[mainNavigationIndex].navigationItems;
  }

  function getThirdLevelNavigationItems(navigationItem: SubNavigationItem | NavigationLink): NavigationLink[] {
    return isSubNavigation(navigationItem) ? (navigationItem as SubNavigationItem).navigationItems : [];
  }

  function getLink(navigationItem: SubNavigationItem | NavigationLink): NavigationLink | undefined {
    return isSubNavigation(navigationItem) ? (navigationItem as SubNavigationItem).link : undefined;
  }

  function onClickSubNavigationBackButton() {
    if (drawer === 'thirdLevelMenu') {
      setSubNavigationIndex(0);
      setDrawer('secondLevelMenu');
      setIsAcButtonHidden(true);
    }
    if (drawer === 'secondLevelMenu') {
      setMainNavigationIndex(0);
      setDrawer('firstLevelMenu');
      setIsAcButtonHidden(true);
    }
  }

  function onClickIconButton(buttonType: string) {
    setFlyout('closed');
    setSearchOpen(false);

    if (['search', 'contact', 'account'].includes(buttonType)) {
      setFlyout(flyout !== buttonType ? buttonType : 'closed');
      if (buttonType === 'search') {
        setSearchOpen(true);
      }
    }

    const newDrawerState = buttonType !== 'menu' ? 'closed' : drawer !== 'firstLevelMenu' ? 'firstLevelMenu' : 'closed';
    setDrawer(newDrawerState);

    setIsAcButtonHidden(newDrawerState !== 'closed');
  }

  function closeDrawer() {
    setDrawer('closed');
    setIsAcButtonHidden(false);
  }

  // Note: to test opening and closing on tablet landscape, use Browserstack or actual device! It won't work in Dev tools on a Desktop!
  function onClickMainNavigationTopLevelItem(event: React.MouseEvent<HTMLButtonElement>, item: MainNavigationItem) {
    const itemId = `mainnav-${getSanitizedIdSelector(item.title)}`;
    setFlyout(flyout === itemId ? 'closed' : itemId);
    trackEvent({
      category: 'Link',
      action: 'click',
      element: event.currentTarget,
      content: item.title,
    });
  }

  return (
    <div id="main-navigation" data-tracking-module="main-navigation" className={mainNavigationContainerStyles()}>
      <nav className={desktopNavContainerStyles()} aria-labelledby="main-navigation-label">
        <span id="main-navigation-label" hidden>
          Hauptnavigation
        </span>
        <BrandLogo href={businessAreaUrl} />
        {showMenu && !searchOpen && (
          <ul className={desktopNavigationListStyles()}>
            {navigationItems &&
              navigationItems.length > 0 &&
              navigationItems.map((item, index) => {
                const itemId = `mainnav-${getSanitizedIdSelector(item.title)}`;

                return (
                  <li key={`${itemId}-${index}`}>
                    <button
                      className={desktopNavigationButtonStyles({
                        item: index === 0 ? 'first' : index === navigationItems.length - 1 ? 'last' : 'middle',
                      })}
                      onMouseEnter={(event) => onMouseEnterMainNavigationItem(event, itemId)}
                      onMouseLeave={() => onMouseLeaveMainNavigationItem()}
                      onClick={(event) => onClickMainNavigationTopLevelItem(event, item)}
                    >
                      <span className={'font-semibold'}>{item.title}</span>
                      <MainNavigationFlyout id={itemId} size="large" visible={itemId === flyout}>
                        <SubNavigationDesktop navigationItems={item.navigationItems} />
                      </MainNavigationFlyout>
                    </button>
                  </li>
                );
              })}
          </ul>
        )}
        {showMenu && searchOpen && (
          <Suspense fallback={<p>Lade Suche...</p>}>
            <SearchBar />
          </Suspense>
        )}

        {!searchOpen && (
          <ul className={'flex items-center justify-center gap-4 align-middle text-sm'}>
            {showMenu && (
              <li>
                {!searchOpen && (
                  <IconButton
                    label="Suche"
                    isActive={flyout === 'search'}
                    icon={SearchSm}
                    onClick={() => {
                      setSearchOpen(true);
                      setFlyout('closed');
                    }}
                  />
                )}
              </li>
            )}

            {!searchOpen && (
              <>
                {<NavigationHotline location={'mainNavigation'} pageType={pageType} businessArea={businessArea} />}
                <li>
                  <IconButton
                    label="Kontakt"
                    isActive={flyout === 'contact'}
                    icon={ContactSm}
                    onClick={() => {
                      onClickIconButton('contact');
                    }}
                  />
                  <MainNavigationFlyout id="contact" visible={flyout === 'contact'}>
                    <ContactFlyout businessArea={businessArea} />
                  </MainNavigationFlyout>
                </li>
                {showMenu && (
                  <li>
                    <IconButton
                      label="Account"
                      isActive={flyout === 'account'}
                      icon={AccountSm}
                      onClick={() => {
                        onClickIconButton('account');
                      }}
                    />
                    <MainNavigationFlyout id="account" visible={flyout === 'account'}>
                      <AccountFlyout />
                    </MainNavigationFlyout>
                  </li>
                )}
              </>
            )}
          </ul>
        )}

        {searchOpen && (
          <button
            className="flex gap-4 self-center hover:text-hover"
            onClick={(event) => {
              setSearchOpen(false);
              setFlyout('closed');
              trackEvent({
                category: 'Button',
                action: 'click',
                element: event.currentTarget,
                content: `Suche ${CLOSE_BUTTON_LABEL}`,
              });
            }}
          >
            <span>{CLOSE_BUTTON_LABEL}</span>
            <CloseSm />
          </button>
        )}
      </nav>
      <nav className={mobileNavContainerStyles()}>
        <BrandLogo href={businessAreaUrl} />
        <ul className={mobileNavigationListStyles()}>
          {showMenu && (
            <li>
              <IconButton
                label="Suche"
                isActive={flyout === 'search'}
                icon={SearchSm}
                onClick={() => onClickIconButton('search')}
              />
              <MainNavigationFlyout id="search" size="large" visible={flyout === 'search'}>
                <Suspense fallback={<p>Lade Suche...</p>}>
                  <SearchBar />
                </Suspense>
              </MainNavigationFlyout>
            </li>
          )}
          <li>
            <IconButton
              label="Kontakt"
              isActive={flyout === 'contact'}
              icon={ContactSm}
              onClick={() => onClickIconButton('contact')}
            />
            <MainNavigationFlyout id="contact" size="large" visible={flyout === 'contact'} isMobile={true}>
              <ContactFlyout businessArea={businessArea} />
            </MainNavigationFlyout>
          </li>

          {showMenu && (
            <li>
              <IconButton
                label="Account"
                isActive={flyout === 'account'}
                icon={AccountSm}
                onClick={() => onClickIconButton('account')}
              />
              <MainNavigationFlyout id="account" size="large" visible={flyout === 'account'} isMobile={true}>
                <AccountFlyout />
              </MainNavigationFlyout>
            </li>
          )}
          {showMenu && navigationItems && (
            <li>
              <IconButton
                label="Menü"
                isActive={['firstLevelMenu', 'secondLevelMenu', 'thirdLevelMenu'].includes(drawer)}
                icon={MenuSm}
                onClick={() => onClickIconButton('menu')}
              />
              <MainNavigationDrawer visible={['firstLevelMenu', 'secondLevelMenu', 'thirdLevelMenu'].includes(drawer)}>
                <SubNavigationMobile
                  visible={['firstLevelMenu', 'secondLevelMenu', 'thirdLevelMenu'].includes(drawer)}
                  businessArea={businessArea}
                  pageType={pageType}
                  items={navigationItems}
                  userAddress={userAddress}
                  onSelectSubmenu={(index) => onSelectSubmenu(0, index)}
                  breadcrumbs={[]}
                  onClose={closeDrawer}
                />
              </MainNavigationDrawer>
            </li>
          )}
        </ul>
      </nav>
      {notification && !loading && (
        <NotificationBar
          id={notification.id}
          type={notification.type}
          headline={notification.headline}
          text={notification.text}
          cutoffDate={notification.cutoffDate}
        />
      )}
      <AvailabilityCheckButton
        label={userAddress}
        testId="ac-open-button"
        onClick={onAvailabilityCheckClick}
        className={acButtonStyles({ state: isAcButtonHidden ? 'hidden' : 'visible' })}
      />

      <MainNavigationFlyout
        id="searchError"
        size="large"
        visible={flyout === 'searchError'}
        errorFlyout={flyout === 'searchError'}
      >
        <ErrorMessage id={`search-bar-input-error`} message={'Bitte geben Sie mind. 3 Zeichen ein.'} />
      </MainNavigationFlyout>

      {showMenu && navigationItems && (
        <>
          <MainNavigationDrawer visible={['secondLevelMenu', 'thirdLevelMenu'].includes(drawer)}>
            <SubNavigationMobile
              visible={['secondLevelMenu', 'thirdLevelMenu'].includes(drawer)}
              businessArea={businessArea}
              items={getSecondLevelNavigationItems(navigationItems[mainNavigationIndex])}
              userAddress={userAddress}
              onSelectSubmenu={(index) => onSelectSubmenu(1, index)}
              breadcrumbs={[navigationItems[mainNavigationIndex].title]}
              onBack={onClickSubNavigationBackButton}
              onClose={closeDrawer}
            />
          </MainNavigationDrawer>

          <MainNavigationDrawer visible={drawer === 'thirdLevelMenu'}>
            <SubNavigationMobile
              visible={drawer === 'thirdLevelMenu'}
              businessArea={businessArea}
              link={getLink(navigationItems[mainNavigationIndex]?.navigationItems[subNavigationIndex])}
              items={getThirdLevelNavigationItems(
                navigationItems[mainNavigationIndex]?.navigationItems[subNavigationIndex],
              )}
              userAddress={userAddress}
              breadcrumbs={[
                navigationItems[mainNavigationIndex].title,
                navigationItems[mainNavigationIndex].navigationItems[subNavigationIndex].title,
              ]}
              onBack={onClickSubNavigationBackButton}
              onClose={closeDrawer}
            />
          </MainNavigationDrawer>
        </>
      )}

      <MainNavigationBackdrop visible={flyout !== 'closed'} />
    </div>
  );
}
