import React from 'react';

import { useArgs } from '@storybook/preview-api';
import type { Meta, StoryObj } from '@storybook/react';

import Copy from '@/components/Copy';
import Headline from '@/components/Headline';

import Modal from './index';

const meta: Meta<typeof Modal> = {
  title: 'UI/Modal',
  component: Modal,
  args: {},
};

export default meta;

type Story = StoryObj<typeof Modal>;

export const HasNormalContent: Story = {
  args: {
    isOpen: false,
  },
  render: function Component(args) {
    const [, setArgs] = useArgs();

    const onClose = () => {
      args.isOpen = false;
      setArgs({ isOpen: false });
    };

    // Forward all args and overwrite onValueChange
    return (
      <Modal
        id={'storybook-modal'}
        hasCloseBtn={true}
        isOpen={args.isOpen}
        onClose={onClose}
        className={'space-y-4 px-4 py-8 lg:px-[120px] lg:py-20'}
      >
        <Headline intent={'h2'} className={'text-center'}>
          Headline
        </Headline>
        <Copy className={'text-center'}>
          Phasellus dolor. In hac habitasse platea dictumst. Donec quam felis, ultricies nec, pellentesque eu, pretium
          quis, sem. Suspendisse eu ligula. Morbi mollis tellus ac sapien.
        </Copy>
      </Modal>
    );
  },
};

export const HasLongContent: Story = {
  args: {
    isOpen: false,
  },
  render: function Component(args) {
    const [, setArgs] = useArgs();

    const onClose = () => {
      args.isOpen = false;
      setArgs({ isOpen: false });
    };

    // Forward all args and overwrite onValueChange
    return (
      <div className={'flex flex-col items-center'}>
        <Headline intent={'h2'} className={'w-2/3 text-center'}>
          Headline
        </Headline>
        <Copy className={'p-8'}>
          Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
          tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
          Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
          <br />
          <br />
          Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
          posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
          mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu, nisl.
          Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed libero.
          Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad litora torquent
          per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae tortor.
          <br />
          <br />
          Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
          tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
          Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
          <br />
          <br />
          Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
          posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
          mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu, nisl.
          Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed libero.
          Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad litora torquent
          per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae tortor.
          <br />
          <br />
          Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
          tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
          Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
          <br />
          <br />
          Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
          posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
          mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu, nisl.
          Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed libero.
          Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad litora torquent
          per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae tortor.
        </Copy>
        <Modal
          id={'storybook-modal'}
          hasCloseBtn={true}
          isOpen={args.isOpen}
          onClose={onClose}
          className={'space-y-4 px-4 py-8 lg:px-[120px] lg:py-20'}
        >
          <Headline intent={'h2'} className={'text-center'}>
            Headline
          </Headline>
          <Copy className={'text-center'}>
            Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
            tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
            Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
            <br />
            <br />
            Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
            posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
            mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu,
            nisl. Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed
            libero. Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad
            litora torquent per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae
            tortor.
            <br />
            <br />
            Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
            tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
            Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
            <br />
            <br />
            Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
            posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
            mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu,
            nisl. Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed
            libero. Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad
            litora torquent per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae
            tortor.
            <br />
            <br />
            Curabitur turpis. Nam eget dui. Donec vitae orci sed dolor rutrum auctor. Pellentesque habitant morbi
            tristique senectus et netus et malesuada fames ac turpis egestas. Proin magna. Nam commodo suscipit quam.
            Vivamus in erat ut urna cursus vestibulum. Donec orci lectus, aliquam ut, faucibus non, euismod id, nulla.
            <br />
            <br />
            Vestibulum fringilla pede sit amet augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices
            posuere cubilia Curae; Fusce id purus. Etiam iaculis nunc ac metus. Sed libero. Nullam dictum felis eu pede
            mollis pretium. Fusce fermentum. Vestibulum turpis sem, aliquet eget, lobortis pellentesque, rutrum eu,
            nisl. Curabitur a felis in nunc fringilla tristique. Sed aliquam ultrices mauris. Praesent adipiscing. Sed
            libero. Cras dapibus. Praesent blandit laoreet nibh. Aliquam lobortis. Class aptent taciti sociosqu ad
            litora torquent per conubia nostra, per inceptos hymenaeos. Praesent vestibulum dapibus nibh. Etiam vitae
            tortor.
          </Copy>
        </Modal>
      </div>
    );
  },
};
