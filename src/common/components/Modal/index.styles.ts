import { cva } from 'class-variance-authority';

export const modalStyles = cva([
  // Common styles
  'fixed h-fit transform-gpu overflow-hidden transition-all transition-discrete duration-300 ease-out',
  // Backdrop styles
  'backdrop:bg-basalt/0 backdrop:transition-colors backdrop:transition-discrete backdrop:duration-300 open:backdrop:bg-basalt/50 open:backdrop:backdrop-blur starting:open:backdrop:bg-basalt/0',
  // Mobile styles with safe area and dynamic viewport support
  'inset-x-0 top-auto bottom-0 m-0 max-h-[calc(90dvh-env(safe-area-inset-bottom))] min-w-full translate-y-full rounded-tl-[8px] rounded-tr-[8px] open:translate-y-0 starting:open:translate-y-full',
  // Fallback for browsers that don't support dvh or env() (mobile only)
  'supports-[height:100dvh]:max-h-[calc(90dvh-env(safe-area-inset-bottom))] lg:supports-[height:100dvh]:max-h-none',
  'supports-[padding:env(safe-area-inset-bottom)]:pb-[env(safe-area-inset-bottom)] lg:supports-[padding:env(safe-area-inset-bottom)]:pb-0',
  // Large screen styles
  'lg:inset-0 lg:m-auto lg:max-w-[1000px] lg:min-w-auto lg:translate-y-0 lg:scale-[0.7] lg:rounded-[20px] lg:pb-0 lg:opacity-0 lg:open:scale-100 lg:open:opacity-100 lg:starting:open:translate-y-0 lg:starting:open:scale-[0.7] lg:starting:open:opacity-100',
  // Extra large screen styles
  'xl:max-w-[1256px]',
]);

// Styles for the inner container DIV
export const containerStyles = cva([
  // Base styles for the container
  'w-full bg-white shadow-container',
  'pt-12 xl:pt-16', // Add top padding for button space
  // Safe area support for mobile only
  'pb-[max(1rem,env(safe-area-inset-bottom))] lg:pb-0', // Ensure minimum padding while respecting safe area
]);

// modalButtonStyles remains unchanged
export const modalCloseButtonStyles = cva(
  'absolute top-4 right-4 z-50 flex gap-2 font-semibold text-basalt hover:text-hover xl:top-8 xl:right-8',
);
