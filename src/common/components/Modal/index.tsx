'use client';

import React, { useEffect, useRef, useState } from 'react';

import { CLOSE_BUTTON_LABEL } from '@/app/consts';

import { CloseSm } from '@/components/Icons/sm';
import Portal from '@/components/Portal';

import { useOutsideClick } from '@/hooks/useOutsideClick';
import useTracking from '@/hooks/useTracking';

import { cn } from '@/utils/cn';

import { containerStyles, modalCloseButtonStyles, modalStyles } from './index.styles';

type ModalProps = {
  id: string;
  isOpen: boolean;
  hasCloseBtn?: boolean;
  children: React.ReactNode;
  className?: string;
  closeOnOutsideClick?: boolean;
  onClose?: () => void;
};

export default function Modal({
  id,
  isOpen,
  hasCloseBtn = true,
  onClose,
  children,
  className,
  closeOnOutsideClick = true,
}: ModalProps) {
  const { trackEvent } = useTracking();
  const [isModalOpen, setModalOpen] = useState(isOpen);
  const modalRef = useRef<HTMLDialogElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useOutsideClick(containerRef, closeOnOutsideClick ? handleOutsideClick : () => {});

  function handleOutsideClick() {
    if (!isModalOpen || !closeOnOutsideClick) return;
    handleCloseModal();
  }

  function handleCloseModal() {
    setModalOpen(false);
    if (onClose) {
      onClose();
    }
  }

  function onCloseClick(event: React.MouseEvent<HTMLButtonElement>) {
    trackEvent({
      category: 'Modal',
      action: 'click',
      element: event.currentTarget,
      content: 'close',
    });
    handleCloseModal();
  }

  useEffect(() => {
    setModalOpen(isOpen);
  }, [isOpen]);

  useEffect(() => {
    const modalElement = modalRef.current;

    if (modalElement) {
      if (isModalOpen) {
        modalElement.showModal();
      } else {
        modalElement.close();
      }
    }
  }, [isModalOpen]);

  // Add event listener for native dialog close events (like ESC key)
  useEffect(() => {
    const modalElement = modalRef.current;

    function handleNativeClose() {
      if (isModalOpen && onClose) {
        setModalOpen(false);
        onClose();
      }
    }

    if (modalElement) {
      modalElement.addEventListener('close', handleNativeClose);

      return () => {
        modalElement.removeEventListener('close', handleNativeClose);
      };
    }
  }, [isModalOpen, onClose]);

  return (
    <Portal selector="#modal-root">
      <dialog id={id} data-tracking-module="modal" data-tracking-id={id} ref={modalRef} className={modalStyles()}>
        <div ref={containerRef} className={cn(containerStyles(), className)}>
          {hasCloseBtn && (
            <button type="button" className={modalCloseButtonStyles()} onClick={onCloseClick}>
              {CLOSE_BUTTON_LABEL} <CloseSm />
            </button>
          )}
          {children}
        </div>
      </dialog>
    </Portal>
  );
}
