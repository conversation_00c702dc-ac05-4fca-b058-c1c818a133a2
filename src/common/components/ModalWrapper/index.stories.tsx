import React, { useEffect } from 'react';

import type { <PERSON>a, StoryObj } from '@storybook/react';

import ModalWrapper from './index';

// Decorator to setup modal functionality for Storybook
const withModalSetup = (Story: React.ComponentType) => {
  useEffect(() => {
    // Create modal-root if it doesn't exist
    if (!document.getElementById('modal-root')) {
      const modalRoot = document.createElement('div');
      modalRoot.id = 'modal-root';
      document.body.appendChild(modalRoot);
    }

    return () => {
      // Cleanup: remove modal-root when component unmounts
      const modalRoot = document.getElementById('modal-root');
      if (modalRoot) {
        modalRoot.remove();
      }
    };
  }, []);

  return <Story />;
};

const meta: Meta<typeof ModalWrapper> = {
  title: 'Components/ModalWrapper',
  component: ModalWrapper,
  decorators: [withModalSetup],
  parameters: {
    layout: 'centered',
    // Add parameters to mock Next.js navigation for Storybook
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
      description: 'Content to display inside the modal',
    },
    label: {
      control: 'text',
      description: 'Label for the trigger button',
    },
    labelHidden: {
      control: 'boolean',
      description: 'Whether to hide the label visually (screen reader only)',
    },
    icon: {
      control: false,
      description: 'Icon to display in the trigger button',
    },
    inline: {
      control: 'boolean',
      description: 'Whether to render as inline button instead of primary button',
    },
    inlineStyles: {
      control: 'text',
      description: 'Additional CSS classes for inline button styling',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes for the modal',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Button style modal
export const ButtonModal: Story = {
  args: {
    label: 'Open Modal',
    inline: true,
    inlineStyles:
      'rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500',
    children: (
      <div className="p-6">
        <h2 className="mb-4 text-xl font-semibold">Modal Title</h2>
        <p className="mb-4 text-gray-600">
          This is a basic modal opened by a button. You can put any React components or HTML elements inside.
        </p>
        <div className="flex gap-2">
          <button className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">Action</button>
          <button className="rounded bg-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-400">Cancel</button>
        </div>
      </div>
    ),
  },
};

// Legal note inline modal
export const LegalNote: Story = {
  args: {
    label: 'Legal Notice',
    inline: true,
    inlineStyles: 'text-blue-600 hover:text-blue-800 underline text-sm',
    children: (
      <div className="max-w-2xl p-6">
        <h2 className="mb-4 text-xl font-semibold">Legal Notice</h2>
        <div className="space-y-4 text-sm text-gray-700">
          <p>
            This website is operated by [Company Name]. All content on this website is protected by copyright and other
            intellectual property laws.
          </p>
          <p>
            By using this website, you agree to our terms of service and privacy policy. Any unauthorized use of the
            content is strictly prohibited.
          </p>
          <p>For questions regarding this legal notice, please contact our legal <NAME_EMAIL>.</p>
        </div>
      </div>
    ),
  },
};

// Text link within Lorem ipsum
export const TextLinkInParagraph: Story = {
  render: () => (
    <div className="max-w-2xl p-4">
      <p className="leading-relaxed text-gray-700">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore
        magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
        consequat. Duis aute irure dolor in{' '}
        <ModalWrapper label="reprehenderit" inline={true} inlineStyles="text-blue-600 hover:text-blue-800 underline">
          <div className="p-6">
            <h2 className="mb-4 text-xl font-semibold">Additional Information</h2>
            <p className="text-gray-600">
              This modal was triggered by clicking on a word within the Lorem ipsum text. This demonstrates how modals
              can be seamlessly integrated into flowing text content.
            </p>
          </div>
        </ModalWrapper>{' '}
        in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident,
        sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
    </div>
  ),
};
