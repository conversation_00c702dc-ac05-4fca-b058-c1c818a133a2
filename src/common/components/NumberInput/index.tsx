import type { ForwardedRef } from 'react';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';

import ErrorMessage from '@/components/ErrorMessage';
import FormLabel from '@/components/FormLabel';
import { ArrowDownSm, ArrowUpSm, ErrorSm, InfoSm, SuccessSm } from '@/components/Icons/sm';
import { inputStyles, numberControlStyles } from '@/components/NumberInput/index.styles';
import Tooltip, { TooltipContent, TooltipTrigger } from '@/components/Tooltip';

import type { InputProps } from '@/types/inputProps';
import type { Tooltip as TooltipType } from '@/types/tooltip';

import { cn } from '@/utils/cn';

type NumberInputProps = {
  value?: number;
  min?: number;
  max?: number;
  tooltip?: TooltipType;
  onClick?: (event: React.MouseEvent<HTMLInputElement>) => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
} & InputProps;

const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(function NumberInput(
  {
    id,
    label,
    defaultValue,
    invalidText = '',
    invalid,
    valid,
    disabled,
    onClick,
    onChange,
    onKeyUp,
    tooltip,
    hotjarSuppress = false,
    className,
    required = false,
    min,
    max,
    readonly,
  }: NumberInputProps,
  ref: ForwardedRef<HTMLInputElement | null>,
) {
  const innerRef = useRef<HTMLInputElement>(null);
  const hasIcon = !!invalid || !!valid;
  const inputClasses = inputStyles({ isError: invalid, hasIcon: hasIcon, disabled });
  const numberControlClasses = numberControlStyles({ isDisabled: disabled });

  useImperativeHandle(ref, () => innerRef.current!, []);

  const increment = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    innerRef.current?.stepUp();
    innerRef.current?.dispatchEvent(new Event('change', { bubbles: true }));
  };

  const decrement = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    innerRef.current?.stepDown();
    innerRef.current?.dispatchEvent(new Event('change', { bubbles: true }));
  };

  return (
    <div className={cn('flex flex-col', className)}>
      {label && (
        <div className="mb-[4px] flex items-center gap-1">
          <FormLabel htmlFor={id} disabled={disabled} required={required}>
            {label}
          </FormLabel>

          {tooltip && (
            <Tooltip>
              <TooltipTrigger>
                <InfoSm width={16} height={16} />
                <span className="sr-only">Hinweis</span>
              </TooltipTrigger>
              <TooltipContent className="w-max max-w-[200px] border border-basalt bg-sand px-4 py-4 text-basalt">
                <div className="text-sm font-bold">{tooltip.title}</div>
                <p className="text-sm">{tooltip.text}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      )}
      <div className="relative flex items-center">
        <input
          id={id}
          ref={innerRef}
          name={id}
          defaultValue={defaultValue}
          className={inputClasses}
          type="number"
          min={min}
          max={max}
          disabled={disabled}
          onClick={onClick}
          onChange={onChange}
          onKeyUp={onKeyUp}
          aria-describedby={`${id}-error`}
          aria-invalid={!!invalid}
          {...(required && { 'aria-required': 'true' })}
          {...(hotjarSuppress ? { 'data-hj-suppress': '' } : {})}
          {...(typeof readonly !== 'undefined' ? { readOnly: readonly } : {})}
        />
        <span className="absolute right-0 flex items-center">
          {invalid && (
            <span className="mr-3">
              <ErrorSm />
            </span>
          )}
          {valid && (
            <span className="mr-3">
              <SuccessSm />
            </span>
          )}
          <div className={numberControlClasses}>
            <button
              aria-label="Hochzählen"
              className="mb-[-2px]"
              disabled={disabled}
              onClick={(event) => increment(event)}
              {...(hotjarSuppress ? { 'data-hj-suppress': '' } : {})}
            >
              <ArrowUpSm />
            </button>
            <button
              aria-label="Runterzählen"
              className="mt-[-2px]"
              disabled={disabled}
              onClick={(event) => decrement(event)}
              {...(hotjarSuppress ? { 'data-hj-suppress': '' } : {})}
            >
              <ArrowDownSm />
            </button>
          </div>
        </span>
      </div>
      <ErrorMessage id={`${id}-error`} message={invalidText} />
    </div>
  );
});

export default NumberInput;
