import Image from 'next/image';

import BrandLogo from '@/components/BrandLogo';
import Copy from '@/components/Copy';
import FooterCredits from '@/components/Footer/FooterCredits';
import Headline from '@/components/Headline';

import errorPageImage from './error-page-image.svg';
import {
  containerStyles,
  contentStyles,
  footerCreditsStyles,
  footerStyles,
  headlineStyles,
  imageContainerStyles,
  imageStyles,
  mainContentStyles,
  navigationStyles,
} from './index.styles';

export type PageError = {
  type?: 'error' | 'maintenance';
  pathname: string;
};

function getPageErrorContent({ type = 'error' }: { type: PageError['type'] }) {
  switch (type) {
    case 'maintenance':
      return {
        headline: 'Kurze Pause: Wir führen aktuell Wartungsarbeiten durch.',
        text: 'Wir arbeiten bereits daran die Datenautobahn zu reparieren und sind bald wieder für Sie da. In der Zwischenzeit können Sie auf unseren anderen Seiten vorbeischauen.',
      };
    default:
      return {
        headline: 'Hoppla! Hier ist wohl ein Glasfaserkabel durcheinandergeraten.',
        text: 'Beim Aufruf der Seite gab es technische Schwierigkeiten. Keine Sorge: Versuchen Sie die Seite einfach erneut zu laden.',
      };
  }
}

export default function PageErrorContent({ type }: PageError) {
  // TODO: use type Wartung for maintenance error page when decided where we get the info from
  const { headline, text } = getPageErrorContent({ type });

  return (
    <div className={containerStyles()}>
      <nav className={navigationStyles()}>
        <BrandLogo href={'/'} />
      </nav>
      <main id="main" className={mainContentStyles()}>
        <div className={imageContainerStyles()}>
          <Image
            className={imageStyles()}
            src={errorPageImage}
            alt={'500er Fehlerbild'}
            width={790}
            height={590}
            priority
          />
        </div>

        <div className={contentStyles()}>
          <Headline className={headlineStyles()}>{headline}</Headline>
          <Copy>{text}</Copy>
        </div>
      </main>
      <footer id="footer" className={footerStyles()}>
        <FooterCredits className={footerCreditsStyles()} />
      </footer>
    </div>
  );
}
