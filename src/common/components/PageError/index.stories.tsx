import React from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import { BusinessArea } from '@/app/consts';

import { useUserStore } from '@/stores/UserStore';

// Create a simplified version of PageError that doesn't use PageWrapper to avoid Sharp dependencies
import PageErrorContent from './PageErrorContent';
import type { PageError as PageErrorType } from './types';

function PageError({ type }: PageErrorType) {
  // Mock pathname since we can't use usePathname in Storybook easily
  const pathname = '/';

  return (
    <div className="flex min-h-screen flex-col">
      <PageErrorContent pathname={pathname} type={type} />
    </div>
  );
}

const withMockedUserStorePreVC = (Story: React.ComponentType) => {
  useUserStore.setState({
    offerList: undefined,
    userISP: {
      ip: '***************',
      isp: 'Test ISP',
    },
    loading: false,
  });

  return <Story />;
};

const meta: Meta<typeof PageError> = {
  title: 'UI/PageError',
  component: PageError,
  tags: ['autodocs'],
  argTypes: {},
  decorators: [withMockedUserStorePreVC],
  parameters: {
    nextjs: {
      appDirectory: true,
    },
    navigation: {
      pathname: '/',
    },
  },
};

export default meta;
type Story = StoryObj<typeof PageError>;

export const Page500: Story = {
  render: () => <PageError />,
};

export const PageMaintenance: Story = {
  render: () => <PageError type="maintenance" />,
};

export const Page500Jobportal: Story = {
  render: () => <PageError businessArea={BusinessArea.Companies} />,
};
