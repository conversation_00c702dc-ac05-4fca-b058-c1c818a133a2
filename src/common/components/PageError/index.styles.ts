import { cva } from 'class-variance-authority';

export const containerStyles = cva('relative flex h-screen min-h-screen flex-col justify-between');

export const navigationStyles = cva(
  'flex h-mobile-nav items-center justify-center bg-white p-2 shadow-container lg:h-desktop-nav lg:p-4',
);

export const mainContentStyles = cva(
  'flex flex-col items-center justify-center gap-4 px-4 py-8 md:flex-row md:gap-20 md:p-20',
);

export const imageContainerStyles = cva('flex w-full items-center md:w-1/2');

export const imageStyles = cva('aspect-3/2 md:aspect-4/3');

export const contentStyles = cva('flex w-full flex-col gap-4 text-basalt md:w-1/2 md:gap-6');

export const headlineStyles = cva('font-semibold word-break');

export const footerStyles = cva(
  'flex w-full items-center bg-basalt bg-footer-mobile bg-[left_-1rem_top] bg-repeat-x text-white md:justify-center md:bg-footer-desktop md:pt-28 md:pb-20',
);

export const footerCreditsStyles = cva('px-4 pt-12 pb-6 md:p-0');
