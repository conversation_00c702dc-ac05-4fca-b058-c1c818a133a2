import React from 'react';

import { usePathname } from 'next/navigation';

import { BusinessArea } from '@/app/consts';

import PageWrapper from '@/components/PageWrapper';

import PageErrorContent from './PageErrorContent';
import type { PageError } from './types';

export default function PageError({ type, businessArea }: PageError) {
  const pathname = usePathname();

  return (
    <PageWrapper pageType="500" businessArea={businessArea ?? BusinessArea.Private}>
      <PageErrorContent pathname={pathname} type={type} />
    </PageWrapper>
  );
}
