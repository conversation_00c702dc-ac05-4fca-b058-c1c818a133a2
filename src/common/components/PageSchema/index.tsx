import React from 'react';

import Script from 'next/script';

import { BusinessArea, PageType } from '@/app/consts';

import { getOfferList } from '@/apis/dg/offerList';
import { Provider } from '@/apis/dg/types';
import { getReviews } from '@/apis/trustedShops/reviews';

import type { ContentImage } from '@/types/contentImage';

type PageSchemaProps = {
  businessArea: string;
  pageType: string;
  title?: string;
  image?: ContentImage;
  publishedAt?: Date;
};

async function PageSchema({ businessArea, pageType, title = 'undefined-title', image, publishedAt }: PageSchemaProps) {
  const reviews = await getReviews();

  if (businessArea === BusinessArea.Private) {
    switch (pageType) {
      case PageType.Homepage: {
        const organizationSchema = {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          url: 'https://www.deutsche-glasfaser.de',
          name: 'Deutsche Glasfaser',
          logo: 'https://www.deutsche-glasfaser.de/logo.svg',
          sameAs: [
            'https://www.facebook.com/DeutscheGlasfaser/',
            'https://twitter.com/DtGlasfaser',
            'https://twitter.com/DG_Glasfaser',
            'https://www.youtube.com/user/DeutscheGlasfaser',
            'https://www.instagram.com/heimat.verbunden/',
            'https://www.xing.com/companies/deutscheglasfaserunternehmensgruppe',
            'https://de.linkedin.com/company/deutsche-glasfaser-unternehmensgruppe',
          ],
          contactPoint: [
            {
              '@type': 'ContactPoint',
              telephone: '+49-2861-8133-400',
              contactType: 'sales',
              contactOption: 'TollFree',
              areaServed: 'DE',
            },
            {
              '@type': 'ContactPoint',
              telephone: '+49-2861-890-600',
              contactType: 'customer support',
              contactOption: 'TollFree',
              areaServed: 'DE',
            },
          ],
        };
        const aggregateRatingSchema = {
          '@context': 'https://schema.org',
          '@type': 'AggregateRating',
          ratingValue: reviews?.overallMark,
          ratingCount: reviews?.activeReviewCount,
        };

        const offers = await getOfferList({ provider: Provider.Private });
        const productSchema = offers?.offers.map((offer) => {
          const { name, bandwidth, twoYearTariffDetails } = offer;
          const { promotions } = twoYearTariffDetails;
          return {
            '@context': 'https://schema.org/',
            '@type': 'Product',
            name: `${name} ${bandwidth}`,
            description: 'Eine Glasfaser - tausend Möglichkeiten',
            url: `https://www.deutsche-glasfaser.de/tarife/${name.toLowerCase().replace(' ', '-')}-${bandwidth}/`,
            brand: { '@type': 'Thing', name: 'Deutsche Glasfaser Holding GmbH' },
            offers: {
              '@type': 'Offer',
              priceCurrency: 'EUR',
              availability: 'InStock',
              price: promotions[0].price,
              url: `https://www.deutsche-glasfaser.de/tarife/${name.toLowerCase().replace(' ', '-')}-${bandwidth}/`,
              seller: { '@type': 'Organization', name: 'Deutsche Glasfaser Holding GmbH' },
            },
          };
        });
        return (
          <>
            <Script
              id={'organization-schema'}
              type="application/ld+json"
              dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
            />
            <Script
              id={'aggregate-rating-schema'}
              type="application/ld+json"
              dangerouslySetInnerHTML={{ __html: JSON.stringify(aggregateRatingSchema) }}
            />
            {productSchema &&
              productSchema.map((product, index) => (
                <Script
                  key={index}
                  id={`product-schema-${product.name.replaceAll(' ', '-').toLowerCase()}`}
                  type="application/ld+json"
                  dangerouslySetInnerHTML={{ __html: JSON.stringify(product) }}
                />
              ))}
          </>
        );
      }
      case PageType.DigitalKnowledge:
        const jsonData = {
          '@context': 'https://schema.org',
          '@type': 'Article',
          headline: title,
          image: image?.src,
          author: {
            '@type': 'Organization',
            name: 'Deutsche Glasfaser',
          },
          publisher: {
            '@type': 'Organization',
            name: 'Deutsche Glasfaser',
            logo: {
              '@type': 'ImageObject',
              logo: 'https://www.deutsche-glasfaser.de/logo.svg',
            },
          },
          datePublished: publishedAt,
        };
        return (
          <Script
            id={`page-schema-${title}`}
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonData) }}
          />
        );
    }
  }
}

export default PageSchema;
