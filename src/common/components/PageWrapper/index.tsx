'use client';

import React, { useEffect } from 'react';
import type { ReactNode } from 'react';

import { usePathname } from 'next/navigation';

import { cva } from 'class-variance-authority';

import type { BusinessArea } from '@/app/consts';

import AvailabilityCheckModal from '@/components/AvailabilityCheckModal';
import FloatingActionButton from '@/components/FloatingActionButton';

import usePageTracking from '@/hooks/usePageTracking';
import useTracking from '@/hooks/useTracking';
import { useUserInitialization } from '@/hooks/useUserInitialization';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useFlyoutStore } from '@/stores/FlyoutStore';
import { useHotlineListStore } from '@/stores/HotlineListStore';
import { useUserStore } from '@/stores/UserStore';

import { OPEN_AC_FLYOUT_URL_PARAM } from '@/utils/specialIds/specialIdsCatalog';
import { queryParamsHasKey, removeQueryParam } from '@/utils/url/queryParams';

type PageWrapperProps = {
  pageType: string;
  businessArea: BusinessArea;
  children: ReactNode;
  hasScrollToTop?: boolean;
  hasCallButton?: boolean;
};

const pageWrapperStyles = cva('flex min-h-screen flex-col');

export default function PageWrapper({
  pageType,
  businessArea,
  children,
  hasScrollToTop,
  hasCallButton = false,
}: PageWrapperProps) {
  const pathname = usePathname();

  const { trackPage } = usePageTracking({ pageType, businessArea });
  const { trackAcEvent } = useTracking();

  useUserInitialization(businessArea);

  const offerList = useUserStore.use.offerList();
  const userISP = useUserStore.use.userISP();
  const trackingEnabled = useUserStore.use.trackingEnabled();
  const setTrackingEnabled = useUserStore.use.setTrackingEnabled();

  const loadHotlineList = useHotlineListStore.use.loadHotlineList();

  const resetFlyout = useFlyoutStore.use.resetFlyout();

  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const setAcType = useAvailabilityCheckStore.use.setAcType();

  useEffect(() => {
    void loadHotlineList();
  }, [loadHotlineList]);

  useEffect(() => {
    if (trackingEnabled) {
      trackPage(userISP, offerList);
      setTrackingEnabled(false);
      if (queryParamsHasKey(OPEN_AC_FLYOUT_URL_PARAM)) {
        setAcModalOpen(true);
        setAcType('query');
        trackAcEvent({ acType: 'query', acInteraction: 'open' });
        removeQueryParam(OPEN_AC_FLYOUT_URL_PARAM);
      }
    }
  }, [trackingEnabled, offerList, userISP, trackPage, setTrackingEnabled, setAcModalOpen, setAcType, trackAcEvent]);

  useEffect(() => {
    resetFlyout();
    setAcModalOpen(false);
  }, [pathname, resetFlyout, setAcModalOpen]);

  // Check if the clicked element is a link to itself, if so trigger page tracking
  // Reason: When clicking a link to the same page, the page is not reloaded and the page tracking won't trigger
  function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
    const anchorElement = (event.target as HTMLElement).closest('a');
    const href = anchorElement?.getAttribute('href');

    // Reset flyout when clicking a link
    if (href) resetFlyout();

    // check if the href is not the same as the current pathname to avoid double tracking
    if (!href || pathname !== href) return;

    trackPage(userISP, offerList);
  }

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
    <div className={pageWrapperStyles()} onClick={handleClick}>
      {children}
      <FloatingActionButton
        businessArea={businessArea}
        pageType={pageType}
        hasScrollToTop={hasScrollToTop}
        hasCallButton={hasCallButton}
      />
      <AvailabilityCheckModal />
    </div>
  );
}
