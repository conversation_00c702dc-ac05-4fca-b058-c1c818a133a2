import type { Meta, StoryObj } from '@storybook/react';

import { DEFAULT_DG_LOGO, DEFAULT_DG_PARTNER_LOGO } from '@/app/consts';

import { projectDemandBundling } from '@/mock/project/mockedProjects';

import ProjectPartnerCard from './index';

const meta: Meta<typeof ProjectPartnerCard> = {
  title: 'Components/Areasite/Project/ProjectPartnerCard',
  component: ProjectPartnerCard,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof ProjectPartnerCard>;

export const ServicePartner: Story = {
  args: {
    ...projectDemandBundling.servicepointPartners[0],
    fallbackLogo: DEFAULT_DG_PARTNER_LOGO,
  },
};

export const SalesPartner: Story = {
  args: {
    ...projectDemandBundling.salesPartners[0],
    fallbackLogo: DEFAULT_DG_LOGO,
  },
};

export const ISPPartner: Story = {
  args: {
    ...projectDemandBundling.ispPartners[0],
    fallbackLogo: DEFAULT_DG_LOGO,
  },
};
