import Image from 'next/image';

import Copy from '@/components/Copy';
import { projectPartnerCardStyles } from '@/components/ProjectPartnerCard/index.styles';
import TextLink from '@/components/TextLink';

export default function ProjectPartnerCard({
  name,
  street,
  building,
  zipcode,
  city,
  logoUrl,
  emailAddress,
  phoneNr,
  mobileNr,
  faxNr,
  website,
  openingHours,
  fallbackLogo,
}: ProjectPartner & { fallbackLogo: string }) {
  return (
    <div className={projectPartnerCardStyles()}>
      {!logoUrl && <Image src={fallbackLogo} alt={name} width={180} height={120} />}

      {logoUrl && <Image src={logoUrl} alt={name} width={180} height={120} />}

      <div>
        <Copy weight="bold">{name}</Copy>

        {street && zipcode && city && (
          <Copy>
            {street}
            {building ? ` ${building}` : ''}, {zipcode} {city}
          </Copy>
        )}

        {[phoneNr, mobileNr, faxNr]
          .filter((x) => !!x)
          .map((x) => (
            <Copy key={x}>
              <TextLink href={`tel:${x}`}>{x}</TextLink>
            </Copy>
          ))}

        {emailAddress && (
          <Copy>
            <TextLink href={`mailto:${emailAddress}`}>{emailAddress}</TextLink>
          </Copy>
        )}

        {website && (
          <Copy>
            <TextLink href={website}>{website.replace(/(http|https):\/\//g, '')}</TextLink>
          </Copy>
        )}
      </div>

      <div>
        {openingHours && (
          <>
            <Copy>Öffnungszeiten:</Copy>
            <Copy>{openingHours}</Copy>
          </>
        )}
      </div>
    </div>
  );
}
