import React, { useMemo } from 'react';

import { documentToReactComponents } from '@contentful/rich-text-react-renderer';
import type { Options } from '@contentful/rich-text-react-renderer';
import { BLOCKS, INLINES, MARKS } from '@contentful/rich-text-types';
import type { Block, Document, Inline, Text } from '@contentful/rich-text-types';
import { cva } from 'class-variance-authority';

import { ApiRoutes } from '@/app/consts';

import { parseInternalLinkData } from '@/apis/contentful/parser/internalLink';

import Copy from '@/components/Copy';
import Headline from '@/components/Headline';
import { Hotline } from '@/components/HotlineList';
import LegalNote from '@/components/LegalNote';
import List from '@/components/List';
import type { ListItem } from '@/components/List';
import TextLink from '@/components/TextLink';

import type { RichTextLinks } from '@/types/pageProps';

import { encodeString } from '@/utils/customEncoder';

export interface RichTextProps {
  document: Document;
  links?: RichTextLinks;
}

const richTextStyles = cva('space-y-8 hyphens-auto');

export function RichText({
  document,
  links = { entries: { hyperlink: [], inline: [] }, assets: { hyperlink: [] } },
}: RichTextProps) {
  const options: Options = useMemo(
    () => ({
      renderMark: {
        [MARKS.BOLD]: (text) => <strong>{text}</strong>,
      },
      renderNode: {
        [BLOCKS.HEADING_2]: (node, children) => (
          <Headline type={'h2'} intent={'h2'}>
            {children}
          </Headline>
        ),
        [BLOCKS.HEADING_3]: (node, children) => (
          <Headline type={'h3'} intent={'h3'}>
            {children}
          </Headline>
        ),
        [BLOCKS.HEADING_4]: (node, children) => (
          <Headline type={'h4'} intent={'h4'}>
            {children}
          </Headline>
        ),
        [BLOCKS.HEADING_5]: (node, children) => (
          <Headline type={'h5'} intent={'h5'}>
            {children}
          </Headline>
        ),
        [BLOCKS.HEADING_6]: (node, children) => (
          <Headline type={'h6'} intent={'h6'}>
            {children}
          </Headline>
        ),
        [BLOCKS.OL_LIST]: (node) => {
          const items = convertRichTextNodesToList(node, options) as ListItem[];
          return <List items={items} ordered />;
        },
        [BLOCKS.UL_LIST]: (node) => {
          const items = convertRichTextNodesToList(node, options) as ListItem[];
          return <List ordered={false} items={items} />;
        },
        [BLOCKS.TABLE]: (node, children) => {
          return (
            <RichTextTable node={node} options={options}>
              {children}
            </RichTextTable>
          );
        },
        [BLOCKS.TABLE_ROW]: (node, children) => (
          <tr className="border-collapse gap-2 border-[1px] lg:table-row">{children}</tr>
        ),
        [BLOCKS.TABLE_HEADER_CELL]: (node, children) => (
          <th className="border-sand bg-basalt px-4 py-3 text-left *:text-xl *:font-semibold *:text-sand lg:table-cell lg:border-r lg:border-l">
            {children}
          </th>
        ),
        [BLOCKS.TABLE_CELL]: (node, children) => (
          <td className="border-gray-200 px-4 text-left lg:table-cell lg:border-r lg:border-l lg:py-3">{children}</td>
        ),
        [BLOCKS.PARAGRAPH]: (node, children) => <Copy>{children}</Copy>,
        [INLINES.ENTRY_HYPERLINK]: (node) => {
          const linkedEntry = links.entries.hyperlink.find((entry) => entry?.sys?.id === node.data?.target?.sys?.id);

          const textNodes = node.content.filter((n) => n.nodeType === 'text') as Text[];
          const linkedText = textNodes.map((c) => c.value).join('');

          if (linkedEntry && linkedEntry.__typename === 'Page') {
            return <TextLink href={linkedEntry.slug as string}>{linkedText}</TextLink>;
          } else {
            return <span>{linkedText}</span>;
          }
        },
        [INLINES.EMBEDDED_ENTRY]: (node) => {
          const inlineEntry = links.entries.inline.find((entry) => entry?.sys?.id === node.data?.target?.sys?.id);

          if (!inlineEntry) return;

          if (inlineEntry.__typename === 'LegalNote') {
            return (
              <LegalNote
                title={inlineEntry?.headline ?? 'Headline'}
                text={inlineEntry?.text ?? 'Lorem Ipsum...'}
                textType={'Markdown'}
              />
            );
          }

          if (inlineEntry.__typename === 'HotlineList') {
            return <Hotline />;
          }

          if (inlineEntry.__typename === 'InternalLinkComponent') {
            const internalLink = parseInternalLinkData(inlineEntry);
            return <TextLink href={internalLink.href}>{internalLink.label}</TextLink>;
          }
        },

        [INLINES.HYPERLINK]: (node) => {
          const textNodes = node.content.filter((n) => n.nodeType === 'text') as Text[];
          const label = textNodes.map((c) => c.value).join('');
          return (
            <TextLink isExternal href={node.data.uri}>
              {label}
            </TextLink>
          );
        },
        [INLINES.ASSET_HYPERLINK]: (node) => {
          const linkedEntry = links.assets.hyperlink.find((entry) => entry?.sys?.id === node.data?.target?.sys?.id);

          if (!linkedEntry && !node.content?.[0]) return;

          if (linkedEntry && typeof linkedEntry.url === 'string') {
            const encodedUrl = encodeString(linkedEntry.url);
            const href = `${ApiRoutes.MEDIA}?file=${encodedUrl}`;
            return (
              <TextLink href={href} isExternal>
                {documentToReactComponents(node.content?.[0] as Document)}
              </TextLink>
            );
          } else {
            return <span>{documentToReactComponents(node.content?.[0] as Document)}</span>;
          }
        },
      },
    }),
    [links],
  );

  if (!document) {
    return null;
  }

  return <div className={richTextStyles()}>{documentToReactComponents(document, options)}</div>;
}

function convertRichTextNodesToList(node: Block | Inline, options: Options): (ListItem | undefined)[] {
  return node.content
    .map((n) => {
      if (n.nodeType === 'list-item') {
        const texts = n.content.filter((doc) => doc.nodeType === 'paragraph');
        const subitems = n.content.filter((doc) => ['unordered-list', 'ordered-list'].includes(doc.nodeType));

        if (texts.length > 0 || subitems.length > 0) {
          return {
            text: documentToReactComponents(texts[0] as Document, options),
            subItems: subitems.flatMap((subitem) => convertRichTextNodesToList(subitem as Block | Inline, options)),
          } as ListItem;
        }
      }
    })
    .filter((item) => !!item);
}

function extractTextFromContentfulNode(node: Block | Inline | Text): string {
  if (!node) return '';
  if (node.nodeType === 'text') {
    return node.value || '';
  }
  if ('content' in node && Array.isArray(node.content)) {
    return node.content.map(extractTextFromContentfulNode).join('');
  }
  return '';
}

// Extracted Table Component
interface RichTextTableProps {
  node: Block | Inline;
  options: Options;
  children?: React.ReactNode;
}

function RichTextTable({ node, options, children }: RichTextTableProps) {
  // Extract header labels from the first row
  const headerRow = node.content.find((row) => row.nodeType === 'table-row') as Block | undefined;
  const headerCells =
    headerRow && 'content' in headerRow
      ? headerRow.content.filter((cell: Block | Inline | Text) => cell.nodeType === 'table-header-cell')
      : [];
  const headerLabels = headerCells.map((cell: Block | Inline | Text) =>
    documentToReactComponents(
      {
        nodeType: 'document',
        data: {},
        content: [cell as Block | Inline | Text],
      } as Document,
      options,
    ),
  );

  // Get all body rows (excluding the header row)
  const bodyRows = node.content.filter((row, idx) => idx !== 0 && (row as Block).nodeType === 'table-row') as Block[];
  const bodyCells = bodyRows.map((row: Block) =>
    'content' in row && Array.isArray(row.content)
      ? row.content.filter((cell: Block | Inline | Text) => (cell as Block).nodeType === 'table-cell')
      : [],
  );

  // Transpose the table: columns[i] = [cell from row 0 col i, row 1 col i, ...]
  const numCols = headerLabels.length;
  const columns = Array.from({ length: numCols }, (_, colIdx) => {
    const colCells = bodyCells.map((rowCells) => rowCells[colIdx]);
    return colCells;
  });

  const mobileHeaderLabels = headerCells.map((cell: Block | Inline | Text) =>
    documentToReactComponents(
      {
        nodeType: 'document',
        data: {},
        content: [cell as Block | Inline | Text],
      } as Document,
      {
        ...options,
        renderNode: {
          ...options.renderNode,
          [BLOCKS.TABLE_HEADER_CELL]: (node, children) => <span className="text-left font-semibold">{children}</span>,
          [BLOCKS.TABLE_CELL]: (node, children) => <span className="text-left">{children}</span>,
        },
      },
    ),
  );

  return (
    <>
      {/* Desktop Table */}
      <table className="hidden w-full md:table md:w-full md:table-fixed">
        <tbody>{children}</tbody>
      </table>
      {/* Mobile Column Cards */}
      <div className="flex flex-col gap-4 md:hidden">
        {mobileHeaderLabels.map((header, colIdx) => (
          <div key={colIdx} className="border border-basalt bg-transparent p-0">
            <div className="w-full bg-basalt px-4 py-3 text-left text-xl font-semibold text-sand">{header}</div>
            <div className="flex flex-col">
              {columns[colIdx].map((cell, rowIdx) => {
                // Use the new utility to check if the cell is empty or just '-'
                const cellText = extractTextFromContentfulNode(cell).trim();
                if (!cellText || cellText === '-') {
                  return null;
                }
                const cellContent =
                  cell &&
                  documentToReactComponents(
                    {
                      nodeType: 'document',
                      data: {},
                      content: [cell as Block | Inline | Text],
                    } as Document,
                    {
                      ...options,
                      renderNode: {
                        ...options.renderNode,
                        [BLOCKS.TABLE_HEADER_CELL]: (node, children) => (
                          <div className="text-left font-semibold">{children}</div>
                        ),
                        [BLOCKS.TABLE_CELL]: (node, children) => <div className="text-left">{children}</div>,
                      },
                    },
                  );
                return (
                  <div key={rowIdx} className="px-4 py-2 text-left">
                    {cellContent}
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
