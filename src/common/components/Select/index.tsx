import type { ForwardedRef, KeyboardEvent } from 'react';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';

import ErrorMessage from '@/components/ErrorMessage';
import FormLabel from '@/components/FormLabel';
import { ArrowDownSm, ArrowUpSm, ErrorSm, InfoSm, SuccessSm } from '@/components/Icons/sm';
import Tooltip, { TooltipContent, TooltipTrigger } from '@/components/Tooltip';

import { useOutsideClick } from '@/hooks/useOutsideClick';

import type { InputProps } from '@/types/inputProps';
import type { SelectOption } from '@/types/selectOption';
import type { Tooltip as TooltipType } from '@/types/tooltip';

import { cn } from '@/utils/cn';

import { itemSelectionInputStyles, itemSelectionStyles, itemStyles } from './index.styles';

type SelectProps = {
  value?: string | string[];
  options: SelectOption[];
  multiple?: boolean;
  maxSelectable?: number;
  tooltip?: TooltipType;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLDivElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
} & InputProps;

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(function Select(
  {
    id = 'dropdown',
    label,
    value,
    defaultValue,
    placeholder = 'Bitte auswählen',
    options,
    labelHidden = false,
    multiple = false,
    maxSelectable,
    valid,
    invalid,
    disabled,
    invalidText = '',
    required = false,
    hotjarSuppress = false,
    className,
    tooltip,
    onClick,
    onChange,
    onFocus,
    onKeyUp,
  },
  ref: ForwardedRef<HTMLSelectElement | null>,
) {
  const containerRef = useRef<HTMLDivElement>(null);
  const selectRef = useRef<HTMLSelectElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<(HTMLLIElement | null)[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<SelectOption[]>([]); // Only used to have reactivity on the fake select component
  const itemSelectionInputClass = itemSelectionInputStyles({ isError: !!invalid, disabled: !!disabled });

  useOutsideClick(containerRef, handleOnClickOutside);
  useImperativeHandle(ref, () => selectRef.current!, []);

  function selectOption(selectedOption: SelectOption) {
    if (selectRef.current) {
      if (multiple) {
        // Find the option in the select element
        const optionElement = Array.from(selectRef.current.options).find(
          (option) => selectedOption.value === option.value,
        );

        if (optionElement) {
          // Check if the maxSelectable limit is reached
          if (
            maxSelectable &&
            selectedOptions.length >= maxSelectable &&
            !selectedOptions.some((so) => so.value === selectedOption.value)
          ) {
            return; // Prevent further selections if limit is reached and the option is not already selected
          }

          // Toggle the selection state
          optionElement.selected = !optionElement.selected;

          // Only update selectedOptions directly for uncontrolled components
          if (!value) {
            const newSelectedOptions = selectedOptions.some((so) => so.value === selectedOption.value)
              ? selectedOptions.filter((option) => option.value !== selectedOption.value)
              : [...selectedOptions, selectedOption];

            setSelectedOptions(newSelectedOptions);
          }
        }
      } else {
        // If not multiple, simply set the value
        selectRef.current.value = selectedOption.value;
        // Update the display state only for uncontrolled components
        if (!value) {
          setSelectedOptions([selectedOption]);
        }
        setIsOpen(false);
        inputRef.current?.focus();
      }

      // Dispatch the change event
      selectRef.current.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  function handleOnClickOutside() {
    setIsOpen(false);
  }

  function handleOnClick(event: React.MouseEvent<HTMLDivElement>) {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (onClick) onClick(event);
  }

  function handleOpenOnKeyDown(event: KeyboardEvent) {
    if (disabled) return;
    switch (event.key) {
      case ' ':
        event.preventDefault();
        setIsOpen(!isOpen);
        setTimeout(() => moveFocus(0, 0), 100);
        break;
      case 'ArrowDown':
        event.preventDefault();
        setIsOpen(true);
        setTimeout(() => moveFocus(0, 0), 100);
        break;
      case 'ArrowUp':
        event.preventDefault();
        setIsOpen(true);
        setTimeout(() => moveFocus(options.length, -1), 100);
        break;
      default:
        break;
    }
  }

  function handleSelectOnKeyDown(event: KeyboardEvent, option: SelectOption, index: number) {
    switch (event.key) {
      case ' ':
        event.preventDefault();
        selectOption(option);
        break;
      case 'Enter':
        event.preventDefault();
        selectOption(option);
        if (multiple) {
          setIsOpen(false);
          inputRef.current?.focus();
        }
        break;
      case 'ArrowDown':
        event.preventDefault();
        moveFocus(index, 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        moveFocus(index, -1);
        break;
      case 'Escape':
        setIsOpen(false);
        inputRef.current?.focus();
        break;
      default:
        break;
    }
  }

  function moveFocus(currentIndex: number, offset: number) {
    const newIndex = currentIndex + offset;
    if (newIndex >= 0 && newIndex < options.length) {
      optionRefs.current[newIndex]?.focus();
    }
  }

  // Set the ref with a proper function type
  const setOptionRef =
    (index: number) =>
    (el: HTMLLIElement | null): void => {
      optionRefs.current[index] = el;
    };

  useEffect(() => {
    if (value) {
      let selectedOptions: SelectOption[] = [];
      if (Array.isArray(value)) {
        selectedOptions = options.filter((option) => value.includes(option.value));
      } else {
        selectedOptions = options.filter((option) => option.value === value);
      }
      setSelectedOptions(selectedOptions);
    } else {
      setSelectedOptions([]);
    }
  }, [options, value]);

  useEffect(() => {
    // Only handle defaultValue for uncontrolled components
    if (!value) {
      if (defaultValue) {
        let selectedOptions: SelectOption[] = [];
        if (Array.isArray(defaultValue)) {
          selectedOptions = options.filter((option) => defaultValue.includes(option.value));
        } else {
          selectedOptions = options.filter((option) => option.value === defaultValue);
        }
        setSelectedOptions(selectedOptions);
      } else {
        setSelectedOptions([]);
      }
    }
  }, [options, defaultValue, value]);

  return (
    <div ref={containerRef} className={cn('relative space-y-1', className)} onKeyUp={onKeyUp}>
      <div className="flex items-center gap-1">
        <FormLabel htmlFor={id} disabled={disabled} required={required} hidden={labelHidden}>
          {label}
        </FormLabel>

        {tooltip && (
          <Tooltip>
            <TooltipTrigger>
              <InfoSm width={16} height={16} />
              <span className="sr-only">Hinweis</span>
            </TooltipTrigger>
            <TooltipContent className="w-max max-w-[200px] border border-basalt bg-sand px-4 py-4 text-basalt">
              <div className="text-sm font-bold">{tooltip.title}</div>
              <p className="text-sm">{tooltip.text}</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      <div className="relative">
        <div
          ref={inputRef}
          tabIndex={disabled ? undefined : 0} // skip focus when tab
          onClick={handleOnClick}
          onFocus={onFocus}
          onKeyDown={(event) => handleOpenOnKeyDown(event)}
          className={itemSelectionInputClass}
        >
          <span className="truncate">
            {selectedOptions.length > 0 ? selectedOptions.map((option) => option.label).join(', ') : placeholder}
          </span>
          <span className="flex">
            {invalid && (
              <span className="mr-3">
                <ErrorSm />
              </span>
            )}
            {valid && (
              <span className="mr-3">
                <SuccessSm />
              </span>
            )}
            {isOpen ? <ArrowUpSm /> : <ArrowDownSm />}
          </span>
        </div>
        <ul className={itemSelectionStyles({ isOpen })}>
          {options.map((option, index) => {
            const selected = selectedOptions.some((selectedOption) => selectedOption.value === option.value);
            const disabledOption: boolean | null | undefined =
              maxSelectable && selectedOptions.length >= maxSelectable && !selected ? true : undefined;

            return (
              <li
                ref={setOptionRef(index)}
                tabIndex={0}
                onClick={() => selectOption(option)}
                onKeyDown={(event) => handleSelectOnKeyDown(event, option, index)}
                className={itemStyles({
                  selected,
                  multiple,
                  disabled: disabled || disabledOption,
                })}
                key={index}
                {...(hotjarSuppress ? { 'data-hj-suppress': '' } : {})}
              >
                {option.label}
              </li>
            );
          })}
        </ul>
      </div>
      <ErrorMessage id={`${id}-error`} message={invalidText} />

      <select
        ref={selectRef}
        value={value ? value : multiple ? [] : ''}
        onChange={onChange}
        name={id}
        id={id}
        className="hidden"
        multiple={multiple}
        aria-describedby={`${id}-error`}
        aria-invalid={!!invalid}
        {...(required && { 'aria-required': 'true' })}
      >
        {!multiple && <option value={''}>{placeholder}</option>}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
});

export default Select;
