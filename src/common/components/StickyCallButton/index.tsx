import React, { useEffect, useRef, useState } from 'react';

import {
  BusinessArea,
  CLOSE_BUTTON_LABEL,
  DGP_CAMPAIGN_PAGE_HOTLINE,
  FALLBACK_DGP_PROFESSIONAL_PAGE_HOTLINE,
} from '@/app/consts';
import type { BusinessArea as BusinessAreaType } from '@/app/consts';

import Headline from '@/components/Headline';
import { Hotline } from '@/components/HotlineList';
import { CallBackMd } from '@/components/Icons/md';
import { CloseSm } from '@/components/Icons/sm';
import TrackableLink from '@/components/TrackableLink';

import { isOutsideOfWorkingHours } from '@/hooks/isOutsideOfWorkingHours';
import useTracking from '@/hooks/useTracking';

import { useHotlineListStore } from '@/stores/HotlineListStore';

import { normalizePhone } from '@/utils/normalizePhone';

import {
  popoverCloseButtonStyles,
  popoverContentStyles,
  popoverStyles,
  stickyButtonContainerStyles,
  stickyButtonContentStyles,
  stickyButtonStyles,
} from './index.style';

type StickyCallButtonProps = {
  businessArea?: BusinessAreaType;
  pageType?: string;
};

export default function StickyCallButton({ businessArea, pageType }: StickyCallButtonProps) {
  const { trackEvent } = useTracking();
  const [isOpen, setIsOpen] = useState(false);
  const hotlineList = useHotlineListStore.use.hotlineList();
  const popoverRef = useRef<HTMLDivElement>(null);

  // Check if this is a Professional business area
  const isProfessionalArea = businessArea === BusinessArea.Professional;

  useEffect(() => {
    const popover = popoverRef.current;
    if (!popover) return;

    const handleToggle = () => setIsOpen(popover.matches(':popover-open'));

    // Set state when popover is toggled by button click or outside click (e.g. by clicking outside or open another dialog)
    popover.addEventListener('toggle', handleToggle);
    return () => popover.removeEventListener('toggle', handleToggle);
  }, []);

  // Don't render if not Professional area and outside working hours
  if (!isProfessionalArea && isOutsideOfWorkingHours(hotlineList?.workingHours)) {
    return null;
  }

  // Get the appropriate phone number for Professional area
  function getProfessionalHotline() {
    if (pageType === 'Aktion' || pageType === 'Campaign') {
      return DGP_CAMPAIGN_PAGE_HOTLINE;
    }
    return FALLBACK_DGP_PROFESSIONAL_PAGE_HOTLINE;
  }

  function handleClick(event: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    trackEvent({
      category: 'Button',
      action: 'click',
      element: event.currentTarget,
      content: !isOpen ? 'open' : 'close',
    });
  }

  return (
    <>
      <div data-tracking-module="sticky-call-button" className={stickyButtonContainerStyles()}>
        <button
          type="button"
          className={stickyButtonStyles()}
          {...{ popovertarget: 'sticky-button-popover' }}
          onClick={handleClick}
        >
          <span className={stickyButtonContentStyles()}>
            {isOpen ? (
              <>
                <CloseSm className="cursor-pointer" />
                <span className="sr-only">Kontaktinformation ausblenden</span>
              </>
            ) : (
              <>
                <CallBackMd className="cursor-pointer" />
                <span className="sr-only">Kontaktinformation einblenden</span>
              </>
            )}
          </span>
        </button>
      </div>
      <div
        id="sticky-button-popover"
        data-tracking-module="sticky-call-button-popup"
        ref={popoverRef}
        popover="auto"
        className={popoverStyles()}
      >
        <div className={popoverContentStyles()}>
          <button
            type="button"
            className={popoverCloseButtonStyles()}
            {...{ popovertarget: 'sticky-button-popover' }}
            onClick={handleClick}
          >
            {CLOSE_BUTTON_LABEL} <CloseSm />
          </button>
          <Headline intent={'h4'} type={'p'}>
            Wir sind für Sie da –<br />
            Unsere Berater helfen gerne!
          </Headline>
          <p className="text-base">
            Welcher Glasfaser-Tarif passt zu mir?
            <br />
            Wir beraten Sie! Unsere geschulten Mitarbeiter helfen Ihnen unverbindlich dabei, den passenden Tarif für Sie
            zu finden.
          </p>
          <p>Jetzt Kontakt aufnehmen!</p>
          <div>
            <div className="text-[28px] font-semibold lg:text-[38px]">
              {isProfessionalArea ? (
                <TrackableLink
                  tabIndex={0}
                  className={'text-inherit underline underline-offset-4'}
                  href={`tel:${normalizePhone(getProfessionalHotline())}`}
                >
                  {getProfessionalHotline()}
                </TrackableLink>
              ) : (
                <Hotline />
              )}
            </div>
            <p className="mt-2 text-sm">
              Erreichbarkeit:{' '}
              <abbr title="Montag" className="no-underline">
                Mo.
              </abbr>
              -
              <abbr title="Freitag" className="no-underline">
                Fr.
              </abbr>
              : {isProfessionalArea ? '08:00 - 18:00' : hotlineList?.workingHours?.workdays} Uhr
              {!isProfessionalArea && (
                <>
                  <br />
                  <abbr title="Samstag" className="no-underline">
                    Sa.
                  </abbr>
                  : {hotlineList?.workingHours?.saturday} Uhr
                  <br />
                  Sonn- und Feiertage: {hotlineList?.workingHours?.sundayBankHolidays || 'geschlossen'}
                </>
              )}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
