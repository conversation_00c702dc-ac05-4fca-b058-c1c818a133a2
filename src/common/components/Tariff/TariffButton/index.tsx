'use client';

import React, { useEffect, useState } from 'react';

import { AC_BUTTON_LABEL } from '@/app/consts';

import Button from '@/components/Button';
import { ArrowRightSm } from '@/components/Icons/sm';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';

import type { CTA } from '@/types/cta';

type TariffButtonProps = {
  cta: CTA;
  fullWidth?: boolean;
  isAcPositive: boolean;
  variant?: 'primary' | 'highlight';
  additionalProductIds?: string[];
  trackingId?: string;
  className?: string;
  retargeting?: boolean;
};

export default function TariffButton({
  cta,
  fullWidth,
  isAcPositive,
  variant = 'primary',
  additionalProductIds = [],
  trackingId,
  className,
  retargeting,
}: TariffButtonProps) {
  const { trackAcEvent } = useTracking();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const setAcType = useAvailabilityCheckStore.use.setAcType();
  const [additionalProductParams, setAdditionalProductParams] = useState('');
  const retargetingParams = retargeting ? '&retargeting=true' : '';

  useEffect(() => {
    if (additionalProductIds.length > 0) {
      const query = additionalProductIds.map((id) => `&additionalProduct=${id}:1`).join('');
      setAdditionalProductParams(query);
    }
  }, [additionalProductIds]);

  function onAvailabilityCheckClick() {
    setAcModalOpen(true);
    setAcType('button');
    trackAcEvent({ acType: 'button', acInteraction: 'open' });
  }

  const buttonProps = {
    variant,
    fullWidth,
    icon: <ArrowRightSm />,
    label: isAcPositive ? cta.label : AC_BUTTON_LABEL,
    onClick: isAcPositive ? undefined : onAvailabilityCheckClick,
  };

  return (
    <div className={className} data-tracking-id={trackingId}>
      <Button
        {...buttonProps}
        href={isAcPositive ? `${cta.to}${additionalProductParams}${retargetingParams}` : undefined}
      />
    </div>
  );
}
