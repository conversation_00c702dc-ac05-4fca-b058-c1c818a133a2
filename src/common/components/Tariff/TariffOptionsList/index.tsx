import React from 'react';

import type { TariffTeaserVariant } from '@/modules/TariffOverview/TariffTable/TariffTeaser';

import type { TariffOption } from '@/apis/dg/types';

import Copy from '@/components/Copy';
import { CheckedSm, CloseSm, InfoSm, OptionSm } from '@/components/Icons/sm';
import Tooltip, { TooltipContent, TooltipTrigger } from '@/components/Tooltip';

import { optionListStyles } from './index.styles';

type TarifOptionsListProps = {
  tariffOptions: TariffOption[];
  variant?: TariffTeaserVariant;
  trackingId?: string;
  className?: string;
  showBorders?: boolean;
};

export default function TariffOptionsList({
  tariffOptions,
  variant = 'default',
  trackingId,
  className,
  showBorders = false,
}: TarifOptionsListProps) {
  return (
    <ul className={className} data-tracking-id={trackingId}>
      {tariffOptions.map((tariffOption, index) => {
        const { title, text, legalNote, optionType } = tariffOption;

        return (
          <li className={optionListStyles({ variant, showBorders })} key={index}>
            <span>{optionType === 1 ? <CheckedSm /> : optionType === 2 ? <CloseSm /> : <OptionSm />}</span>
            <div className={'text-inherit'}>
              <Copy className="flex items-start gap-1">
                <span>{title}</span>
                {legalNote && (
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoSm width={16} height={16} />
                      <span className="sr-only">Hinweis</span>
                    </TooltipTrigger>
                    <TooltipContent className="w-max max-w-[240px] border border-basalt bg-white px-4 py-4 text-basalt">
                      <Copy size={'sm'} weight={'bold'}>
                        {legalNote.title}
                      </Copy>
                      <Copy size={'sm'}>{legalNote.text}</Copy>
                    </TooltipContent>
                  </Tooltip>
                )}
              </Copy>
              {text && <Copy className="mt-1">{text}</Copy>}
            </div>
          </li>
        );
      })}
    </ul>
  );
}
