import { cva } from 'class-variance-authority';

import { TextAlignment } from '@/types/text';
import { Theme } from '@/types/theme';

export const teaserStyles = cva('relative flex flex-col', {
  variants: {
    theme: {
      [Theme.Sand]: 'bg-sand text-basalt',
      [Theme.Basalt]: 'theme-basalt group bg-basalt text-sand',
      [Theme.Gray]: 'bg-gray-50 text-basalt',
    },
    hasInnerPadding: {
      true: '',
    },
    variant: {
      icon: 'gap-y-8',
      image: '',
      text: '',
    },
  },
  compoundVariants: [
    {
      variant: 'icon',
      hasInnerPadding: true,
      class: 'px-4 py-8',
    },
    {
      variant: 'icon',
      hasInnerPadding: false,
      class: 'px-4 py-8 lg:p-0',
    },
    {
      variant: 'text',
      hasInnerPadding: true,
      class: 'px-4 py-8',
    },
    {
      variant: 'text',
      hasInnerPadding: false,
      class: 'px-4 lg:px-0',
    },
  ],
});

export const teaserContentStyles = cva('box-border flex w-full grow flex-col gap-y-2 self-center text-base leading-6', {
  variants: {
    hoverText: {
      true: 'md:absolute md:bottom-0',
    },
    hasInnerPadding: {
      true: '',
    },
    textAlignment: {
      [TextAlignment.Left]: 'items-start text-left',
      [TextAlignment.Center]: 'items-center text-center',
      [TextAlignment.Right]: 'items-end text-right',
    },
    variant: {
      icon: '',
      image: '',
      text: '',
    },
  },
  compoundVariants: [
    {
      variant: 'image',
      hasInnerPadding: true,
      class: 'bg-gray-50 px-4 pt-4 pb-8',
    },
  ],
});

export const teaserHeadlineStyles = cva('text-inherit');

export const teaserTextStyles = cva('grow text-inherit lg:line-clamp-none', {
  variants: {
    lineClampCount: {
      3: 'mb-2 line-clamp-3',
      4: 'mb-2 line-clamp-4',
      5: 'mb-2 line-clamp-5',
      7: 'mb-2 line-clamp-7',
      10: 'mb-2 line-clamp-10',
      none: 'mb-0 line-clamp-none',
    },
  },
});

export const teaserImageStyles = cva('order-first', {
  variants: {
    ratio: {
      '3/2': 'aspect-3/2',
      '21/9': 'aspect-21/9',
    },
    variant: {
      icon: 'h-[75px] w-[75px] place-self-center',
      image: 'self-start object-cover',
      text: '',
    },
  },
});

export const teaserPhoneStyles = cva(
  'mb-2 text-xl leading-[120%] font-semibold text-basalt underline hover:text-hover md:text-[1.75rem]',
);
