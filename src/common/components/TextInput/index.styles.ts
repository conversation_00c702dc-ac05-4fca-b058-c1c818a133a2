import { cva } from 'class-variance-authority';

export const inputWrapperStyles = cva('relative space-y-1', {
  variants: {
    disabled: {
      true: 'cursor-not-allowed',
    },
  },
});
export const fieldOuterWrapperStyles = cva('relative');
export const fieldWrapperStyles = cva('relative flex w-full');
export const iconStyles = cva('absolute top-1/2 right-4 -translate-y-1/2', {
  variants: {
    invalid: {
      true: 'fill-error',
    },
    clickable: {
      true: 'cursor-pointer',
    },
  },
});
export const inputStyles = cva(
  'max-h-12 w-full border border-gray-800 bg-white px-4 leading-[48px] text-basalt -outline-offset-2 group-[.theme-basalt]:-outline-offset-4 focus:group-[.theme-basalt]:outline-offset-2',
  {
    variants: {
      variant: {
        rounded: 'rounded-full pr-10 placeholder:text-basalt',
        square: 'placeholder-opacity-60 rounded-none',
      },
      invalid: {
        true: 'pr-10 outline-error',
      },
      disabled: {
        true: 'cursor-not-allowed bg-gray-100 text-gray-600',
      },
    },
  },
);
