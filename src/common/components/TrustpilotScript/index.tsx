'use client';

import { useEffect } from 'react';

import { cva } from 'class-variance-authority';

import { UsercentricsServiceId } from '@/app/consts';

import useTracking from '@/hooks/useTracking';
import { useUsercentricsConsent } from '@/hooks/useUsercentricsConsent';

import type { TrackingElement } from '@/types/tracking';

import { cn } from '@/utils/cn';
import { isDevEnv } from '@/utils/isDevEnv';

import UsercentricsPlaceholder from '../UsercentricsPlaceholder';

const TRUSTPILOT_MINI_TEMPLATE_ID = '53aa8807dec7e10d38f59f32';
const TRUSTPILOT_CAROUSEL_TEMPLATE_ID = '53aa8912dec7e10d38f59f36';
const TRUSTPILOT_SCRIPT_ID = 'trustpilot-widget-script';

const miniTrustpilotStyles = cva(
  'flex items-center justify-center border-b py-6 text-sand lg:justify-self-start lg:border-none lg:py-0',
);

export type TrustpilotScriptProps = {
  type?: 'mini' | 'carousel';
};

/**
 * "mini" Trustpilot Widget is used only in Footer.
 * "carousel" Trustpilot Widget is used in the Placeholder module and is default.
 */
export default function TrustpilotScript({ type = 'carousel' }: TrustpilotScriptProps) {
  const consent = useUsercentricsConsent(UsercentricsServiceId.TRUSTPILOT);
  const isCarousel = type === 'carousel';
  const TRUSTPILOT_BUSINESSUNIT_ID = process.env.NEXT_PUBLIC_TRUSTPILOT_BUSINESSUNIT_ID;
  const { trackEvent } = useTracking();

  useEffect(() => {
    if (!TRUSTPILOT_BUSINESSUNIT_ID || consent !== 'accepted' || typeof document === 'undefined') {
      // Clean up when consent is withdrawn
      const existingScript = document.getElementById(TRUSTPILOT_SCRIPT_ID);
      if (existingScript) {
        document.head.removeChild(existingScript);
      }

      // Remove any existing Trustpilot widgets and global object
      const existingTrustboxes = document.querySelectorAll('.trustpilot-widget');
      existingTrustboxes.forEach((box) => {
        box.innerHTML = '';
      });
      delete window.Trustpilot;
      return;
    }

    // Create and load script
    const script = document.createElement('script');
    script.src = '//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js';
    script.async = true;
    script.id = TRUSTPILOT_SCRIPT_ID;
    script.type = 'text/javascript';

    script.onload = () => {
      const trustbox = document.getElementById('trustbox');
      if (trustbox && window.Trustpilot) {
        window.Trustpilot.loadFromElement(trustbox, true);
      }
    };

    // Only add script if it doesn't exist
    const existingScript = document.getElementById(TRUSTPILOT_SCRIPT_ID);
    if (!existingScript) {
      document.head.appendChild(script);
    } else if (window.Trustpilot) {
      // If script exists and Trustpilot is loaded, initialize widget
      const trustbox = document.getElementById('trustbox');
      if (trustbox) {
        window.Trustpilot.loadFromElement(trustbox, true);
      }
    }

    return () => {
      const script = document.getElementById(TRUSTPILOT_SCRIPT_ID);
      if (script) {
        document.head.removeChild(script);
      }

      // Clean up widgets and global object on unmount
      const existingTrustboxes = document.querySelectorAll('.trustpilot-widget');
      existingTrustboxes.forEach((box) => {
        box.innerHTML = '';
      });
      delete window.Trustpilot;
    };
  }, [TRUSTPILOT_BUSINESSUNIT_ID, consent]);

  if (!TRUSTPILOT_BUSINESSUNIT_ID) {
    if (isDevEnv) {
      console.info('TRUSTPILOT_BUSINESSUNIT_ID is not set');
    }
    return null;
  }

  if (consent !== 'accepted') {
    return (
      <div className={!isCarousel ? 'flex-auto' : ''}>
        <UsercentricsPlaceholder serviceId={UsercentricsServiceId.TRUSTPILOT} isCarousel={isCarousel} />
      </div>
    );
  }

  // We cannot track clicks on the widget directly, so we use a link positioned absolutely above it for tracking. This does not work for the Carousel though.
  function TrustpilotMiniTrackableLink() {
    return (
      <a
        href="https://de.trustpilot.com/review/www.deutsche-glasfaser.de?utm_medium=trustbox&utm_source=Mini"
        target="_blank"
        rel="noopener"
        className={cn(
          'absolute inset-0 z-float h-full w-full',
          isCarousel ? 'max-h-[180px] translate-y-1/2 lg:max-h-[200px]' : '',
          !isCarousel && miniTrustpilotStyles(),
        )}
        onClick={onClick}
      >
        <span className="sr-only">Trustpilot</span>
      </a>
    );
  }

  function onClick(event: React.MouseEvent<HTMLAnchorElement>) {
    trackEvent({
      category: 'Link',
      action: 'click',
      element: event.currentTarget as TrackingElement,
      content: 'trustpilot',
    });
  }

  return (
    <div className={!isCarousel ? 'relative flex-auto' : 'relative'}>
      {!isCarousel && <TrustpilotMiniTrackableLink />}
      <div
        id="trustbox"
        className={cn('trustpilot-widget', !isCarousel && miniTrustpilotStyles())}
        data-locale="de-DE"
        data-template-id={isCarousel ? TRUSTPILOT_CAROUSEL_TEMPLATE_ID : TRUSTPILOT_MINI_TEMPLATE_ID}
        data-businessunit-id={TRUSTPILOT_BUSINESSUNIT_ID}
        data-style-height={isCarousel ? '180px' : '150px'}
        data-style-width="100%"
        data-tags={isCarousel ? 'SelectedReview' : undefined}
        data-review-languages="de"
        data-theme={isCarousel ? undefined : 'dark'}
      >
        <a href="https://de.trustpilot.com/review/www.deutsche-glasfaser.de" target="_blank" rel="noopener">
          Trustpilot
        </a>
      </div>
    </div>
  );
}
