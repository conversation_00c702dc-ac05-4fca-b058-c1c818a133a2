/**
 * Integration Tests for YoutubeVideo Component
 *
 * This test suite verifies the functionality of the YoutubeVideo component which provides
 * a YouTube video player with Usercentrics consent management and tracking capabilities.
 *
 * Key areas tested:
 * 1. Rendering behavior based on user consent
 *    - Shows YouTube player when consent is granted
 *    - Shows Usercentrics placeholder when consent is denied
 *
 * 2. Video metadata handling
 *    - Fetches video information from YouTube oembed API
 *    - Properly displays metadata in schema.org format
 *
 * 3. Event tracking integration
 *    - Tracks video load events
 *    - Tracks play/start events with mute state
 *    - Tracks progress at 10% intervals
 *    - Tracks video completion
 *
 * 4. Component customization
 *    - Supports custom className for styling
 */
import { act, render, screen, waitFor } from '@testing-library/react';

import useTracking from '@/hooks/useTracking';
import { useUsercentricsConsent } from '@/hooks/useUsercentricsConsent';

import YoutubeVideo from '../index';

// Mock UsercentricsPlaceholder component
jest.mock('@/components/UsercentricsPlaceholder', () => ({
  __esModule: true,
  default: jest
    .fn()
    .mockImplementation(() => <div data-testid="usercentrics-placeholder">Usercentrics Placeholder</div>),
}));

// Mock the hooks
jest.mock('@/hooks/useUsercentricsConsent', () => ({
  useUsercentricsConsent: jest.fn(),
  UsercentricsServiceId: {
    YOUTUBE: 'youtube',
  },
}));

jest.mock('@/hooks/useTracking', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock YouTube component
jest.mock('react-youtube', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(({ onReady, onPlay, onEnd, onStateChange, className }) => {
    // Store callbacks for testing
    (
      global as unknown as {
        youtubeCallbacks: {
          onReady: () => void;
          onPlay: () => void;
          onEnd: () => void;
          onStateChange: () => void;
        };
      }
    ).youtubeCallbacks = {
      onReady,
      onPlay,
      onEnd,
      onStateChange,
    };
    return (
      <div data-testid="youtube-player" className={className}>
        YouTube Player Mock
      </div>
    );
  }),
}));

// Mock fetch for metadata
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('YoutubeVideo', () => {
  const mockVideoId = 'test-video-id';
  const mockTrackYoutubeEvent = jest.fn();
  const mockMetadata = {
    title: 'Test Video',
    thumbnail_url: 'https://example.com/thumbnail.jpg',
    upload_date: '2024-02-21',
    duration: 'PT2M30S',
    html: '<iframe>test</iframe>',
    view_count: '1000',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useUsercentricsConsent as jest.Mock).mockReturnValue('accepted');
    (useTracking as jest.Mock).mockReturnValue({ trackYoutubeEvent: mockTrackYoutubeEvent });
    mockFetch.mockResolvedValue({
      json: () => Promise.resolve(mockMetadata),
    });
  });

  it('renders YouTube player when consent is accepted', async () => {
    await act(async () => {
      render(<YoutubeVideo videoId={mockVideoId} />);
    });
    expect(screen.getByTestId('youtube-player')).toBeInTheDocument();
  });

  it('renders UsercentricsPlaceholder when consent is not accepted', async () => {
    (useUsercentricsConsent as jest.Mock).mockReturnValue('denied');
    await act(async () => {
      render(<YoutubeVideo videoId={mockVideoId} />);
    });
    expect(screen.queryByTestId('youtube-player')).not.toBeInTheDocument();
    expect(screen.getByTestId('usercentrics-placeholder')).toBeInTheDocument();
  });

  it('applies custom className when provided', async () => {
    const customClass = 'custom-class';
    await act(async () => {
      render(<YoutubeVideo videoId={mockVideoId} className={customClass} />);
    });
    expect(screen.getByTestId('youtube-player')).toHaveClass(customClass);
  });

  it('fetches and displays video metadata', async () => {
    await act(async () => {
      render(<YoutubeVideo videoId={mockVideoId} />);
    });

    await act(async () => {
      // Wait for the fetch to be called
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${mockVideoId}&format=json`,
        );
      });

      // Wait for the promise to resolve
      await mockFetch.mock.results[0].value;
    });

    // Wait for the schema data to be updated
    await waitFor(() => {
      const schemaScript = document.querySelector('script[type="application/ld+json"]');
      expect(schemaScript).not.toBeNull();
      const schemaData = JSON.parse(schemaScript?.innerHTML || '{}');

      expect(schemaData).toMatchObject({
        '@context': 'https://schema.org',
        '@type': 'VideoObject',
        name: mockMetadata.title,
        thumbnailUrl: mockMetadata.thumbnail_url,
      });
    });
  });

  describe('YouTube event tracking', () => {
    it('tracks video load event', async () => {
      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      await act(async () => {
        (global as any).youtubeCallbacks.onReady();
      });

      expect(mockTrackYoutubeEvent).toHaveBeenCalledWith({
        videoId: mockVideoId,
        eventAction: 'load',
      });
    });

    it('tracks video start event', async () => {
      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 0,
          },
        });
      });

      expect(mockTrackYoutubeEvent).toHaveBeenCalledWith({
        videoId: mockVideoId,
        eventAction: 'start',
        videoPercent: 0,
        muteState: 'unmuted',
      });
    });

    it('prevents double-triggering of start event', async () => {
      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      // First play at beginning - should trigger start
      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 0,
          },
        });
      });

      // Second play at beginning - should NOT trigger start because hasStarted is true
      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 0,
          },
        });
      });

      // Play from middle - should NOT trigger start
      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 50,
          },
        });
      });

      // Verify start event was only called once
      expect(mockTrackYoutubeEvent).toHaveBeenCalledTimes(1);
      expect(mockTrackYoutubeEvent).toHaveBeenCalledWith({
        videoId: mockVideoId,
        eventAction: 'start',
        videoPercent: 0,
        muteState: 'unmuted',
      });
    });

    it('allows start event after video ends', async () => {
      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      // First play at beginning
      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 0,
          },
        });
      });

      // End the video
      await act(async () => {
        (global as any).youtubeCallbacks.onEnd({
          target: {
            isMuted: () => false,
          },
        });
      });

      // Play again from beginning - should trigger start again
      await act(async () => {
        (global as any).youtubeCallbacks.onPlay({
          target: {
            isMuted: () => false,
            getCurrentTime: () => 0,
          },
        });
      });

      // Verify start event was called twice (once for each play from beginning)
      const startEvents = mockTrackYoutubeEvent.mock.calls.filter((call) => call[0].eventAction === 'start');
      expect(startEvents).toHaveLength(2);
    });

    it('tracks video end event', async () => {
      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      await act(async () => {
        (global as any).youtubeCallbacks.onEnd({
          target: {
            isMuted: () => true,
          },
        });
      });

      expect(mockTrackYoutubeEvent).toHaveBeenCalledWith({
        videoId: mockVideoId,
        eventAction: 'end',
        videoPercent: 100,
        muteState: 'muted',
      });
    });

    it('tracks video progress', async () => {
      jest.useFakeTimers();

      await act(async () => {
        render(<YoutubeVideo videoId={mockVideoId} />);
      });

      const mockEvent = {
        data: 1, // Playing state
        target: {
          getCurrentTime: () => 50, // 50 seconds
          getDuration: () => 100, // 100 seconds total
          isMuted: () => false,
        },
      };

      await act(async () => {
        (global as any).youtubeCallbacks.onStateChange(mockEvent);
        jest.advanceTimersByTime(1000);
      });

      expect(mockTrackYoutubeEvent).toHaveBeenCalledWith({
        videoId: mockVideoId,
        eventAction: 'progress',
        videoPercent: 50,
        muteState: 'unmuted',
      });

      jest.useRealTimers();
    });
  });
});
