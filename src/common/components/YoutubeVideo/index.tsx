'use client';

import { useEffect, useRef, useState } from 'react';

import YouTube from 'react-youtube';
import type { YouTubeEvent } from 'react-youtube';

import { UsercentricsServiceId } from '@/app/consts';

import UsercentricsPlaceholder from '@/components/UsercentricsPlaceholder';

import useTracking from '@/hooks/useTracking';
import { useUsercentricsConsent } from '@/hooks/useUsercentricsConsent';

import { cn } from '@/utils/cn';

type YoutubeVideoProps = {
  videoId: string;
  className?: string;
};

type Metadata = {
  name: string;
  description: string;
  thumbnailUrl: string;
  uploadDate: string;
  duration: string;
  embedUrl: string;
  interactionCount: string;
};

export default function YoutubeVideo({ videoId, className }: YoutubeVideoProps) {
  const consent = useUsercentricsConsent(UsercentricsServiceId.YOUTUBE);
  const { trackYoutubeEvent } = useTracking();
  const intervalTime = 1000;
  const [hasStarted, setHasStarted] = useState(false);
  const progressInterval = useRef<NodeJS.Timeout | null>(null);
  const progressSetRef = useRef(new Set<number>());
  const lastPositionRef = useRef<number>(0);
  const [metaData, setMetadata] = useState<Metadata>({
    name: '',
    description: '',
    thumbnailUrl: '',
    uploadDate: '',
    duration: '',
    embedUrl: '',
    interactionCount: '',
  });

  function onReady() {
    trackYoutubeEvent({
      videoId: videoId,
      eventAction: 'load',
    });
  }

  function onPlay(event: YouTubeEvent) {
    const muteState = event.target.isMuted() ? 'muted' : 'unmuted';
    const currentTime = event.target.getCurrentTime();

    // Only trigger start event if we're near the beginning and haven't started
    if (!hasStarted && currentTime < 1) {
      setHasStarted(true);
      trackYoutubeEvent({
        videoId: videoId,
        eventAction: 'start',
        videoPercent: 0,
        muteState,
      });
    }
  }

  function onEnd(event: YouTubeEvent) {
    const muteState = event.target.isMuted() ? 'muted' : 'unmuted';
    setHasStarted(false);
    clearInterval(progressInterval.current!);
    progressSetRef.current = new Set(); // Reset for next play

    trackYoutubeEvent({
      videoId: videoId,
      eventAction: 'end',
      videoPercent: 100,
      muteState,
    });
  }

  function onStateChange(event: YouTubeEvent) {
    const currentTime = event.target.getCurrentTime();

    // If we were not at the beginning before, but now we are, reset hasStarted
    if (lastPositionRef.current > 1 && currentTime < 1) {
      setHasStarted(false);
      progressSetRef.current = new Set(); // Reset progress tracking
    }

    // Update the last position
    lastPositionRef.current = currentTime;

    if (event.data === 1) {
      // When video is playing
      progressInterval.current = setInterval(() => {
        trackProgress(event, intervalTime);
      }, intervalTime);
    } else {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }

      // Only reset hasStarted when paused at the beginning
      if (event.data === 2 && currentTime < 0.1) {
        setHasStarted(false);
        progressSetRef.current = new Set(); // Reset progress tracking
      }
    }
  }

  function trackProgress(event: YouTubeEvent, intervalTime: number) {
    const muteState = event.target.isMuted() ? 'muted' : 'unmuted';
    const currentTime = event.target.getCurrentTime();
    const duration = event.target.getDuration();
    const currentPercent = Math.floor((currentTime / duration) * 100);

    // Find the nearest 10% mark we've passed
    const nearestTenth = Math.floor(currentPercent / 10) * 10;

    // Calculate how many percent video time are included within an interval
    const percentPerInterval = (100 / duration) * (intervalTime / 1000);

    // If we've passed a new 10% mark, haven't tracked it yet
    // and most likely have not skipped past the last 10% mark
    if (
      nearestTenth > 0 &&
      nearestTenth < 100 &&
      !progressSetRef.current.has(nearestTenth) &&
      currentPercent <= nearestTenth + percentPerInterval
    ) {
      progressSetRef.current.add(nearestTenth);
      trackYoutubeEvent({
        videoId: videoId,
        eventAction: 'progress',
        videoPercent: nearestTenth,
        muteState,
      });
    }
  }

  // Fetch video metadata
  useEffect(() => {
    if (consent !== 'accepted') return;

    fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`)
      .then((response) => response.json())
      .then((data) => {
        setMetadata({
          name: data.title,
          description: data.title,
          thumbnailUrl: data.thumbnail_url,
          uploadDate: data.upload_date,
          duration: data.duration,
          embedUrl: data.html,
          interactionCount: data.view_count,
        });
      });
  }, [consent, videoId]);

  if (consent !== 'accepted') {
    return <UsercentricsPlaceholder serviceId={UsercentricsServiceId.YOUTUBE} />;
  }

  return (
    <>
      <YouTube
        videoId={videoId}
        onReady={onReady}
        onPlay={onPlay}
        onEnd={onEnd}
        onStateChange={onStateChange}
        className={cn(
          'relative pt-[56.25%] lg:w-screen [&>iframe]:absolute [&>iframe]:top-0 [&>iframe]:left-0 [&>iframe]:h-full [&>iframe]:w-full',
          className,
        )}
      />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'VideoObject',
            name: metaData.name,
            description: metaData.description,
            thumbnailUrl: metaData.thumbnailUrl,
            uploadDate: metaData.uploadDate,
            duration: metaData.duration,
            embedUrl: metaData.embedUrl,
            interactionCount: metaData.interactionCount,
          }),
        }}
      />
    </>
  );
}
