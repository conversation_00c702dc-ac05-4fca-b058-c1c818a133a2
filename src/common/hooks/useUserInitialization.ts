import { useEffect } from 'react';

import type { BusinessArea } from '@/app/consts';

import { Provider } from '@/apis/dg/types';

import { useUserStore } from '@/stores/UserStore';

import { getHouseIdFromCookies } from '@/utils/cookies/houseId';
import { getLivingSituationFromCookies } from '@/utils/cookies/livingSituation';
import { getProviderFromCookies } from '@/utils/cookies/provider';
import { getUserAddressFromCookies } from '@/utils/cookies/userAddress';
import { getProvider } from '@/utils/getProvider';

export const useUserInitialization = (businessArea: BusinessArea) => {
  const {
    provider: stateProvider,
    houseId: stateHouseId,
    acAddress: stateAddress,
    livingSituation: stateLivingSituation,
    userISP,
    offerList,
    setProvider,
    setHouseId,
    setAcAddress,
    setLivingSituation,
    setUserISP,
    setOfferList,
    setLoading,
    setError,
    setTrackingEnabled,
    resetOfferList,
  } = useUserStore();

  useEffect(() => {
    const initializeUserState = async () => {
      setLoading(true);
      setError(null);

      try {
        // 1. Only load from cookies if the corresponding state value is undefined
        const provider = stateProvider ?? (stateProvider === undefined ? await getProviderFromCookies() : null);
        const houseId = stateHouseId ?? (stateHouseId === undefined ? await getHouseIdFromCookies() : null);
        const acAddress = stateAddress ?? (stateAddress === undefined ? await getUserAddressFromCookies() : null);
        const livingSituation =
          stateLivingSituation ?? (stateLivingSituation === undefined ? await getLivingSituationFromCookies() : null);

        // 2. Determine new provider based on business area
        const businessAreaProvider = getProvider(businessArea);
        const newProvider = businessAreaProvider || provider || Provider.Private;

        // 3. Detect true provider changes - only between DGH and DGP
        const providerChanged =
          provider !== undefined &&
          businessAreaProvider !== null &&
          provider !== businessAreaProvider &&
          provider !== null;

        // 4. Always update provider
        setProvider(newProvider);

        // 5. If provider changed, reset all user data except provider and finish early
        if (houseId && providerChanged) {
          resetOfferList();
          setLoading(false); // Set loading to false since no data fetching happens
          setTrackingEnabled(true);
          return;
        }

        // 6. Update state with loaded values
        if (houseId !== undefined && houseId !== null) setHouseId(houseId);
        if (acAddress) setAcAddress(acAddress);
        if (livingSituation) setLivingSituation(livingSituation);

        // 7. Fetch User ISP if needed
        if (!userISP) {
          try {
            const { getUserISP } = await import('@/apis/dg/isp');
            const ispData = await getUserISP();
            if (ispData) {
              setUserISP(ispData);
            }
          } catch (error) {
            console.error('Error fetching user ISP:', error);
            // Continue even if ISP fetch fails
          }
        }

        // 8. Fetch offer list if needed
        if (houseId && !offerList) {
          try {
            const { getOfferList } = await import('@/apis/dg/offerList');
            const data = await getOfferList({
              provider: newProvider,
              houseId,
            });

            if (!data) {
              throw new Error('Failed to fetch offer list or no data returned.');
            }

            setOfferList(data);
          } catch (error) {
            console.error('Error fetching offer list:', error);
            setError('An error occurred while fetching offer data');
          }
        }

        // 9. Complete initialization - set loading to false AFTER all state updates
        setLoading(false);
        setTrackingEnabled(true);
      } catch (err) {
        console.error('Error during user state initialization:', err);
        setError('An error occurred while initializing user data');
        setLoading(false);
        setTrackingEnabled(true);
      }
    };

    void initializeUserState();
  }, [businessArea]);
};
