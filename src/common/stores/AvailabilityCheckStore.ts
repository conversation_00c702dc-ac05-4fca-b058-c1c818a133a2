import { create } from 'zustand';

import type { CityResult, HouseNumberResult, StreetResult } from '@/stores/types/availabilityCheckStore';

import type { AcType } from '@/types/availabilityCheck';

import { createSelectors } from './utils/createSelectors';

type AvailabilityCheckStore = {
  acType: AcType;
  setAcType: (value: AcType) => void;
  cityInput: string;
  setCityInput: (value: string) => void;
  citySelected: CityResult | undefined;
  setCitySelected: (city: CityResult | undefined) => void;
  cityResults: CityResult[] | undefined;
  setCityResults: (cities: CityResult[] | undefined) => void;
  cityNotFound: boolean;
  setCityNotFound: (value: boolean) => void;
  streetInput: string;
  setStreetInput: (value: string) => void;
  streetSelected: StreetResult | undefined;
  setStreetSelected: (street: StreetResult | undefined) => void;
  streetResults: StreetResult[] | undefined;
  setStreetResults: (cities: StreetResult[] | undefined) => void;
  streetNotFound: boolean;
  setStreetNotFound: (value: boolean) => void;
  houseNumberInput: string;
  setHouseNumberInput: (value: string) => void;
  houseNumberSelected: HouseNumberResult | undefined;
  setHouseNumberSelected: (houseNumber: HouseNumberResult | undefined) => void;
  houseNumberResults: HouseNumberResult[] | undefined;
  setHouseNumberResults: (houseNumbers: HouseNumberResult[] | undefined) => void;
  houseNumberNotFound: boolean;
  setHouseNumberNotFound: (value: boolean) => void;
  resetSearch: () => void;

  // State for LivingSituationStep selections
  personTypeSelection: string | undefined;
  setPersonTypeSelection: (value: string | null) => void;
  housingTypeSelection: string | undefined;
  setHousingTypeSelection: (value: string | null) => void;

  // Modal
  isAcModalOpen: boolean;
  setAcModalOpen: (value: boolean) => void;

  // AC Button visibility
  isAcButtonHidden: boolean;
  setIsAcButtonHidden: (value: boolean) => void;
};

const defaultState = {
  cityInput: '',
  citySelected: undefined,
  cityResults: undefined,
  cityNotFound: false,
  streetInput: '',
  streetSelected: undefined,
  streetResults: undefined,
  streetNotFound: false,
  houseNumberInput: '',
  houseNumberSelected: undefined,
  houseNumberResults: undefined,
  houseNumberNotFound: false,
  personTypeSelection: undefined,
  housingTypeSelection: undefined,
  isAcModalOpen: false,
  isAcButtonHidden: false,
};

export const useAvailabilityCheckStoreBase = create<AvailabilityCheckStore>((set) => ({
  ...defaultState,
  acType: 'button',
  setAcType: (value) => {
    set({ acType: value });
  },
  setCityInput: (value) => {
    set({ cityInput: value });
  },
  setCitySelected: (city: CityResult | undefined) => {
    set({ citySelected: city });
  },
  setCityResults: (cities: CityResult[] | undefined) => {
    set({ cityResults: cities });
  },
  setCityNotFound: (value) => {
    set({ cityNotFound: value });
  },
  setStreetInput: (value) => {
    set({ streetInput: value });
  },
  setStreetSelected: (street) => {
    set({ streetSelected: street });
  },
  setStreetResults: (streets: StreetResult[] | undefined) => {
    set({ streetResults: streets });
  },
  setStreetNotFound: (value) => {
    set({ streetNotFound: value });
  },
  setHouseNumberInput: (value) => {
    set({ houseNumberInput: value });
  },
  setHouseNumberSelected: (houseNumber) => {
    set({ houseNumberSelected: houseNumber });
  },
  setHouseNumberResults: (houseNumbers: HouseNumberResult[] | undefined) => {
    set({ houseNumberResults: houseNumbers });
  },
  setHouseNumberNotFound: (value) => {
    set({ houseNumberNotFound: value });
  },
  resetSearch: () => {
    set((state) => ({
      ...defaultState,
      // Clear living situation selection state on reset
      personTypeSelection: undefined,
      housingTypeSelection: undefined,
      // Preserve modal state on reset
      isAcModalOpen: state.isAcModalOpen,
    }));
  },
  setAcModalOpen: (value) => set({ isAcModalOpen: value }),

  // Setters for LivingSituationStep selections
  setPersonTypeSelection: (value) => set({ personTypeSelection: value === null ? undefined : value }),
  setHousingTypeSelection: (value) => set({ housingTypeSelection: value === null ? undefined : value }),

  // AC Button visibility
  setIsAcButtonHidden: (value) => set({ isAcButtonHidden: value }),
}));

export const useAvailabilityCheckStore = createSelectors(useAvailabilityCheckStoreBase);
