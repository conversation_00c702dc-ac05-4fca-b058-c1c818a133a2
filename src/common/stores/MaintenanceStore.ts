import { create } from 'zustand';

import type { Disruption } from '@/apis/dg/parser/disruptions';

import { createSelectors } from './utils/createSelectors';

type MaintenanceState = {
  disruptionsData?: Disruption[];
  searchTerm: string;
  filteredDisruptions: Disruption[];
  setSearchTerm: (term: string) => void;
  initializeDisruptions: (data: Disruption[] | undefined) => void;
};

// Function to filter disruptions based on search term
const filterDisruptions = (disruptions: Disruption[], term: string) => {
  const lowerTerm = term.toLowerCase();
  return disruptions.filter(
    ({ category, area, subject, description, affectedProducts }) =>
      category.toLowerCase().includes(lowerTerm) ||
      area.some((a) => a.toLowerCase().includes(lowerTerm)) ||
      subject.toLowerCase().includes(lowerTerm) ||
      description.toLowerCase().includes(lowerTerm) ||
      affectedProducts.some((p) => p.toLowerCase().includes(lowerTerm)),
  );
};

export const useMaintenanceStoreBase = create<MaintenanceState>((set) => ({
  disruptionsData: [],
  searchTerm: '',
  filteredDisruptions: [],
  setSearchTerm: (term) => {
    set((state) => ({
      searchTerm: term,
      filteredDisruptions: filterDisruptions(state.disruptionsData || [], term),
    }));
  },
  initializeDisruptions: (data) =>
    set({
      disruptionsData: data,
      filteredDisruptions: data,
    }),
}));

export const useMaintenanceStore = createSelectors(useMaintenanceStoreBase);
