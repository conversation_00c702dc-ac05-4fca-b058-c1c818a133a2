import { create } from 'zustand';

import { getCookie, setCookie } from '@/actions/cookie';

import type { Notification } from '@/apis/contentful/getPages';

import { createSelectors } from './utils/createSelectors';

export type ExtendedNotification = Notification & {
  persistent?: boolean;
};

type NotificationState = {
  notification: ExtendedNotification | null;
  setNotification: (notification: ExtendedNotification | null) => void;
};

export const useNotificationStoreBase = create<NotificationState>((set) => ({
  notification: null,
  setNotification: (notification) => set({ notification }),
}));

export async function showNotification(notification: ExtendedNotification) {
  const { id, type, headline, text, cutoffDate, delay, persistent } = notification;

  if (!persistent) {
    const notificationCookie = await getNotificationFromCookie(id);
    if (notificationCookie && notificationCookie.value === 'hide') {
      useNotificationStoreBase.setState({ notification: null });
      return;
    }
  }

  const notificationToShow: ExtendedNotification = {
    id,
    type,
    headline,
    text,
    cutoffDate,
    delay: delay ?? 0,
    persistent,
  };

  setTimeout(() => {
    useNotificationStoreBase.setState({ notification: notificationToShow });
  }, delay ?? 0);
}

export function closeNotification(id: string) {
  const currentNotification = useNotificationStoreBase.getState().notification;

  if (currentNotification && !currentNotification.persistent) {
    setCookie({ name: `n_${id}`, value: 'hide' }).then(() => {
      useNotificationStoreBase.setState({ notification: null });
    });
  } else {
    useNotificationStoreBase.setState({ notification: null });
  }
}

async function getNotificationFromCookie(id: string) {
  return await getCookie(`n_${id}`);
}

export const useNotificationStore = createSelectors(useNotificationStoreBase);
