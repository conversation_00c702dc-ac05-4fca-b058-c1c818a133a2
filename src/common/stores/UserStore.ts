import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { type AcAddress, type LivingSituation, type OfferList } from '@/apis/dg/types';

import type { UserISP } from '@/types/tracking';

import { clearHouseIdFromCookies, getHouseIdFromCookies, saveHouseIdToCookies } from '@/utils/cookies/houseId';
import {
  clearLivingSituationFromCookies,
  getLivingSituationFromCookies,
  saveLivingSituationToCookies,
} from '@/utils/cookies/livingSituation';
import { getProviderFromCookies, saveProviderToCookies } from '@/utils/cookies/provider';
import {
  clearUserAddressFromCookies,
  getUserAddressFromCookies,
  saveUserAddressToCookies,
} from '@/utils/cookies/userAddress';

import { createSelectors } from './utils/createSelectors';

type UserStore = {
  houseId: string | null | undefined;
  setHouseId: (houseId: string) => void;
  offerList: OfferList | null | undefined;
  setOfferList: (offerList: OfferList | null | undefined) => void;
  acAddress: AcAddress | null | undefined;
  setAcAddress: (acAddress: AcAddress) => void;
  livingSituation: LivingSituation | null | undefined;
  setLivingSituation: (situation: LivingSituation) => void;
  provider: 'DGH' | 'DGP';
  setProvider: (provider: 'DGH' | 'DGP') => void;
  trackingEnabled: boolean;
  setTrackingEnabled: (value: boolean) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  userISP: UserISP | undefined;
  setUserISP: (userISP: UserISP | undefined) => void;
  getUserAddressAsString: () => string;
  resetOfferList: () => void;
  hydrateFromCookies: () => Promise<void>;
};

const initialState = {
  houseId: undefined,
  offerList: undefined,
  acAddress: undefined,
  livingSituation: undefined,
  provider: 'DGH' as 'DGH' | 'DGP',
  trackingEnabled: false,
  loading: true,
  error: null,
  userISP: undefined,
};

export const useUserStoreBase = create<UserStore>()(
  devtools((set, get) => ({
    ...initialState,
    setUserISP: (userISP) => {
      set({ userISP });
    },
    setOfferList: (offerList) => {
      set({ offerList });
    },
    setHouseId: (houseId) => {
      saveHouseIdToCookies(houseId);
      set({ houseId });
    },
    setAcAddress: (acAddress) => {
      saveUserAddressToCookies(acAddress);
      set({ acAddress });
    },
    setProvider: (provider) => {
      saveProviderToCookies(provider);
      set({ provider });
    },
    setLivingSituation: (livingSituation) => {
      saveLivingSituationToCookies(livingSituation);
      set({ livingSituation });
    },
    getUserAddressAsString: () => {
      const userAddress = get().acAddress;
      if (!userAddress) return 'Verfügbarkeit prüfen';

      const { street, houseNumber, houseNumberExtension, zipCode, city } = userAddress;

      return `${street} ${houseNumber}${houseNumberExtension ? houseNumberExtension : ''},${zipCode ? ` ${zipCode}` : ''}${city ? ` ${city}` : ''}`;
    },
    setLoading: (loading) => {
      set({ loading });
    },
    setError: (error) => {
      set({ error });
    },
    setTrackingEnabled: (value) => {
      set({ trackingEnabled: value });
    },
    resetOfferList: () => {
      set({
        offerList: undefined,
        acAddress: undefined,
        livingSituation: undefined,
        houseId: undefined,
      });
      clearHouseIdFromCookies();
      clearUserAddressFromCookies();
      clearLivingSituationFromCookies();
    },
    hydrateFromCookies: async () => {
      const { provider, houseId, acAddress, livingSituation } = get();

      // Only load from cookies if the corresponding state value is undefined
      // Wrap each cookie operation in try-catch to handle errors gracefully
      let cookieProvider = null;
      let cookieHouseId = null;
      let cookieAddress = null;
      let cookieLivingSituation = null;

      try {
        cookieProvider = provider === undefined ? await getProviderFromCookies() : null;
      } catch (error) {
        // Silently ignore cookie loading errors
        console.warn('Failed to load provider from cookies:', error);
      }

      try {
        cookieHouseId = houseId === undefined ? await getHouseIdFromCookies() : null;
      } catch (error) {
        // Silently ignore cookie loading errors
        console.warn('Failed to load houseId from cookies:', error);
      }

      try {
        cookieAddress = acAddress === undefined ? await getUserAddressFromCookies() : null;
      } catch (error) {
        // Silently ignore cookie loading errors
        console.warn('Failed to load address from cookies:', error);
      }

      try {
        cookieLivingSituation = livingSituation === undefined ? await getLivingSituationFromCookies() : null;
      } catch (error) {
        // Silently ignore cookie loading errors
        console.warn('Failed to load living situation from cookies:', error);
      }

      // Update state with loaded values (only if they exist and current state is undefined)
      const updates: Partial<UserStore> = {};

      if (cookieProvider && provider === undefined) {
        updates.provider = cookieProvider;
      }
      if (cookieHouseId && houseId === undefined) {
        updates.houseId = cookieHouseId;
      }
      if (cookieAddress && acAddress === undefined) {
        updates.acAddress = cookieAddress;
      }
      if (cookieLivingSituation && livingSituation === undefined) {
        updates.livingSituation = cookieLivingSituation;
      }

      if (Object.keys(updates).length > 0) {
        set(updates);
      }
    },
  })),
);

export const useUserStore = createSelectors(useUserStoreBase);
