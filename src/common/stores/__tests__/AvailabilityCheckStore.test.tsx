import { act, renderHook } from '@testing-library/react';

import { useAvailabilityCheckStoreBase } from '@/stores/AvailabilityCheckStore';

import type { AcType } from '@/types/availabilityCheck';

describe('Test AvailabilityCheckStore set and reset functions', () => {
  const initialStoreMock = {
    cityInput: '',
    citySelected: undefined,
    cityResults: undefined,
    cityNotFound: false,
    streetInput: '',
    streetSelected: undefined,
    streetResults: undefined,
    streetNotFound: false,
    houseNumberInput: '',
    houseNumberSelected: undefined,
    houseNumberResults: undefined,
    houseNumberNotFound: false,
  };

  const mockCitySelected = {
    id: 1,
    zipcode: '12345',
    city: 'Berlin',
  };

  const mockCityResults = [
    {
      id: 1,
      zipcode: '12345',
      city: 'Berlin',
    },
    {
      id: 2,
      zipcode: '12346',
      city: 'Berlin-Karlshorst',
    },
  ];

  const mockStreetSelected = {
    id: 1,
    name: 'Street',
    tzip: '123',
  };

  const mockStreetResults = [
    {
      id: 1,
      name: 'Street',
      tzip: '123',
    },
    {
      id: 2,
      name: 'Street-two',
      tzip: '456',
    },
  ];

  const mockHouseNumberSelected = {
    id: '1',
    street: 'Street',
    houseNumber: 1,
    houseNumberExtension: '123',
  };

  const mockHouseNumberResults = [
    {
      id: '1',
      street: 'Street',
      houseNumber: 1,
      houseNumberExtension: '123',
    },
    {
      id: '2',
      street: 'Street',
      houseNumber: 2,
      houseNumberExtension: '456',
    },
  ];

  it('should return the initial state corectly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    for (const key in initialStoreMock) {
      expect(acStoreMock.current[key as keyof typeof initialStoreMock]).toEqual(
        initialStoreMock[key as keyof typeof initialStoreMock],
      );
    }
  });

  it('should set ac type correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.acType).toBe('button');

    act(() => {
      acStoreMock.current.setAcType('flyOut' as AcType);
    });
    expect(acStoreMock.current.acType).toBe('flyOut');

    act(() => {
      acStoreMock.current.setAcType('page' as AcType);
    });
    expect(acStoreMock.current.acType).toBe('page');

    act(() => {
      acStoreMock.current.setAcType('query' as AcType);
    });
    expect(acStoreMock.current.acType).toBe('query');
  });

  it('should set city input correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.cityInput).toBe('');

    act(() => {
      acStoreMock.current.setCityInput('Berlin');
    });
    expect(acStoreMock.current.cityInput).toEqual('Berlin');

    act(() => {
      acStoreMock.current.setCityInput('');
    });
    expect(acStoreMock.current.cityInput).toBe('');
  });

  it('should set city selected correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.citySelected).toBe(undefined);

    act(() => {
      acStoreMock.current.setCitySelected(mockCitySelected);
    });
    expect(acStoreMock.current.citySelected).toEqual(mockCitySelected);

    act(() => {
      acStoreMock.current.setCitySelected(undefined);
    });
    expect(acStoreMock.current.citySelected).toBe(undefined);
  });

  it('should set city results correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.cityResults).toBe(undefined);

    act(() => {
      acStoreMock.current.setCityResults(mockCityResults);
    });
    expect(acStoreMock.current.cityResults).toEqual(mockCityResults);

    act(() => {
      acStoreMock.current.setCityResults([]);
    });
    expect(acStoreMock.current.cityResults).toEqual([]);

    act(() => {
      acStoreMock.current.setCityResults(undefined);
    });
    expect(acStoreMock.current.cityResults).toBe(undefined);
  });

  it('should set city not found correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.cityNotFound).toBe(false);

    act(() => {
      acStoreMock.current.setCityNotFound(true);
    });
    expect(acStoreMock.current.cityNotFound).toBe(true);

    act(() => {
      acStoreMock.current.setCityNotFound(false);
    });
    expect(acStoreMock.current.cityNotFound).toBe(false);
  });

  it('should set street input correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.streetInput).toBe('');

    act(() => {
      acStoreMock.current.setStreetInput('Street');
    });
    expect(acStoreMock.current.streetInput).toEqual('Street');

    act(() => {
      acStoreMock.current.setStreetInput('');
    });
    expect(acStoreMock.current.streetInput).toEqual('');
  });

  it('should set street input correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.streetInput).toBe('');

    act(() => {
      acStoreMock.current.setStreetInput('Street');
    });
    expect(acStoreMock.current.streetInput).toEqual('Street');

    act(() => {
      acStoreMock.current.setStreetInput('');
    });
    expect(acStoreMock.current.streetInput).toEqual('');
  });

  it('should set street selected correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.streetSelected).toBe(undefined);

    act(() => {
      acStoreMock.current.setStreetSelected(mockStreetSelected);
    });
    expect(acStoreMock.current.streetSelected).toEqual(mockStreetSelected);

    act(() => {
      acStoreMock.current.setStreetSelected(undefined);
    });
    expect(acStoreMock.current.streetSelected).toBe(undefined);
  });

  it('should set street results correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.cityResults).toBe(undefined);

    act(() => {
      acStoreMock.current.setStreetResults(mockStreetResults);
    });
    expect(acStoreMock.current.streetResults).toEqual(mockStreetResults);

    act(() => {
      acStoreMock.current.setStreetResults([]);
    });
    expect(acStoreMock.current.streetResults).toEqual([]);

    act(() => {
      acStoreMock.current.setStreetResults(undefined);
    });
    expect(acStoreMock.current.streetResults).toBe(undefined);
  });

  it('should set street not found correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.streetNotFound).toBe(false);

    act(() => {
      acStoreMock.current.setStreetNotFound(true);
    });
    expect(acStoreMock.current.streetNotFound).toBe(true);

    act(() => {
      acStoreMock.current.setStreetNotFound(false);
    });
    expect(acStoreMock.current.streetNotFound).toBe(false);
  });

  it('should set house number input correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.houseNumberInput).toBe('');

    act(() => {
      acStoreMock.current.setHouseNumberInput('1');
    });
    expect(acStoreMock.current.houseNumberInput).toEqual('1');

    act(() => {
      acStoreMock.current.setHouseNumberInput('');
    });
    expect(acStoreMock.current.houseNumberInput).toEqual('');
  });

  it('should set house number selected correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.houseNumberSelected).toBe(undefined);

    act(() => {
      acStoreMock.current.setHouseNumberSelected(mockHouseNumberSelected);
    });
    expect(acStoreMock.current.houseNumberSelected).toEqual(mockHouseNumberSelected);

    act(() => {
      acStoreMock.current.setHouseNumberSelected(undefined);
    });
    expect(acStoreMock.current.houseNumberSelected).toBe(undefined);
  });

  it('should set house number results correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    expect(acStoreMock.current.houseNumberResults).toBe(undefined);

    act(() => {
      acStoreMock.current.setHouseNumberResults(mockHouseNumberResults);
    });
    expect(acStoreMock.current.houseNumberResults).toEqual(mockHouseNumberResults);

    act(() => {
      acStoreMock.current.setHouseNumberResults([]);
    });
    expect(acStoreMock.current.houseNumberResults).toEqual([]);

    act(() => {
      acStoreMock.current.setHouseNumberResults(undefined);
    });
    expect(acStoreMock.current.houseNumberResults).toBe(undefined);
  });

  it('should set house number not found correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.houseNumberNotFound).toBe(false);

    act(() => {
      acStoreMock.current.setHouseNumberNotFound(true);
    });
    expect(acStoreMock.current.houseNumberNotFound).toBe(true);

    act(() => {
      acStoreMock.current.setHouseNumberNotFound(false);
    });
    expect(acStoreMock.current.houseNumberNotFound).toBe(false);
  });

  it('should reset search correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());

    act(() => {
      acStoreMock.current.setAcType('query' as AcType);
      acStoreMock.current.setCityInput('Berlin');
      acStoreMock.current.setCitySelected(mockCitySelected);
      acStoreMock.current.setCityResults(mockCityResults);
      acStoreMock.current.setCityNotFound(true);
      acStoreMock.current.setStreetInput('Street');
      acStoreMock.current.setStreetSelected(mockStreetSelected);
      acStoreMock.current.setStreetResults(mockStreetResults);
      acStoreMock.current.setStreetNotFound(true);
      acStoreMock.current.setHouseNumberInput('1');
      acStoreMock.current.setHouseNumberSelected(mockHouseNumberSelected);
      acStoreMock.current.setHouseNumberResults(mockHouseNumberResults);
      acStoreMock.current.setHouseNumberNotFound(true);
    });

    expect(acStoreMock.current.acType).toBe('query');
    expect(acStoreMock.current.cityInput).toBe('Berlin');
    expect(acStoreMock.current.citySelected).toBe(mockCitySelected);
    expect(acStoreMock.current.cityResults).toBe(mockCityResults);
    expect(acStoreMock.current.cityNotFound).toBe(true);
    expect(acStoreMock.current.streetInput).toBe('Street');
    expect(acStoreMock.current.streetSelected).toBe(mockStreetSelected);
    expect(acStoreMock.current.streetResults).toBe(mockStreetResults);
    expect(acStoreMock.current.streetNotFound).toBe(true);
    expect(acStoreMock.current.houseNumberInput).toBe('1');
    expect(acStoreMock.current.houseNumberSelected).toBe(mockHouseNumberSelected);
    expect(acStoreMock.current.houseNumberResults).toBe(mockHouseNumberResults);
    expect(acStoreMock.current.houseNumberNotFound).toBe(true);

    act(() => {
      acStoreMock.current.resetSearch();
    });

    for (const key in initialStoreMock) {
      expect(acStoreMock.current[key as keyof typeof initialStoreMock]).toEqual(
        initialStoreMock[key as keyof typeof initialStoreMock],
      );
    }
  });
});

describe('Test AvailabilityCheckStore isAcButtonHidden functionality', () => {
  it('should return the initial isAcButtonHidden state correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.isAcButtonHidden).toBe(false);
  });

  it('should set and get isAcButtonHidden state correctly', () => {
    const { result: acStoreMock } = renderHook(() => useAvailabilityCheckStoreBase());
    expect(acStoreMock.current.isAcButtonHidden).toBe(false);

    act(() => {
      acStoreMock.current.setIsAcButtonHidden(true);
    });

    expect(acStoreMock.current.isAcButtonHidden).toBe(true);

    act(() => {
      acStoreMock.current.setIsAcButtonHidden(false);
    });

    expect(acStoreMock.current.isAcButtonHidden).toBe(false);
  });
});
