import { type AcAddress, type LivingSituation, type OfferList } from '@/apis/dg/types';

import type { UserISP } from '@/types/tracking';

// Import mocked functions for assertions
import { clearHouseIdFromCookies, getHouseIdFromCookies, saveHouseIdToCookies } from '@/utils/cookies/houseId';
import {
  clearLivingSituationFromCookies,
  getLivingSituationFromCookies,
  saveLivingSituationToCookies,
} from '@/utils/cookies/livingSituation';
import { getProviderFromCookies, saveProviderToCookies } from '@/utils/cookies/provider';
import {
  clearUserAddressFromCookies,
  getUserAddressFromCookies,
  saveUserAddressToCookies,
} from '@/utils/cookies/userAddress';

import { useUserStoreBase } from '../UserStore';

// Mock all cookie utilities
jest.mock('@/utils/cookies/houseId', () => ({
  clearHouseIdFromCookies: jest.fn(),
  getHouseIdFromCookies: jest.fn(),
  saveHouseIdToCookies: jest.fn(),
}));

jest.mock('@/utils/cookies/livingSituation', () => ({
  clearLivingSituationFromCookies: jest.fn(),
  getLivingSituationFromCookies: jest.fn(),
  saveLivingSituationToCookies: jest.fn(),
}));

jest.mock('@/utils/cookies/provider', () => ({
  getProviderFromCookies: jest.fn(),
  saveProviderToCookies: jest.fn(),
}));

jest.mock('@/utils/cookies/userAddress', () => ({
  clearUserAddressFromCookies: jest.fn(),
  getUserAddressFromCookies: jest.fn(),
  saveUserAddressToCookies: jest.fn(),
}));

describe('UserStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useUserStoreBase.setState({
      houseId: undefined,
      offerList: undefined,
      acAddress: undefined,
      livingSituation: undefined,
      provider: 'DGH',
      trackingEnabled: false,
      loading: true,
      error: null,
      userISP: undefined,
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useUserStoreBase.getState();

      expect(state.houseId).toBeUndefined();
      expect(state.offerList).toBeUndefined();
      expect(state.acAddress).toBeUndefined();
      expect(state.livingSituation).toBeUndefined();
      expect(state.provider).toBe('DGH');
      expect(state.trackingEnabled).toBe(false);
      expect(state.loading).toBe(true);
      expect(state.error).toBeNull();
      expect(state.userISP).toBeUndefined();
    });
  });

  describe('setUserISP', () => {
    it('should update userISP', () => {
      const mockUserISP: UserISP = { ip: '***********', isp: 'Test ISP' };

      useUserStoreBase.getState().setUserISP(mockUserISP);

      expect(useUserStoreBase.getState().userISP).toEqual(mockUserISP);
    });

    it('should handle undefined userISP', () => {
      useUserStoreBase.getState().setUserISP(undefined);

      expect(useUserStoreBase.getState().userISP).toBeUndefined();
    });
  });

  describe('setOfferList', () => {
    it('should update offerList', () => {
      const mockOfferList: OfferList = {
        campaign: null,
        address: null,
        offers: [],
        projectNumber: null,
        projectSlug: null,
        projectName: null,
        projectStatus: null,
        projectStatusOriginal: null,
        projectStatusDescription: null,
        projectStatusGroupCode: null,
        projectStatusGroupTitle: null,
        projectStatusTitle: null,
        projectDemandBundling: null,
        deliveryStatus: null,
        dwellingUnits: null,
        infoText: null,
      };

      useUserStoreBase.getState().setOfferList(mockOfferList);

      expect(useUserStoreBase.getState().offerList).toEqual(mockOfferList);
    });

    it('should handle null offerList', () => {
      useUserStoreBase.getState().setOfferList(null);

      expect(useUserStoreBase.getState().offerList).toBeNull();
    });
  });

  describe('setHouseId', () => {
    it('should update houseId and save to cookies', () => {
      const testHouseId = 'house-123';

      useUserStoreBase.getState().setHouseId(testHouseId);

      expect(useUserStoreBase.getState().houseId).toBe(testHouseId);
      expect(saveHouseIdToCookies).toHaveBeenCalledWith(testHouseId);
    });
  });

  describe('setAcAddress', () => {
    it('should update acAddress and save to cookies', () => {
      const mockAddress: AcAddress = {
        street: 'Test Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '12345',
        city: 'Test City',
      };

      useUserStoreBase.getState().setAcAddress(mockAddress);

      expect(useUserStoreBase.getState().acAddress).toEqual(mockAddress);
      expect(saveUserAddressToCookies).toHaveBeenCalledWith(mockAddress);
    });
  });

  describe('setProvider', () => {
    it('should update provider to DGP and save to cookies', () => {
      useUserStoreBase.getState().setProvider('DGP');

      expect(useUserStoreBase.getState().provider).toBe('DGP');
      expect(saveProviderToCookies).toHaveBeenCalledWith('DGP');
    });

    it('should update provider to DGH and save to cookies', () => {
      useUserStoreBase.getState().setProvider('DGH');

      expect(useUserStoreBase.getState().provider).toBe('DGH');
      expect(saveProviderToCookies).toHaveBeenCalledWith('DGH');
    });
  });

  describe('setLivingSituation', () => {
    it('should update livingSituation and save to cookies', () => {
      const mockSituation: LivingSituation = {
        personType: 'PRIVATE',
        housingType: 'HOUSE',
      };

      useUserStoreBase.getState().setLivingSituation(mockSituation);

      expect(useUserStoreBase.getState().livingSituation).toEqual(mockSituation);
      expect(saveLivingSituationToCookies).toHaveBeenCalledWith(mockSituation);
    });
  });

  describe('setLoading', () => {
    it('should update loading state', () => {
      useUserStoreBase.getState().setLoading(false);

      expect(useUserStoreBase.getState().loading).toBe(false);
    });
  });

  describe('setError', () => {
    it('should update error state', () => {
      const errorMessage = 'Test error';

      useUserStoreBase.getState().setError(errorMessage);

      expect(useUserStoreBase.getState().error).toBe(errorMessage);
    });

    it('should clear error state', () => {
      useUserStoreBase.getState().setError(null);

      expect(useUserStoreBase.getState().error).toBeNull();
    });
  });

  describe('setTrackingEnabled', () => {
    it('should enable tracking', () => {
      useUserStoreBase.getState().setTrackingEnabled(true);

      expect(useUserStoreBase.getState().trackingEnabled).toBe(true);
    });

    it('should disable tracking', () => {
      useUserStoreBase.getState().setTrackingEnabled(false);

      expect(useUserStoreBase.getState().trackingEnabled).toBe(false);
    });
  });

  describe('getUserAddressAsString', () => {
    it('should return default message when no address is set', () => {
      const result = useUserStoreBase.getState().getUserAddressAsString();

      expect(result).toBe('Verfügbarkeit prüfen');
    });

    it('should format complete address correctly', () => {
      const mockAddress: AcAddress = {
        street: 'Main Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '12345',
        city: 'Berlin',
      };

      useUserStoreBase.getState().setAcAddress(mockAddress);
      const result = useUserStoreBase.getState().getUserAddressAsString();

      expect(result).toBe('Main Street 123A, 12345 Berlin');
    });

    it('should format address without extension', () => {
      const mockAddress: AcAddress = {
        street: 'Main Street',
        houseNumber: 123,
        houseNumberExtension: '',
        zipCode: '12345',
        city: 'Berlin',
      };

      useUserStoreBase.getState().setAcAddress(mockAddress);
      const result = useUserStoreBase.getState().getUserAddressAsString();

      expect(result).toBe('Main Street 123, 12345 Berlin');
    });

    it('should format address without zipCode', () => {
      const mockAddress: AcAddress = {
        street: 'Main Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '',
        city: 'Berlin',
      };

      useUserStoreBase.getState().setAcAddress(mockAddress);
      const result = useUserStoreBase.getState().getUserAddressAsString();

      expect(result).toBe('Main Street 123A, Berlin');
    });

    it('should format address without city', () => {
      const mockAddress: AcAddress = {
        street: 'Main Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '12345',
        city: '',
      };

      useUserStoreBase.getState().setAcAddress(mockAddress);
      const result = useUserStoreBase.getState().getUserAddressAsString();

      expect(result).toBe('Main Street 123A, 12345');
    });
  });

  describe('resetOfferList', () => {
    it('should reset all relevant state and clear cookies', () => {
      // Set some initial state
      const mockAddress: AcAddress = {
        street: 'Test Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '12345',
        city: 'Test City',
      };
      const mockSituation: LivingSituation = {
        personType: 'PRIVATE',
        housingType: 'HOUSE',
      };
      const mockOfferList: OfferList = {
        campaign: null,
        address: null,
        offers: [],
        projectNumber: null,
        projectSlug: null,
        projectName: null,
        projectStatus: null,
        projectStatusOriginal: null,
        projectStatusDescription: null,
        projectStatusGroupCode: null,
        projectStatusGroupTitle: null,
        projectStatusTitle: null,
        projectDemandBundling: null,
        deliveryStatus: null,
        dwellingUnits: null,
        infoText: null,
      };

      useUserStoreBase.setState({
        offerList: mockOfferList,
        acAddress: mockAddress,
        livingSituation: mockSituation,
        houseId: 'house-123',
      });

      // Reset
      useUserStoreBase.getState().resetOfferList();

      // Check state is reset
      const state = useUserStoreBase.getState();
      expect(state.offerList).toBeUndefined();
      expect(state.acAddress).toBeUndefined();
      expect(state.livingSituation).toBeUndefined();
      expect(state.houseId).toBeUndefined();

      // Check cookies are cleared
      expect(clearHouseIdFromCookies).toHaveBeenCalled();
      expect(clearUserAddressFromCookies).toHaveBeenCalled();
      expect(clearLivingSituationFromCookies).toHaveBeenCalled();
    });
  });

  describe('hydrateFromCookies', () => {
    it('should load all values from cookies when state is undefined', async () => {
      const mockProvider = 'DGP';
      const mockHouseId = 'house-123';
      const mockAddress: AcAddress = {
        street: 'Cookie Street',
        houseNumber: 456,
        houseNumberExtension: 'B',
        zipCode: '54321',
        city: 'Cookie City',
      };
      const mockSituation: LivingSituation = {
        personType: 'BUSINESS',
        housingType: 'APARTMENT',
      };

      // Mock cookie returns
      (getProviderFromCookies as jest.Mock).mockResolvedValue(mockProvider);
      (getHouseIdFromCookies as jest.Mock).mockResolvedValue(mockHouseId);
      (getUserAddressFromCookies as jest.Mock).mockResolvedValue(mockAddress);
      (getLivingSituationFromCookies as jest.Mock).mockResolvedValue(mockSituation);

      // Ensure state is undefined
      useUserStoreBase.setState({
        provider: undefined as any,
        houseId: undefined,
        acAddress: undefined,
        livingSituation: undefined,
      });

      await useUserStoreBase.getState().hydrateFromCookies();

      const state = useUserStoreBase.getState();
      expect(state.provider).toBe(mockProvider);
      expect(state.houseId).toBe(mockHouseId);
      expect(state.acAddress).toEqual(mockAddress);
      expect(state.livingSituation).toEqual(mockSituation);
    });

    it('should not load from cookies when state values are already defined', async () => {
      const existingProvider = 'DGH';
      const existingHouseId = 'existing-house';
      const existingAddress: AcAddress = {
        street: 'Existing Street',
        houseNumber: 789,
        houseNumberExtension: 'C',
        zipCode: '98765',
        city: 'Existing City',
      };
      const existingSituation: LivingSituation = {
        personType: 'PRIVATE',
        housingType: 'HOUSE',
      };

      // Set existing state
      useUserStoreBase.setState({
        provider: existingProvider,
        houseId: existingHouseId,
        acAddress: existingAddress,
        livingSituation: existingSituation,
      });

      // Mock cookie returns (should not be used)
      (getProviderFromCookies as jest.Mock).mockResolvedValue('DGP');
      (getHouseIdFromCookies as jest.Mock).mockResolvedValue('cookie-house');
      (getUserAddressFromCookies as jest.Mock).mockResolvedValue({
        street: 'Cookie Street',
        houseNumber: 123,
        houseNumberExtension: 'A',
        zipCode: '12345',
        city: 'Cookie City',
      });
      (getLivingSituationFromCookies as jest.Mock).mockResolvedValue({
        personType: 'BUSINESS',
        housingType: 'APARTMENT',
      });

      await useUserStoreBase.getState().hydrateFromCookies();

      // State should remain unchanged
      const state = useUserStoreBase.getState();
      expect(state.provider).toBe(existingProvider);
      expect(state.houseId).toBe(existingHouseId);
      expect(state.acAddress).toEqual(existingAddress);
      expect(state.livingSituation).toEqual(existingSituation);
    });

    it('should handle partial cookie loading', async () => {
      const mockHouseId = 'cookie-house';

      // Only mock one cookie to return a value
      (getProviderFromCookies as jest.Mock).mockResolvedValue(null);
      (getHouseIdFromCookies as jest.Mock).mockResolvedValue(mockHouseId);
      (getUserAddressFromCookies as jest.Mock).mockResolvedValue(null);
      (getLivingSituationFromCookies as jest.Mock).mockResolvedValue(null);

      // Set state where only some values are undefined
      useUserStoreBase.setState({
        provider: 'DGH', // defined, should not change
        houseId: undefined, // undefined, should load from cookie
        acAddress: undefined, // undefined, but no cookie value
        livingSituation: undefined, // undefined, but no cookie value
      });

      await useUserStoreBase.getState().hydrateFromCookies();

      const state = useUserStoreBase.getState();
      expect(state.provider).toBe('DGH'); // unchanged
      expect(state.houseId).toBe(mockHouseId); // loaded from cookie
      expect(state.acAddress).toBeUndefined(); // remains undefined
      expect(state.livingSituation).toBeUndefined(); // remains undefined
    });

    it('should handle cookie loading errors gracefully', async () => {
      // Mock console.warn to avoid console output during tests
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

      // Mock cookie functions to throw errors
      (getProviderFromCookies as jest.Mock).mockRejectedValue(new Error('Cookie error'));
      (getHouseIdFromCookies as jest.Mock).mockRejectedValue(new Error('Cookie error'));
      (getUserAddressFromCookies as jest.Mock).mockRejectedValue(new Error('Cookie error'));
      (getLivingSituationFromCookies as jest.Mock).mockRejectedValue(new Error('Cookie error'));

      // Set undefined state
      useUserStoreBase.setState({
        provider: undefined as any,
        houseId: undefined,
        acAddress: undefined,
        livingSituation: undefined,
      });

      // Should not throw and should resolve successfully
      await expect(useUserStoreBase.getState().hydrateFromCookies()).resolves.not.toThrow();

      // State should remain undefined
      const state = useUserStoreBase.getState();
      expect(state.provider).toBeUndefined();
      expect(state.houseId).toBeUndefined();
      expect(state.acAddress).toBeUndefined();
      expect(state.livingSituation).toBeUndefined();

      // Should have logged warnings for each failed cookie operation
      expect(consoleWarnSpy).toHaveBeenCalledTimes(4);
      expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to load provider from cookies:', expect.any(Error));
      expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to load houseId from cookies:', expect.any(Error));
      expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to load address from cookies:', expect.any(Error));
      expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to load living situation from cookies:', expect.any(Error));

      // Restore console.warn
      consoleWarnSpy.mockRestore();
    });
  });
});
