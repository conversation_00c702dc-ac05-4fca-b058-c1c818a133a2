import type { Url } from 'next/dist/shared/lib/router/router';

import type { ContentImage } from '@/types/contentImage';
import type { Modal } from '@/types/modal';

import type { Theme } from './theme';

export type CTA = {
  label: string;
  labelAfterAcPositive?: string;
  labelAfterAcNegative?: string;
  type: 'Link' | 'Modal' | 'Flyout';
  variant?: 'primary' | 'secondary' | 'tertiary';
  to?: Url | Modal;
  icon?: ContentImage;
  theme?: Theme;
  truncateLabel?: boolean;
};
