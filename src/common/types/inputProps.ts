import type React from 'react';

import type { FormLabelColor } from '@/components/FormLabel';

import type { Tooltip } from '@/types/tooltip';

export type InputProps = {
  /**
   * Specify custom classes to be applied to the component
   */
  className?: string;

  /**
   * Specify whether the `<input>` should be disabled
   */
  disabled?: boolean;

  /**
   * Specify the attribute for hotjar anonymization of the `<input>`
   */
  hotjarSuppress?: boolean;

  /**
   * Specify a custom `id` for the `<input>`
   */
  id?: string;

  /**
   * Provide a `name` to give the checkbox a name
   */
  name?: string;

  /**
   * Specify whether the control is currently invalid
   */
  invalid?: boolean;

  /**
   * Specify whether the control is back to valid
   */
  valid?: boolean;

  /**
   * Provide the text that is displayed when the control is in an invalid state
   */
  invalidText?: string;

  /**
   * Provide the text that will be read by a screen reader when visiting this
   * control
   */
  label: string;

  /**
   * Specify whether the label should be hidden
   */
  labelHidden?: boolean;

  /**
   * Specify the color of the label
   */
  labelColor?: FormLabelColor;

  /**
   * Specify the placeholder attribute for the `<input>`
   */
  placeholder?: string;

  /**
   * Optionally provide the default value of the input.
   * Matches React's native HTML input element type.
   */
  defaultValue?: string | number | readonly string[] | undefined;

  /**
   * Optional tooltip configuration
   */
  tooltip?: Tooltip;

  /**
   * Specify if the input is required
   */
  required?: boolean;

  /**
   * Fired when the error icon is clicked.
   * @param event React.MouseEvent<SVGSVGElement>
   */
  onClickErrorIcon?: (event: React.MouseEvent<SVGSVGElement>) => void;

  /**
   * Whether the input should automatically gain focus on mount.
   */
  autofocus?: boolean;

  /**
   * Whether the input is read-only
   */
  readonly?: boolean;
};
