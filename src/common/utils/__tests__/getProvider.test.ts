import { Provider } from '@/apis/dg/types';

import { getProvider } from '../getProvider';

describe('Check for correct Provider - Utility', () => {
  test.each([
    ['Geschäftskunden', Provider.Professional],
    ['Privatkunden', Provider.Private],
    ['', Provider.Private],
    ['Wohnungswirtschaft', Provider.Private],
    ['Kommunen', Provider.Private],
    ['Unternehmen', Provider.Private],
    ['Digital Citizen Network', Provider.Private],
  ])('should return correct provider for Business Area: %s', (provider, expected) => {
    expect(getProvider(provider)).toEqual(expected);
  });
});
