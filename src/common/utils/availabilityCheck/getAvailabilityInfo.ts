import type { OfferList } from '@/apis/dg/types';

export function getAvailabilityInfo(offerList: OfferList | undefined | null) {
  if (!offerList) return { isAvailable: false, isIndustrialPark: false };

  const isIndustrialPark = !!offerList.address?.cms?.isIndustrialPark;
  const isAvailable = isIndustrialPark
    ? !!offerList.address?.cms?.isIndustrialParkHappyflow
    : !!offerList.address?.isHappyFlow;

  return { isAvailable, isIndustrialPark };
}
