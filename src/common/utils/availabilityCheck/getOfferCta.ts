import type { OfferList } from '@/apis/dg/types';

export function getOfferCta(provider: 'DGH' | 'DGP', offerList: OfferList) {
  const label = '<PERSON>ur Tarifauswahl';
  return offerList.address?.cms?.isIndustrialPark // Industrial Park serves first no matter what
    ? { label, href: '/geschaeftskunden/business/tarife' }
    : offerList.campaign?.cta // Campaign serves second
      ? { label: offerList.campaign.cta.label, href: `/${offerList.campaign.cta.href}` }
      : provider === 'DGH'
        ? { label, href: '/tarife' }
        : { label, href: '/geschaeftskunden/professional/tarife' };
}
