import { act, waitFor } from '@testing-library/react';

import type { CookieProps } from '@/actions/cookie';

import {
  COOKIE_EXPIRATION_HOURS,
  HID_COOKIE_KEY,
  clearHouseIdFromCookies,
  getHouseIdFromCookies,
  saveHouseIdToCookies,
} from '../houseId';

const mockDate = new Date('2024-01-01T12:00:00');
const mockTimestamp = mockDate.getTime();
const expirationTime = mockTimestamp + COOKIE_EXPIRATION_HOURS * 60 * 60 * 1000;

// Internal type for the stored cookie value
type HouseIdCookieValue = {
  houseId: string;
  timestamp: number;
};

const mockHouseId = '46325CKD-16-';

const mockHouseIdCookie = {
  name: HID_COOKIE_KEY,
  value: JSON.stringify({
    houseId: mockHouseId,
    timestamp: mockTimestamp,
  } as HouseIdCookieValue),
};

// Expected response is just the string
const mockExpectedHouseId: string = mockHouseId;

const mockGetCookie = jest.fn();
const mockSetCookie = jest.fn();
const mockDeleteCookie = jest.fn();

jest.mock('@/actions/cookie', () => ({
  ...jest.requireActual('@/actions/cookie'),
  getCookie: (cookieName: string) => mockGetCookie(cookieName),
  setCookie: (cookieParams: CookieProps) => mockSetCookie(cookieParams),
  deleteCookie: (cookieName: string) => mockDeleteCookie(cookieName),
}));

describe('Test houseId GET util function', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return null if cookie is not set', async () => {
    const mockRequest = await getHouseIdFromCookies();
    expect(mockGetCookie).toHaveBeenNthCalledWith(1, HID_COOKIE_KEY);

    expect(mockRequest).toEqual(null);
  });

  it('should return correct response if cookie is set correctly (without timestamp)', async () => {
    // Set the mock time to the same time as the cookie timestamp
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);

    mockGetCookie.mockResolvedValue(mockHouseIdCookie);
    const mockRequest = await getHouseIdFromCookies();

    expect(mockGetCookie).toHaveBeenNthCalledWith(1, HID_COOKIE_KEY);
    // Should return just the houseId string
    expect(mockRequest).toEqual(mockExpectedHouseId);
    // expect(mockRequest).not.toHaveProperty('timestamp'); // Redundant check for string

    jest.useRealTimers();
  });

  it('should clear cookie and return null if timestamp is expired', async () => {
    jest.useFakeTimers();
    // Set current time to after expiration
    const futureDate = new Date(expirationTime + 1000);
    jest.setSystemTime(futureDate);

    mockGetCookie.mockResolvedValue(mockHouseIdCookie);
    const mockRequest = await getHouseIdFromCookies();

    expect(mockGetCookie).toHaveBeenCalledWith(HID_COOKIE_KEY);
    expect(mockDeleteCookie).toHaveBeenCalledWith(HID_COOKIE_KEY);
    expect(mockRequest).toEqual(null);

    jest.useRealTimers();
  });
});

describe('saveHouseIdToCookies', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should save houseId string to cookies with timestamp', async () => {
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
    await saveHouseIdToCookies(mockExpectedHouseId);

    expect(mockSetCookie).toHaveBeenCalledWith({
      name: HID_COOKIE_KEY,
      value: JSON.stringify({
        houseId: mockExpectedHouseId,
        timestamp: mockTimestamp,
      } as HouseIdCookieValue),
    });

    jest.useRealTimers();
  });
});

describe('Test houseId CLEAR util function', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should clear cookie correctly', async () => {
    await act(async () => {
      // Set the mock time to the same time as the cookie timestamp
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      // Set cookie first and check if it exists
      await saveHouseIdToCookies(mockExpectedHouseId);
      mockGetCookie.mockResolvedValue(mockHouseIdCookie);
      const currentCookie = await getHouseIdFromCookies();

      expect(mockGetCookie).toHaveBeenNthCalledWith(1, HID_COOKIE_KEY);
      expect(currentCookie).toEqual(mockExpectedHouseId);

      jest.useRealTimers();
    });

    await act(async () => {
      // Clear the cookie
      clearHouseIdFromCookies();
      expect(mockDeleteCookie).toHaveBeenNthCalledWith(1, HID_COOKIE_KEY);
      // Check if it was indeed deleted
      const newCookie = await getHouseIdFromCookies();
      expect(mockGetCookie).toHaveBeenNthCalledWith(2, HID_COOKIE_KEY);

      waitFor(() => {
        expect(newCookie).toEqual(null);
      });
    });
  });
});
