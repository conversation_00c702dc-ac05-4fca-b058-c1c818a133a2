import { deleteCookie, getCookie, setCookie } from '@/actions/cookie';

// Internal type for the stored cookie value
type HouseIdCookieValue = {
  houseId: string;
  timestamp: number;
};

export const HID_COOKIE_KEY = 'houseId';
export const COOKIE_EXPIRATION_HOURS = 12;

export async function getHouseIdFromCookies(): Promise<string | null> {
  const offerListParamsCookie = await getCookie(HID_COOKIE_KEY);
  if (!offerListParamsCookie) return null;

  // Parse the stored value
  const params = JSON.parse(offerListParamsCookie.value) as HouseIdCookieValue;

  // Timestamp should always be present, but check just in case
  if (params.timestamp) {
    const now = Date.now();
    const expirationTime = params.timestamp + COOKIE_EXPIRATION_HOURS * 60 * 60 * 1000;

    if (now > expirationTime) {
      clearHouseIdFromCookies();
      return null;
    }
  }

  // Return just the houseId string
  return params.houseId;
}

export async function saveHouseIdToCookies(houseId: string) {
  await setCookie({
    name: HID_COOKIE_KEY,
    value: JSON.stringify({
      houseId,
      timestamp: Date.now(),
    }),
  });
}

export function clearHouseIdFromCookies() {
  void deleteCookie(HID_COOKIE_KEY);
}
