import { deleteCookie, getC<PERSON>ie, setCookie } from '@/actions/cookie';

import type { LivingSituation } from '@/apis/dg/types';

const LIVING_SITUATION_COOKIE_KEY = 'livingSituation';
// Cookie expiration time in hours (same as userAddress)
const COOKIE_EXPIRATION_HOURS = 12;

// Internal type that includes the timestamp
type LivingSituationWithTimestamp = LivingSituation & {
  timestamp: number;
};

export async function getLivingSituationFromCookies(): Promise<LivingSituation | null> {
  const situationCookie = await getCookie(LIVING_SITUATION_COOKIE_KEY);
  if (!situationCookie) return null;

  try {
    const situationData = JSON.parse(situationCookie.value) as LivingSituationWithTimestamp;

    // Check timestamp for expiration
    if (situationData.timestamp) {
      const now = Date.now();
      const expirationTime = situationData.timestamp + COOKIE_EXPIRATION_HOURS * 60 * 60 * 1000;

      if (now > expirationTime) {
        // If expired, clear the cookie and return null
        clearLivingSituationFromCookies(); // Use await for async action
        return null;
      }
    } else {
      // If timestamp is missing, consider it invalid
      clearLivingSituationFromCookies();
      return null;
    }

    // Remove timestamp from returned data
    const { timestamp, ...situation } = situationData;
    return situation;
  } catch (e) {
    console.error('Error parsing living situation cookie:', e);
    // Clear potentially invalid cookie
    clearLivingSituationFromCookies();
    return null;
  }
}

export function saveLivingSituationToCookies(situation: LivingSituation) {
  void setCookie({
    name: LIVING_SITUATION_COOKIE_KEY,
    value: JSON.stringify({
      ...situation,
      timestamp: Date.now(), // Add current timestamp
    }),
  });
}

export function clearLivingSituationFromCookies() {
  void deleteCookie(LIVING_SITUATION_COOKIE_KEY);
}
