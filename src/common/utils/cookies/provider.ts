import { deleteCookie, getCookie, setCookie } from '@/actions/cookie';

const PROVIDER_COOKIE_KEY = 'provider';
// Cookie expiration time in hours (same as other user data)
const COOKIE_EXPIRATION_HOURS = 12;

// Internal type that includes the timestamp
type ProviderWithTimestamp = {
  provider: 'DGH' | 'DGP';
  timestamp: number;
};

export async function getProviderFromCookies(): Promise<'DGH' | 'DGP' | null> {
  const providerCookie = await getCookie(PROVIDER_COOKIE_KEY);
  if (!providerCookie) return null;

  try {
    const providerData = JSON.parse(providerCookie.value) as ProviderWithTimestamp;

    // Check timestamp for expiration
    if (providerData.timestamp) {
      const now = Date.now();
      const expirationTime = providerData.timestamp + COOKIE_EXPIRATION_HOURS * 60 * 60 * 1000;

      if (now > expirationTime) {
        // If expired, clear the cookie and return null
        clearProviderFromCookies();
        return null;
      }
    } else {
      // If timestamp is missing, consider it invalid
      clearProviderFromCookies();
      return null;
    }

    return providerData.provider;
  } catch (e) {
    console.error('Error parsing provider cookie:', e);
    // Clear potentially invalid cookie
    clearProviderFromCookies();
    return null;
  }
}

export function saveProviderToCookies(provider: 'DGH' | 'DGP') {
  void setCookie({
    name: PROVIDER_COOKIE_KEY,
    value: JSON.stringify({
      provider,
      timestamp: Date.now(), // Add current timestamp
    }),
  });
}

export function clearProviderFromCookies() {
  void deleteCookie(PROVIDER_COOKIE_KEY);
}
