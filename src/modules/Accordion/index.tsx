import React from 'react';

import FaqSchema from '@/modules/Accordion/FAQSchema';

import type { AccordionItem as AccordionItemType } from '@/apis/contentful/generated/types';

import Accordion from '@/components/Accordion';
import AccordionItem from '@/components/Accordion/AccordionItem';
import Headline from '@/components/Headline';
import type { HeadlineProps } from '@/components/Headline';
import ModuleWrapper from '@/components/ModuleWrapper';
import { RichText } from '@/components/RichText';

import type { ModuleCommonProps } from '@/types/moduleProps';
import type { RichTextLinks } from '@/types/pageProps';
import type { Theme } from '@/types/theme';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';

import { accordionContainerStyles, accordionHeadlineStyles } from './index.styles';

type AccordionModuleProps = {
  headline?: string;
  headlineTag?: HeadlineProps['intent'];
  centerHeadline?: boolean;
  items: AccordionItemType[];
  theme?: Theme;
} & ModuleCommonProps;

function AccordionModule({ internalName, headline, headlineTag, centerHeadline, items, theme }: AccordionModuleProps) {
  return (
    <ModuleWrapper
      internalName={internalName}
      moduleName={'accordion'}
      theme={theme}
      className={accordionContainerStyles()}
    >
      {headline && (
        <Headline
          type={headlineTag as HeadlineProps['intent']}
          intent="h2"
          className={accordionHeadlineStyles({ centerHeadline, theme })}
        >
          {headline}
        </Headline>
      )}

      <Accordion id={getSanitizedIdSelector(internalName)}>
        {items.map((item: AccordionItemType, index: number) => (
          <AccordionItem
            key={index}
            label={item.label ?? 'accordion-item'}
            value={getSanitizedIdSelector(item.label ?? '')}
            theme={theme}
          >
            {item?.content && (
              <RichText document={item.content.json} links={item.content.links as unknown as RichTextLinks} />
            )}
          </AccordionItem>
        ))}
      </Accordion>
      <FaqSchema items={items} />
    </ModuleWrapper>
  );
}

export default AccordionModule;
