import type { OfferList } from '@/apis/dg/types';

import AvailabilityCheckContact from '@/components/AvailabilityCheckContact';
import Button from '@/components/Button';
import Headline from '@/components/Headline';
import { NetzausbauLg } from '@/components/Icons/lg';
import { ArrowRightSm } from '@/components/Icons/sm';
import TextLink from '@/components/TextLink';

import useTracking from '@/hooks/useTracking';

import { useAvailabilityCheckStore } from '@/stores/AvailabilityCheckStore';
import { useUserStore } from '@/stores/UserStore';

import { getAvailabilityInfo } from '@/utils/availabilityCheck/getAvailabilityInfo';
import { getOfferCta } from '@/utils/availabilityCheck/getOfferCta';

import {
  availableAddressContainerStyles,
  availableAddressHeadlineStyles,
  availableAddressStyles,
  resultButtonContainerStyles,
  resultContainerStyles,
  resultContentContainerStyles,
  resultContentWrapperStyles,
  resultHeadlineRowStyles,
  resultIconStyles,
} from './index.styles';

type ResultProps = {
  offerList: OfferList | null;
};

export default function Result({ offerList }: ResultProps) {
  const { trackAcEvent } = useTracking();
  const setAcModalOpen = useAvailabilityCheckStore.use.setAcModalOpen();
  const setAcType = useAvailabilityCheckStore.use.setAcType();
  const getUserAddressAsString = useUserStore.use.getUserAddressAsString();
  const provider = useUserStore.use.provider();
  const userAddress = getUserAddressAsString();

  if (!offerList) return null;

  const { isAvailable } = getAvailabilityInfo(offerList);
  const { label, href } = getOfferCta(provider, offerList);

  function handleOpenAvailabilityCheck() {
    setAcType('page');
    trackAcEvent({ acType: 'page', acInteraction: 'open' });
    setAcModalOpen(true);
  }

  const renderHeadline = () => (
    <Headline type="p" intent="h4" id="availability-result-title">
      {isAvailable ? (
        <>
          Glasfaser <span className={resultHeadlineRowStyles()}>verfügbar für</span>
        </>
      ) : (
        <>
          <span className={resultHeadlineRowStyles()}>Ohje! Ein Glasfaser-Anschluss</span>
          ist derzeit nicht möglich.
        </>
      )}
    </Headline>
  );

  const renderAddress = () => (
    <div className={availableAddressContainerStyles()}>
      <span className={availableAddressHeadlineStyles()}>Ihre Adresse</span>
      <strong className={availableAddressStyles()}>{userAddress}</strong>
    </div>
  );

  const renderUnavailableMessage = () => <AvailabilityCheckContact className="md:max-w-[550px] xl:max-w-[400px]" />;

  const renderButtons = () => (
    <div className={resultButtonContainerStyles({ isAvailable })}>
      <TextLink onClick={handleOpenAvailabilityCheck} aria-label="Verfügbarkeitsergebnis anzeigen">
        Ergebnis anzeigen
      </TextLink>

      {isAvailable && <Button variant="primary" href={href} label={label} icon={<ArrowRightSm aria-hidden="true" />} />}
    </div>
  );

  return (
    <div className={resultContainerStyles({ isAvailable })}>
      <div className={resultContentWrapperStyles()}>
        <NetzausbauLg className={resultIconStyles()} aria-hidden="true" />
        <div className={resultContentContainerStyles()}>
          {renderHeadline()}
          {isAvailable ? renderAddress() : renderUnavailableMessage()}
        </div>
      </div>
      {renderButtons()}
    </div>
  );
}
