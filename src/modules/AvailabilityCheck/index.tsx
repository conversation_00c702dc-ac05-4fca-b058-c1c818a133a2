'use client';

import AddressNotFoundInfo from '@/components/AddressNotFoundInfo';
import AvailabilityCheckForm from '@/components/AvailabilityCheckForm';
import Headline from '@/components/Headline';
import type { HeadlineProps } from '@/components/Headline';
import LoadingWrapper from '@/components/LoadingWrapper';
import ModuleWrapper from '@/components/ModuleWrapper';

import { useUserStore } from '@/stores/UserStore';

import type { ModuleCommonProps } from '@/types/moduleProps';
import { Theme } from '@/types/theme';

import Result from './Result';
import {
  availabilityCheckFormWrapperStyles,
  availabilityCheckHeadlineStyles,
  availabilityCheckStyles,
} from './index.styles';

type AvailabilityCheckProps = {
  headline?: string;
  headlineTag?: HeadlineProps['intent'];
  theme?: Theme;
} & ModuleCommonProps;

export default function AvailabilityCheck({
  internalName,
  headline,
  headlineTag = 'h2',
  theme = Theme.Gray,
}: AvailabilityCheckProps) {
  const loading = useUserStore.use.loading();
  const offerList = useUserStore.use.offerList();
  const provider = useUserStore.use.provider();

  return (
    <ModuleWrapper
      internalName={internalName}
      moduleName="availability-check"
      className={availabilityCheckStyles({ theme })}
    >
      <LoadingWrapper loading={loading}>
        {offerList ? (
          <Result offerList={offerList} />
        ) : (
          <div className="w-full space-y-2 pb-4">
            <div className={availabilityCheckFormWrapperStyles()}>
              {headline && (
                <Headline type={headlineTag} intent="h4" className={availabilityCheckHeadlineStyles()}>
                  {headline}
                </Headline>
              )}

              <AvailabilityCheckForm id="ac-module" standAlone={true} openFlyout />
            </div>

            <AddressNotFoundInfo />
          </div>
        )}
      </LoadingWrapper>
    </ModuleWrapper>
  );
}
