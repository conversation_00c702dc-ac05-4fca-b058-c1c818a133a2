import React from 'react';

import { consultationAtHomeComponentStyles } from '@/modules/ConsultationAtHome/index.styles';
import { ConsultationAtHomeSteps } from '@/modules/ConsultationAtHome/types/consultationAtHomeSteps';
import type { StepComponentProps } from '@/modules/ConsultationAtHome/types/stepComponentProps';

import type { CmsData } from '@/apis/dg/types';

import AvailabilityCheckContact from '@/components/AvailabilityCheckContact';
import Button from '@/components/Button';
import Copy from '@/components/Copy';
import Headline from '@/components/Headline';
import TextLink from '@/components/TextLink';
import { textLinkStyles } from '@/components/TextLink/index.styles';

import useTracking from '@/hooks/useTracking';

import { useUserStore } from '@/stores/UserStore';

import { ProjectLinks } from '@/types/project';

import { INTERESTED_PERSON_URL } from '@/utils/specialUrls/specialUrlsCatalog';

export function AvailabilityCheckNegativeStep({ userAddress, offerList, setStep }: StepComponentProps) {
  const { title, description, buttonText } = (offerList?.address?.cms ?? {}) as CmsData;
  const resetOfferList = useUserStore.use.resetOfferList();
  const { trackEvent } = useTracking();

  function onClickChangeAddress(event: React.MouseEvent<HTMLButtonElement>) {
    event.preventDefault();
    resetOfferList();
    trackEvent({
      category: 'Button',
      action: 'click',
      element: event.currentTarget,
      content: 'Adresse ändern',
    });
    setStep(ConsultationAtHomeSteps.AvailabilityCheckExecution);
  }

  return (
    <div className={consultationAtHomeComponentStyles()}>
      <div className="grid grid-cols-1 md:grid-cols-[3fr_2fr]">
        <div className="flex flex-col gap-8 p-10 md:border-r md:border-gray-200">
          <Copy>An Ihrer Adresse kann derzeit kein Glasfaseranschluss gebucht werden.</Copy>

          <div className="space-y-2">
            {userAddress && (
              <Copy weight={'bold'}>
                Ihre geprüfte Adresse: <span data-hj-suppress="">{userAddress}</span>{' '}
              </Copy>
            )}

            <button
              className={textLinkStyles({ variant: 'primary', withIcon: false, fullWidth: false, disabled: false })}
              onClick={onClickChangeAddress}
            >
              Adresse ändern
            </button>
          </div>

          <AvailabilityCheckContact />
        </div>

        <div className="flex flex-col items-center gap-8 p-10 text-center">
          {title && (
            <Headline type="h3" intent="h4">
              {title}
            </Headline>
          )}

          {description && <div dangerouslySetInnerHTML={{ __html: description }}></div>}

          {offerList?.address?.cms?.isIndustrialPark === false && buttonText && (
            <Button href={INTERESTED_PERSON_URL} label={buttonText} />
          )}
        </div>
      </div>
    </div>
  );
}
