import { BusinessArea } from '@/app/consts';

import { consultationAtHomeComponentStyles } from '@/modules/ConsultationAtHome/index.styles';
import { ConsultationAtHomeSteps } from '@/modules/ConsultationAtHome/types/consultationAtHomeSteps';
import type { StepComponentProps } from '@/modules/ConsultationAtHome/types/stepComponentProps';

import Button from '@/components/Button';
import Copy from '@/components/Copy';

export function CustomerPromptStep({ setStep, offerList, businessArea }: StepComponentProps) {
  function onClickYes() {
    setStep(ConsultationAtHomeSteps.HotlineInfo);
  }

  function onClickNo() {
    if (offerList !== null && typeof offerList !== 'undefined') {
      if (
        offerList?.address?.isHappyFlow ||
        (businessArea === BusinessArea.Professional && offerList?.address?.cms?.isIndustrialParkHappyflow)
      ) {
        setStep(ConsultationAtHomeSteps.ContactForm);
      } else if (offerList?.address?.cms?.isIndustrialParkHappyflow) {
        setStep(ConsultationAtHomeSteps.ContactFormIndustrial);
      } else if (offerList?.address?.isHappyFlow === false) {
        setStep(ConsultationAtHomeSteps.AvailabilityCheckNegative);
      }
    } else {
      setStep(ConsultationAtHomeSteps.AvailabilityCheckPrompt);
    }
  }
  return (
    <div className={consultationAtHomeComponentStyles()}>
      <Copy className="text-center">
        Sind Sie bereits Kunde bei Deutsche Glasfaser{businessArea === BusinessArea.Professional && ' Business'}?
      </Copy>

      <div className="flex w-full flex-col items-center justify-center gap-6 md:flex-row">
        <Button label="Ja" type="button" onClick={onClickYes} />
        <Button label="Nein" type="button" onClick={onClickNo} />
      </div>
    </div>
  );
}
