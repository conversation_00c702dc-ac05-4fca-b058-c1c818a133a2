import { BusinessArea } from '@/app/consts';

import { consultationAtHomeComponentStyles } from '@/modules/ConsultationAtHome/index.styles';
import type { StepComponentProps } from '@/modules/ConsultationAtHome/types/stepComponentProps';

import Button from '@/components/Button';
import Copy from '@/components/Copy';
import Headline from '@/components/Headline';

import { PrivateHotline } from './PrivateHotline';

export function HotlineInfoStep({
  hotlinePrivate,
  hotlineProfessional,
  hotlineBusinessPublic,
  businessArea,
}: StepComponentProps) {
  return (
    <div className={consultationAtHomeComponentStyles()}>
      <Copy className="text-center">
        Bitten wenden Sie sich an unsere Hotline für Kunden. Da können wir Ihnen weiterhelfen.
      </Copy>

      {businessArea === BusinessArea.Private && <PrivateHotline hotlinePrivate={hotlinePrivate} />}

      {businessArea === BusinessArea.Professional && (
        <div className="grod-cols-1 grid gap-10 md:grid-cols-2">
          <div className="flex flex-col items-center justify-between gap-6">
            <Headline type="h3" intent="h5">
              DG professional
            </Headline>

            <Headline type="p" intent="h1" className="text-basalt">
              {hotlineProfessional}
            </Headline>

            <Button label="Anrufen" type="button" href={`tel:${hotlineProfessional}`} />
          </div>

          <div className="flex flex-col items-center justify-between gap-6">
            <Headline type="h3" intent="h5">
              DG business & DG public
            </Headline>

            <Headline type="p" intent="h1" className="text-basalt">
              {hotlineBusinessPublic}
            </Headline>

            <Button label="Anrufen" type="button" href={`tel:${hotlineBusinessPublic}`} />
          </div>
        </div>
      )}
    </div>
  );
}
