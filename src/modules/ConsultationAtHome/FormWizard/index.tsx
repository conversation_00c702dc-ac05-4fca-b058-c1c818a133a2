'use client';

import React, { useEffect, useState } from 'react';
import type { FunctionComponent } from 'react';

import { AC_BUTTON_LABEL, BusinessArea } from '@/app/consts';

import { AvailabilityCheckExecutionStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/AvailabilityCheckExecutionStep';
import { AvailabilityCheckNegativeStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/AvailabilityCheckNegativeStep';
import { AvailabilityCheckPromptStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/AvailabilityCheckPromptStep';
import { ContactFormIndustrialStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/ContactFormIndustrialStep';
import { ContactFormStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/ContactFormStep';
import { ContactFormSuccessStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/ContactFormSuccessStep';
import { CustomerPromptStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/CustomerPromptStep';
import { HotlineInfoStep } from '@/modules/ConsultationAtHome/FormWizard/FormStep/components/HotlineInfoStep';
import {
  consultationAtHomeProgressStyles,
  consultationAtHomeWizardStyles,
} from '@/modules/ConsultationAtHome/index.styles';
import type { StepComponentProps } from '@/modules/ConsultationAtHome/types/stepComponentProps';

import Headline from '@/components/Headline';
import { HomeCheckMd, HomePersonMd, PersonMd, TelefonieMd, ThumbMd } from '@/components/Icons/md';
import LoadingWrapper from '@/components/LoadingWrapper';
import TextLink from '@/components/TextLink';

import { useUserStore } from '@/stores/UserStore';

import type { FormField, FormGroup } from '@/types/forms';

import { ConsultationAtHomeSteps } from '../types/consultationAtHomeSteps';
import { getPhase } from '../utils/getPhase';

type ConsultationAtHomeStep = {
  [key in ConsultationAtHomeSteps]: {
    StepImage?: FunctionComponent;
    headline: string;
    headlineB2B?: string;
    progress: number;
    StepComponent: ({
      setStep,
      offerList,
      formElements,
      hotlinePrivate,
      hotlineProfessional,
      hotlineBusinessPublic,
      userAddress,
      businessArea,
    }: StepComponentProps) => JSX.Element;
  };
};

type ConsultationAtHomeWizardProps = {
  hotlinePrivate?: string;
  hotlineProfessional: string;
  hotlineBusinessPublic: string;
  businessArea: BusinessArea;
  formElements: (FormField | FormGroup)[];
};

export default function ConsultationAtHomeWizard({
  hotlinePrivate,
  hotlineProfessional,
  hotlineBusinessPublic,
  businessArea = BusinessArea.Private,
  formElements,
}: ConsultationAtHomeWizardProps) {
  const offerList = useUserStore.use.offerList();
  const loading = useUserStore.use.loading();
  const getUserAddressAsString = useUserStore.use.getUserAddressAsString();
  const resetOfferList = useUserStore.use.resetOfferList();

  const [step, setStep] = useState<ConsultationAtHomeSteps | null>(null);

  const userAddress = getUserAddressAsString();

  useEffect(() => {
    const consultationAtHomeDone = sessionStorage.getItem('ConsultationAtHomeDone') === 'true';
    if (consultationAtHomeDone) return;

    setStep(ConsultationAtHomeSteps.CustomerPrompt);
  }, []);

  useEffect(() => {
    if (step === null) return;

    if (step === ConsultationAtHomeSteps.AvailabilityCheckExecution) {
      const isHappyFlowIndustrialPark = offerList?.address?.cms?.isIndustrialParkHappyflow;
      const isHappyFlow =
        offerList?.address?.isHappyFlow || (businessArea === BusinessArea.Professional && isHappyFlowIndustrialPark);

      if (isHappyFlow) {
        setStep(ConsultationAtHomeSteps.ContactForm);
      } else if (isHappyFlowIndustrialPark) {
        setStep(ConsultationAtHomeSteps.ContactFormIndustrial);
      } else if (offerList?.address?.isHappyFlow === false) {
        setStep(ConsultationAtHomeSteps.AvailabilityCheckNegative);
      }
    }

    if (step === ConsultationAtHomeSteps.ContactFormSuccess) {
      sessionStorage.setItem('ConsultationAtHomeDone', 'true');
      const businessPrefix = businessArea === BusinessArea.Private ? 'B2C' : 'B2B';
      if (typeof window.dataLayer !== 'undefined') {
        window.dataLayer.push({
          event: `${businessPrefix}-Beratung-${getPhase(offerList) || 'Fallback'}`,
        });
      }
    }
  }, [businessArea, offerList, step]);

  if (step === null) return;

  const consultationSteps: ConsultationAtHomeStep = {
    [ConsultationAtHomeSteps.CustomerPrompt]: {
      StepImage: PersonMd,
      headline: 'Gerne beraten wir Sie auch zu Hause.',
      headlineB2B: 'Gerne beraten wir Sie auch persönlich.',
      progress: 20,
      StepComponent: CustomerPromptStep,
    },
    [ConsultationAtHomeSteps.HotlineInfo]: {
      StepImage: TelefonieMd,
      headline: 'Sie sind Kunde bei Deutsche Glasfaser.',
      headlineB2B: 'Sie sind Kunde bei Deutsche Glasfaser Business.',
      progress: 100,
      StepComponent: HotlineInfoStep,
    },
    [ConsultationAtHomeSteps.AvailabilityCheckPrompt]: {
      StepImage: HomeCheckMd,
      headline: 'Vereinbaren Sie Ihren persönlichen Beratungstermin.',
      progress: 50,
      StepComponent: AvailabilityCheckPromptStep,
    },
    [ConsultationAtHomeSteps.AvailabilityCheckExecution]: {
      StepImage: HomeCheckMd,
      headline: AC_BUTTON_LABEL,
      progress: 50,
      StepComponent: AvailabilityCheckExecutionStep,
    },
    [ConsultationAtHomeSteps.ContactForm]: {
      StepImage: ThumbMd,
      headline: 'Gute Neuigkeiten!',
      progress: 80,
      StepComponent: ContactFormStep,
    },
    [ConsultationAtHomeSteps.ContactFormIndustrial]: {
      StepImage: ThumbMd,
      headline: 'Für diese Adresse im Gewerbegebiet ist Glasfaser verfügbar bzw. der Ausbau geplant.',
      progress: 80,
      StepComponent: ContactFormIndustrialStep,
    },
    [ConsultationAtHomeSteps.AvailabilityCheckNegative]: {
      headline: 'Eine Beratung Zuhause können wir nicht anbieten.',
      headlineB2B: 'Eine Beratung können wir nicht anbieten.',
      progress: 100,
      StepComponent: AvailabilityCheckNegativeStep,
    },
    [ConsultationAtHomeSteps.ContactFormSuccess]: {
      StepImage: HomePersonMd,
      headline: 'Vielen Dank für Ihre Anfrage.',
      progress: 100,
      StepComponent: ContactFormSuccessStep,
    },
  };

  const { StepImage, headline, headlineB2B, StepComponent } = consultationSteps[step];
  const { street, houseNumber, zipcode, city } = offerList?.address || {};

  const excludedSteps: ConsultationAtHomeSteps[] = [
    ConsultationAtHomeSteps.CustomerPrompt,
    ConsultationAtHomeSteps.ContactFormSuccess,
    ConsultationAtHomeSteps.AvailabilityCheckNegative,
  ];

  function onClickBack(e: React.MouseEvent<HTMLAnchorElement>) {
    e.preventDefault();
    if (step === ConsultationAtHomeSteps.ContactForm) {
      resetOfferList();
      setStep(ConsultationAtHomeSteps.AvailabilityCheckExecution);
    } else {
      setStep(
        step === ConsultationAtHomeSteps.HotlineInfo || step === ConsultationAtHomeSteps.AvailabilityCheckPrompt
          ? ConsultationAtHomeSteps.CustomerPrompt
          : ConsultationAtHomeSteps.AvailabilityCheckPrompt,
      );
    }
  }

  return (
    <LoadingWrapper loading={loading}>
      <div className={consultationAtHomeWizardStyles()}>
        {StepImage && <StepImage />}

        <Headline intent="h2" className="text-center">
          {businessArea === BusinessArea.Professional && headlineB2B ? headlineB2B : headline}
        </Headline>

        <StepComponent
          userAddress={userAddress}
          offerList={offerList}
          hotlinePrivate={hotlinePrivate}
          hotlineProfessional={hotlineProfessional}
          hotlineBusinessPublic={hotlineBusinessPublic}
          setStep={setStep}
          formElements={street && houseNumber && zipcode && city ? formElements : undefined}
          businessArea={businessArea}
        />

        <progress className={consultationAtHomeProgressStyles()} max="100" value={consultationSteps[step].progress} />

        {!excludedSteps.includes(step) && <TextLink onClick={onClickBack}>Zurück</TextLink>}
      </div>
    </LoadingWrapper>
  );
}
