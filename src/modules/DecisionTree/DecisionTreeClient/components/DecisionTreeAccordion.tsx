'use client';

import type { AccordionItem as AccordionItemType } from '@/apis/contentful/generated/types';
import type { AccordionStep } from '@/apis/contentful/getDecisionStep';

import Accordion from '@/components/Accordion';
import AccordionItem from '@/components/Accordion/AccordionItem';
import { RichText } from '@/components/RichText';

import type { RichTextLinks } from '@/types/pageProps';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';

type DecisionTreeAccordionProps = {
  step: AccordionStep;
};

export default function DecisionTreeAccordion({ step }: DecisionTreeAccordionProps) {
  return (
    <Accordion id={getSanitizedIdSelector(step.internalName)}>
      {step.items.map((item: AccordionItemType, index: number) => (
        <AccordionItem
          key={index}
          label={item.label ?? 'accordion-item'}
          value={getSanitizedIdSelector(item.label ?? '')}
        >
          {item?.content && (
            <RichText document={item.content.json} links={item.content.links as unknown as RichTextLinks} />
          )}
        </AccordionItem>
      ))}
    </Accordion>
  );
}
