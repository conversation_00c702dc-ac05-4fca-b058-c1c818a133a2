import Image from 'next/image';

import Chip from '@/components/Chip';

import type { DecisionHistory } from '..';

type DecisionTreeBreadcrumbProps = {
  history: DecisionHistory[];
};

export default function DecisionTreeBreadcrumb({ history }: DecisionTreeBreadcrumbProps) {
  return (
    <div className="flex min-h-16 w-full flex-col flex-wrap justify-center gap-4 xl:flex-row">
      <span className="sr-only">Bereits ausgewählte Optionen:</span>
      {history.map((item, index) => (
        <Chip
          key={index}
          label={item.selection.title}
          color="white"
          leadingIcon={
            item.selection.icon && (
              <Image src={item.selection.icon.url} alt={item.selection.icon.description || ''} width={48} height={48} />
            )
          }
          alignLeft={true}
        />
      ))}
    </div>
  );
}
