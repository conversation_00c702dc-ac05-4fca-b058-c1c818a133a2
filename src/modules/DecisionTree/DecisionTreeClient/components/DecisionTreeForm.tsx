import { useEffect, useRef } from 'react';

import Copy from '@/components/Copy';
import FormBuilder from '@/components/FormBuilder';
import Headline from '@/components/Headline';
import { RichText } from '@/components/RichText';
import TextLink from '@/components/TextLink';

import type { Form } from '@/types/forms';
import type { RichTextLinks } from '@/types/pageProps';

type DecisionTreeFormProps = {
  step: Form;
  onReset: () => void;
  lastSelectionTitle?: string;
};

export default function DecisionTreeForm({ step, onReset, lastSelectionTitle }: DecisionTreeFormProps) {
  const formContainerRef = useRef<HTMLDivElement>(null);

  // Focus first input when component mounts
  useEffect(() => {
    if (formContainerRef.current) {
      const firstInput = formContainerRef.current?.querySelector('input, textarea, select');
      if (firstInput instanceof HTMLElement) {
        firstInput.focus();
      }
    }
  }, []);

  return (
    <div ref={formContainerRef} className="flex flex-col items-center gap-4">
      <TextLink onClick={onReset}>Auswahl ändern</TextLink>
      {step.headline && (
        <Headline type={'h3'} intent="h2">
          {step.headline}
        </Headline>
      )}
      {/* introText is deprecated and should only be visible if introTextRt is not set */}
      {!step.introTextRt && step.introText && <Copy>{step.introText}</Copy>}
      {step.introTextRt && (
        <RichText document={step.introTextRt.json} links={step.introTextRt.links as unknown as RichTextLinks} />
      )}
      <FormBuilder
        formType={step.formType}
        formElements={step.formElements}
        recipient={step.recipient}
        subject={lastSelectionTitle}
      />
    </div>
  );
}
