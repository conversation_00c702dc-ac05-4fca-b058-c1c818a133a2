import { useEffect, useRef, useState } from 'react';

import Image from 'next/image';

import type { DecisionStep, DecisionStepOption } from '@/apis/contentful/getDecisionStep';

import Button from '@/components/Button';
import Copy from '@/components/Copy';
import GridSelect from '@/components/GridSelect';
import Select from '@/components/Select';

import useTracking from '@/hooks/useTracking';

import type { InternalLink } from '@/types/internalLink';
import type { SelectOption } from '@/types/selectOption';
import type { TrackingAction } from '@/types/tracking';

import { getSanitizedIdSelector } from '@/utils/getSanitizedIdSelector';

// Type guard to check if a step is an internal link
function isInternalLink(step: unknown): step is InternalLink {
  return !!step && typeof (step as InternalLink).href === 'string';
}

type DecisionTreeSelectionProps = {
  step: DecisionStep;
  onNext: (option: DecisionStepOption) => void;
};

export default function DecisionTreeSelection({ step, onNext }: DecisionTreeSelectionProps) {
  const { trackEvent } = useTracking();

  const [selectedValue, setSelectedValue] = useState<string>('');
  const [selectedOption, setSelectedOption] = useState<DecisionStepOption | null>(null);
  const [isActive, setIsActive] = useState(false);

  const selectRef = useRef<HTMLSelectElement>(null);
  const gridSelectContainerRef = useRef<HTMLDivElement>(null);

  const { internalName, question, label, variant } = step;
  const trackingId = `${getSanitizedIdSelector(label)}+${selectedValue}`;

  // Reset selection state when step changes
  useEffect(() => {
    setSelectedValue('');
    setSelectedOption(null);
    setIsActive(false);
  }, [step.internalName]);

  // Focus management when step changes (not on initial mount)
  const previousStepRef = useRef<string | null>(null);

  useEffect(() => {
    // Only focus if this is not the initial mount (step has changed)
    if (previousStepRef.current !== null && previousStepRef.current !== step.internalName) {
      const focusElement = () => {
        if (variant === 'select' && selectRef.current) {
          // For Select component, find the focusable input div in the same container
          const selectContainer = selectRef.current.parentElement;
          const focusableInput = selectContainer?.querySelector('div[tabindex="0"]') as HTMLElement;
          if (focusableInput) {
            focusableInput.focus();
          }
        } else if (variant !== 'select' && gridSelectContainerRef.current) {
          // For GridSelect component, focus the first focusable option
          const firstFocusableOption = gridSelectContainerRef.current.querySelector(
            '[role="radio"][tabindex="0"]',
          ) as HTMLElement;
          if (firstFocusableOption) {
            firstFocusableOption.focus();
          }
        }
      };

      // Use a small delay to ensure the DOM is fully rendered
      const timeoutId = setTimeout(focusElement, 100);

      return () => clearTimeout(timeoutId);
    }

    // Update the previous step reference
    previousStepRef.current = step.internalName;
  }, [variant, step.internalName]);

  // Convert DecisionStepOptions to SelectOptions
  function getSelectOptions(): SelectOption[] {
    return step.options.map((option) => ({
      value: getSanitizedIdSelector(option.title),
      label: option.title,
      icon: option.icon ? (
        <Image src={option.icon.url} alt={option.icon.description || ''} width={48} height={48} />
      ) : undefined,
    }));
  }

  function handleEvent(event: React.SyntheticEvent<HTMLElement>, action: TrackingAction, value?: string) {
    const content = label || 'unlabeled-step';

    switch (action) {
      case 'view':
        if (isActive) {
          setIsActive(false);
          return;
        }
        setIsActive(true);
        trackEvent({
          category: 'Form',
          action,
          element: event.currentTarget,
          content,
          value,
        });
        break;
      default:
        trackEvent({
          category: 'Form',
          action,
          element: event.currentTarget,
          content,
          value,
        });
        if (action === 'select') {
          setIsActive(false);
        }
        break;
    }
  }

  function handleSelection(event: React.ChangeEvent<HTMLSelectElement>) {
    const selectedValue = event.target.value;

    // Don't proceed if no value is selected
    if (!selectedValue || selectedValue === '') {
      setSelectedValue('');
      setSelectedOption(null);
      return;
    }

    const option = step.options.find((opt) => getSanitizedIdSelector(opt.title) === selectedValue);
    if (!option) {
      console.warn('Selected option not found in step options:', selectedValue);
      setSelectedValue('');
      setSelectedOption(null);
      return;
    }

    handleEvent(event, 'select', selectedValue);
    setSelectedValue(selectedValue);
    setSelectedOption(option);
  }

  function handleNext() {
    if (!selectedOption) return;
    onNext(selectedOption);
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Copy>Bitte wählen Sie aus:</Copy>
        {question && <Copy>{question}</Copy>}
      </div>
      {variant === 'select' ? (
        <Select
          ref={selectRef}
          label={label}
          options={getSelectOptions()}
          value={selectedValue}
          onChange={handleSelection}
          onKeyUp={(e) => e.key === 'Tab' && handleEvent(e, 'view')}
          onClick={(e) => handleEvent(e, 'view')}
        />
      ) : (
        <div ref={gridSelectContainerRef}>
          <GridSelect
            id={getSanitizedIdSelector(internalName)}
            label={label}
            options={getSelectOptions()}
            value={selectedValue}
            onChange={handleSelection}
          />
        </div>
      )}

      <div className="flex justify-center">
        {selectedOption && isInternalLink(selectedOption.nextStep) ? (
          <Button label={selectedOption.nextStep.label} variant="secondary" href={selectedOption.nextStep.href} />
        ) : (
          <div data-tracking-id={trackingId}>
            <Button label="Nächster Schritt" variant="secondary" onClick={handleNext} disabled={!selectedOption} />
          </div>
        )}
      </div>
    </div>
  );
}
