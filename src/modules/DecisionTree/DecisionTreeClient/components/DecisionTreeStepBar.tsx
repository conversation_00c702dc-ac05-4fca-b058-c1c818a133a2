interface StepBarProps {
  totalSteps: number;
  currentStep: number;
}

export default function StepBar({ totalSteps, currentStep }: StepBarProps) {
  return (
    <nav aria-label="Fortschrittsanzeige">
      <ol className="m-0 flex list-none items-center justify-center gap-2 p-0">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <li key={index} className="flex items-center">
            <div
              className={`h-2 w-8 rounded-[5px] ${index === currentStep ? 'bg-hover' : 'bg-gray-300'}`}
              aria-hidden="true"
            />
            <span className="sr-only">
              {index === currentStep && `Sie sind hier: Schritt ${index + 1} von ${totalSteps}`}
            </span>
          </li>
        ))}
      </ol>
    </nav>
  );
}
