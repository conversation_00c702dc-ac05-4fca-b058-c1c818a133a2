'use client';

import React, { useEffect, useRef, useState } from 'react';

import Button from '@/components/Button';
import { CloseSm, SearchSm } from '@/components/Icons/sm';
import TextInput from '@/components/TextInput';

import useTracking from '@/hooks/useTracking';

import { useJobFilterStore } from '@/stores/JobFilterStore';

import { buttonStyles, containerStyles, inputStyles, inputWrapperStyles, resetButtonStyles } from './index.styles';

type SearchInputProps = {
  idPrefix: string;
};

// Smartrecruiters API requires a minimum of 4 characters to search
const minLength = 4;

export default function SearchInputWithButton({ idPrefix }: SearchInputProps) {
  const searchTerm = useJobFilterStore.use.searchTerm();
  const search = useJobFilterStore.use.search();
  const { trackEvent } = useTracking();
  const [value, setValue] = useState(searchTerm);
  const [invalid, setInvalid] = useState(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [hasFocused, setHasFocused] = useState<boolean>(false);
  const searchInputRef = useRef(null);

  const handleSubmit = () => {
    if (value.length > 0 && value.length < minLength) {
      setInvalid(true);
    } else if (searchTerm !== value) {
      search(value);
      setInvalid(false);
    }
  };

  function onReset(event: React.MouseEvent<HTMLButtonElement>) {
    setValue('');
    setInvalid(false);
    if (searchTerm !== '') {
      search('');
    }

    trackEvent({ category: 'Button', action: 'click', element: event.currentTarget, content: 'zuruecksetzen' });
  }

  function onChange(event: React.ChangeEvent<HTMLInputElement>) {
    const newValue = event.target.value;
    setValue(newValue);

    if (!isEditing) {
      setIsEditing(true);
      trackEvent({ category: 'Input', action: 'edit', element: event.currentTarget, content: 'suche-eingabe' });
    }

    if (newValue.length >= minLength) {
      setInvalid(false);
    }
  }

  function onFocus(event: React.FocusEvent<HTMLInputElement>) {
    if (!hasFocused) {
      setHasFocused(true);
      trackEvent({ category: 'Input', action: 'view', element: event.currentTarget, content: 'suche-eingabe' });
    }
  }

  function onBlur() {
    setTimeout(() => {
      setIsEditing(false);
      setHasFocused(false);
    }, 150);
  }

  useEffect(() => {
    const unsubscribe = useJobFilterStore.subscribe(
      (state) => state.searchTerm,
      (newSearchTerm) => setValue(newSearchTerm),
    );

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, []);

  return (
    <form data-tracking-id="job-search" action={handleSubmit} className={containerStyles()}>
      <div className={inputWrapperStyles()}>
        {value.length > 0 && (
          <button type="reset" className={resetButtonStyles()} onClick={onReset}>
            <span className={'sr-only'}>Eingabe löschen</span>
            <CloseSm />
          </button>
        )}
        <TextInput
          label={'Was willst du tun?'}
          labelHidden
          ref={searchInputRef}
          id={`${idPrefix}job-search`}
          type={'search'}
          name={'job-search'}
          placeholder={'Was willst du tun?'}
          className={inputStyles()}
          inputClassName={value.length > 0 ? 'pl-14' : ''}
          invalid={invalid}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          value={value}
        />
        <Button
          label="Suche starten"
          labelHidden
          className={buttonStyles()}
          icon={<SearchSm width={24} height={24} />}
          variant={'primary'}
          type="submit"
          onClick={handleSubmit}
        />
      </div>
      {invalid && (
        <p className={'pt-4 pl-1 text-[14px] leading-[150%] text-error'}>
          Bitte geben Sie mind. {minLength} Zeichen ein.
        </p>
      )}
    </form>
  );
}
