'use client';

import React from 'react';

import type { MaintenanceType } from '@/modules/MaintenanceOverview';
import {
  maintenanceTableContainerStyles,
  noResultsTextStyles,
} from '@/modules/MaintenanceOverview/components/MaintenanceTable.styles';

import Copy from '@/components/Copy';
import DataTable from '@/components/DataTable';

import { useMaintenanceStoreBase } from '@/stores/MaintenanceStore';

import { getDataTableContent } from '../utils';

type MaintenanceTableProps = {
  type: MaintenanceType;
};

export default function MaintenanceTable({ type }: MaintenanceTableProps) {
  const { filteredDisruptions } = useMaintenanceStoreBase();

  // Get the data table content for disruptions (Störung)
  const data = getDataTableContent({ disruptionsData: filteredDisruptions, type });

  // If tableBody is empty, handle that case
  if (!data.content.tableBody || data.content.tableBody.length === 0) {
    return (
      <div className={maintenanceTableContainerStyles()}>
        <Copy className={noResultsTextStyles()}>Keine Störungen gefunden.</Copy>
      </div>
    );
  }

  return (
    <div className={maintenanceTableContainerStyles()}>
      <DataTable tableHeaders={data.content.tableHeaders} tableBody={data.content.tableBody} />
    </div>
  );
}
