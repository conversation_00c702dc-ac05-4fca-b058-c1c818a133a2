import type { Meta, StoryObj } from '@storybook/react';
import { HttpResponse, http } from 'msw';

import MaintenanceOverview from '@/modules/MaintenanceOverview';

import { ApiType } from '@/apis/dg/consts';

import disruptionsDataMock from './mocks/disruptionsApiMock.json';
import longDisruptionsDataMock from './mocks/longDisruptionsApiMock.json';

const { DG_HOST_CMS } = process.env;

const meta: Meta<typeof MaintenanceOverview> = {
  title: 'Modules/MaintenanceOverview',
  component: MaintenanceOverview,
  tags: ['autodocs'],
  parameters: {
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/service/stoerungen-und-wartung',
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof MaintenanceOverview>;

export const Default: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json(disruptionsDataMock.data);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};

export const Long: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json(longDisruptionsDataMock.data);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};

export const EmptyMaintenanceData: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json([
            {
              category: 'Störung',
              area: ['Niedersachsen'],
              subject: 'Störung TV, Internet, Festnetz: Niedersachsen / Wolfsburg',
              description:
                '<p>Betroffen: Privatkunden | Gesch&auml;ftskunden<br />\r\nBetroffenes Gebiet: Niedersachsen / Wolfsburg</p>\r\n\r\n<p>St&ouml;rungsbeginn: 17.11.2023, 08:35 Uhr<br />\r\nErwartete L&ouml;sungszeit: unbekannt<br />\r\n<br />\r\nIm Gebiet Niedersachsen / Wolfsburg verzeichnen wir eine St&ouml;rung des Glasfasernetzes. Deshalb kann es derzeit bei den Diensten Internet und Telefon zu Beeintr&auml;chtigungen und vor&uuml;bergehenden Ausf&auml;llen kommen.<br />\r\n<br />\r\nUnsere Teams und Dienstleister sind im Einsatz und ermitteln aktuell die Ursache der St&ouml;rung.<br />\r\n&nbsp;<br />\r\nVielen Dank f&uuml;r Ihr Verst&auml;ndnis.<br />\r\n&nbsp;<br />\r\nMit freundlichen Gr&uuml;&szlig;en Ihr Team von Deutsche Glasfaser</p>',
              affected_products: ['Festnetz', 'Internet', 'TV'],
              start: '2023-11-17T08:35:00+01:00',
              end: null,
              updatedAt: '2023-11-21T08:13:29.020918+01:00',
            },
          ]);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};

export const EmptyDisruptionsData: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json([
            {
              category: 'Wartung',
              area: ['Niedersachsen'],
              subject: 'Internet, Festnetz: Niedersachsen / Wolfsburg',
              description:
                '<p>Betroffen: Privatkunden | Gesch&auml;ftskunden<br />\r\nBetroffenes Gebiet: Niedersachsen / Wolfsburg</p>\r\n\r\n<p>St&ouml;rungsbeginn: 17.11.2023, 08:35 Uhr<br />\r\nErwartete L&ouml;sungszeit: unbekannt<br />\r\n<br />\r\nIm Gebiet Niedersachsen / Wolfsburg verzeichnen wir eine St&ouml;rung des Glasfasernetzes. Deshalb kann es derzeit bei den Diensten Internet und Telefon zu Beeintr&auml;chtigungen und vor&uuml;bergehenden Ausf&auml;llen kommen.<br />\r\n<br />\r\nUnsere Teams und Dienstleister sind im Einsatz und ermitteln aktuell die Ursache der St&ouml;rung.<br />\r\n&nbsp;<br />\r\nVielen Dank f&uuml;r Ihr Verst&auml;ndnis.<br />\r\n&nbsp;<br />\r\nMit freundlichen Gr&uuml;&szlig;en Ihr Team von Deutsche Glasfaser</p>',
              affected_products: ['Festnetz', 'Internet'],
              start: '2023-11-17T08:35:00+01:00',
              end: null,
              updatedAt: '2023-11-21T08:13:29.020918+01:00',
            },
          ]);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};

export const EmptyData: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json([]);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};

export const UndefinedData: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(`${DG_HOST_CMS}${ApiType.DISRUPTIONS}`, () => {
          return HttpResponse.json(undefined);
        }),
      ],
    },
  },
  render: () => <MaintenanceOverview internalName={'maintenance-overview'} />,
};
