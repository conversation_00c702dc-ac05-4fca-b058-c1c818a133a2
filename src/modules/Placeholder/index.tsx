import { Suspense } from 'react';

import AlgoliaSearch from '@/modules/AlgoliaSearch';
import AreaSites from '@/modules/AreaSites';
import DeviceSearch from '@/modules/DeviceSearch';
import Jobportal from '@/modules/Jobportal';
import MaintenanceOverview from '@/modules/MaintenanceOverview';
import RouterPosition from '@/modules/RouterPosition';
import Shopfinder from '@/modules/Shopfinder';
import SpeedTest from '@/modules/SpeedTest';
import TariffDetails from '@/modules/TariffDetails';
import { WinterQuizPrizes, WinterQuizQuestions } from '@/modules/TemporaryModules';
import Trustpilot from '@/modules/Trustpilot';

import { type HeadlineProps } from '@/components/Headline';

import type { ModuleCommonProps } from '@/types/moduleProps';

import { isDevEnv } from '@/utils/isDevEnv';

import type { PlaceholderType } from './types';

type PlaceholderProps = ModuleCommonProps & {
  headline?: string;
  headlineTag?: HeadlineProps['intent'];
  placeholderType?: PlaceholderType;
};

export default function Placeholder({ internalName, headline, headlineTag, placeholderType }: PlaceholderProps) {
  const props = { internalName, headline, headlineTag };

  switch (placeholderType) {
    case 'AlgoliaSearch':
      return (
        <Suspense fallback={<div>Lade Suchergebnisse...</div>}>
          <AlgoliaSearch {...props} />
        </Suspense>
      );
    case 'AreaSites':
      return <AreaSites {...props} />;
    case 'DeviceSearch':
      return <DeviceSearch {...props} />;
    case 'Jobportal':
      return <Jobportal {...props} />;
    case 'MaintenanceOverview':
      return <MaintenanceOverview {...props} />;
    case 'RouterPosition':
      return <RouterPosition {...props} />;
    case 'Shopfinder':
      return <Shopfinder {...props} />;
    case 'SpeedTest':
      return <SpeedTest {...props} />;
    case 'TariffDetailsDGP':
      return <TariffDetails {...props} />;
    case 'Trustpilot':
      return <Trustpilot {...props} />;
    case 'WinterQuizQuestions':
      return <WinterQuizQuestions {...props} />;
    case 'WinterQuizPrizes':
      return <WinterQuizPrizes {...props} />;
    default:
      console.error('Unknown placeholder type:', placeholderType);
      return !isDevEnv ? null : (
        <div className="border-2 border-dashed p-10 text-gray-500">Unknown placeholder type: {placeholderType}</div>
      );
  }
}
