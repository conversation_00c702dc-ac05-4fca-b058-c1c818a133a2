export type PlaceholderType =
  | 'AlgoliaSearch'
  | 'AreaSites'
  | 'DeviceSearch'
  | 'Jobportal'
  | 'MaintenanceOverview'
  | 'RouterPosition'
  | 'Shopfinder'
  | 'SpeedTest'
  | 'TariffDetailsDGP'
  | 'Trustpilot'
  | 'WinterQuizQuestions'
  | 'WinterQuizPrizes';

// Type guard for PlaceholderType
export function isPlaceholderType(value: unknown): value is PlaceholderType {
  if (typeof value !== 'string') return false;
  switch (value) {
    case 'AlgoliaSearch':
    case 'AreaSites':
    case 'DeviceSearch':
    case 'Jobportal':
    case 'MaintenanceOverview':
    case 'RouterPosition':
    case 'Shopfinder':
    case 'SpeedTest':
    case 'TariffDetailsDGP':
    case 'Trustpilot':
    case 'WinterQuizQuestions':
    case 'WinterQuizPrizes':
      return true;
    default:
      return false;
  }
}
