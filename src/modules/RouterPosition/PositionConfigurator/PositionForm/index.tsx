import React, { useEffect, useState } from 'react';

import Button from '@/components/Button';
import FormGroup from '@/components/FormGroup';
import Headline from '@/components/Headline';
import RadioButton from '@/components/RadioButton';
import Select from '@/components/Select';

import useTracking from '@/hooks/useTracking';

import type { SelectOption } from '@/types/selectOption';
import type { TrackingAction } from '@/types/tracking';

const livingSpaceOptions: SelectOption[] = [
  { label: 'bis 70 qm', value: 'upTo70' },
  { label: '71 bis 120 qm', value: 'from71To120' },
  { label: '121 bis 180 qm', value: 'from121To180' },
  { label: 'ab 181 qm', value: 'above181' },
];

const floorOptionMapping: Record<string, SelectOption[]> = {
  from71To120: [
    { label: '1 Etage', value: 'oneFloor' },
    { label: '2 Etagen', value: 'twoFloors' },
  ],
  from121To180: [
    { label: '2 Etagen und Keller', value: 'twoFloorsBasement' },
    { label: '3 Etagen', value: 'threeFloors' },
    { label: '3 Etagen und Keller', value: 'threeFloorsBasement' },
  ],
  above181: [
    { label: '3 Etagen', value: 'threeFloors' },
    { label: '3 Etagen und Keller', value: 'threeFloorsBasement' },
  ],
};

// Mapping for tracking - converts English values to German for analytics
const trackingValueMap = {
  livingSpace: {
    upTo70: 'bis70',
    from71To120: '71bis120',
    from121To180: '121bis180',
    above181: 'ab181',
  },
  floors: {
    oneFloor: '1',
    twoFloors: '2',
    twoFloorsBasement: '2undkeller',
    threeFloors: '3',
    threeFloorsBasement: '3undkeller',
  },
  position: {
    central: 'zentral',
    basement: 'keller',
  },
};

type PositionFormValues = {
  livingSpace: string;
  floors: string;
  position: string;
};

type PositionFormProps = {
  onSubmit: (values: PositionFormValues) => void;
};

function PositionForm({ onSubmit }: PositionFormProps) {
  const { trackEvent } = useTracking();

  const [livingSpace, setLivingSpace] = useState('');
  const [floors, setFloors] = useState('');
  const [position, setPosition] = useState('central');

  useEffect(() => {
    if (livingSpace === 'upTo70') {
      setFloors('');
      setPosition('central');
    } else if (!floorOptionMapping[livingSpace]) {
      setFloors('');
      setPosition('central');
    } else if (livingSpace && floors && !floorOptionMapping[livingSpace]?.some((opt) => opt.value === floors)) {
      setFloors('');
      setPosition('central');
    }
  }, [livingSpace, floors]);

  function handleEvent(event: React.SyntheticEvent, action: TrackingAction, content: string, value?: string) {
    trackEvent({ category: 'Form', action, element: event.currentTarget as HTMLElement, content, value });
  }

  function handleLivingSpaceChange(event: React.ChangeEvent<HTMLSelectElement>) {
    setLivingSpace(event.target.value);
    const trackingValue = trackingValueMap.livingSpace[event.target.value as keyof typeof trackingValueMap.livingSpace];
    handleEvent(event, 'select', 'wohnflaeche', trackingValue);
  }

  function handleFloorsChange(event: React.ChangeEvent<HTMLSelectElement>) {
    setFloors(event.target.value);
    const trackingValue = trackingValueMap.floors[event.target.value as keyof typeof trackingValueMap.floors];
    handleEvent(event, 'select', 'etagenanzahl', trackingValue);
  }

  function handlePositionChange(event: React.ChangeEvent<HTMLInputElement>) {
    setPosition(event.target.value);
    const trackingValue = trackingValueMap.position[event.target.value as keyof typeof trackingValueMap.position];
    handleEvent(event, 'edit', 'standort', trackingValue);
  }

  function isFormValid() {
    if (!livingSpace) return false;
    return !(livingSpace !== 'upTo70' && !floors);
  }

  function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    if (!isFormValid()) return;
    onSubmit({ livingSpace, floors, position });
  }

  return (
    <>
      <Headline intent={'h3'} type={'h3'}>
        Ermitteln Sie den optimalen Standort für Ihre Geräte
      </Headline>
      <form className="mt-4 lg:max-w-[460px]" onSubmit={handleSubmit}>
        <Select
          id="livingSpace"
          label="Wie viel Wohnfläche nutzen Sie?"
          options={livingSpaceOptions}
          value={livingSpace}
          onChange={handleLivingSpaceChange}
          onClick={(e) => handleEvent(e, 'view', 'wohnflaeche')}
          onKeyUp={(e) => (e.key === 'Tab' ? handleEvent(e, 'view', 'wohnflaeche') : undefined)}
          placeholder="Bitte auswählen"
          required
        />
        {livingSpace && livingSpace !== 'upTo70' && (
          <Select
            id="floors"
            label="Wie viele Etagen haben Sie?"
            options={floorOptionMapping[livingSpace] || []}
            value={floors}
            onChange={handleFloorsChange}
            onClick={(e) => handleEvent(e, 'view', 'etagen')}
            onKeyUp={(e) => (e.key === 'Tab' ? handleEvent(e, 'view', 'etagen') : undefined)}
            placeholder="Bitte auswählen"
            required
          />
        )}
        {floors === 'threeFloorsBasement' && (
          <FormGroup
            label="Wo steht Ihr Router bzw. wo wird Ihr Router stehen?"
            direction="row"
            isRadioGroup={true}
            id="radio-group-position"
          >
            <RadioButton
              name="position"
              id="position-central"
              label="Router zentral im Wohnraum"
              value="central"
              checked={position === 'central'}
              onChange={handlePositionChange}
            />
            <RadioButton
              name="position"
              id="position-basement"
              label="Router im Keller am HÜP"
              value="basement"
              checked={position === 'basement'}
              onChange={handlePositionChange}
            />
          </FormGroup>
        )}
        <div className="mt-6">
          <Button type="submit" label="Ermitteln" variant="primary" disabled={!isFormValid()} />
        </div>
      </form>
    </>
  );
}

export default PositionForm;
