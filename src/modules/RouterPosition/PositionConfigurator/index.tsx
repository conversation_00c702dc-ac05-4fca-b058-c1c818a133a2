'use client';

import React, { useState } from 'react';

import Image from 'next/image';

import { containerContentStyles, containerImageStyles, containerStyles } from '@/modules/TextImageTeaser/index.styles';
import { TextImageTeaserImagePosition, type TextImageTeaserProps } from '@/modules/TextImageTeaser/index.types';

import { Theme } from '@/types/theme';

import { cn } from '@/utils/cn';

import PositionForm from './PositionForm';
import PositionResult from './PositionResult';

type PositionConfiguratorProps = {
  resultMap: Record<string, TextImageTeaserProps | null>;
};

function PositionConfigurator({ resultMap }: PositionConfiguratorProps) {
  const [selectedResult, setSelectedResult] = useState<TextImageTeaserProps | null>(null);

  function handleFormSubmit({
    livingSpace,
    floors,
    position,
  }: {
    livingSpace: string;
    floors: string;
    position: string;
  }) {
    // Build the key by chaining input values
    let key = livingSpace;
    if (floors) {
      key += `-${floors}`;
    }
    // Only add position if floors includes 'threeFloorsBasement' or 'threeFloorsBasement' is selected
    if (floors && floors.includes('threeFloorsBasement')) {
      key += `-${position}`;
    }
    setSelectedResult(resultMap[key] || null);
  }

  function handleReset() {
    setSelectedResult(null);
  }

  const containerClasses = containerStyles({ theme: Theme.Sand });
  const containerImageClasses = containerImageStyles({ imagePosition: TextImageTeaserImagePosition.Right });
  const containerContentClasses = cn(
    containerContentStyles({
      imagePosition: TextImageTeaserImagePosition.Right,
    }),
    'justify-start',
  );

  return (
    <div data-tracking-id="ihc-position" className={containerClasses}>
      <div className={containerContentClasses}>
        {selectedResult === null ? (
          <PositionForm onSubmit={handleFormSubmit} />
        ) : (
          <PositionResult selectedResult={selectedResult} onReset={handleReset} />
        )}
      </div>
      <div className={containerImageClasses}>
        <Image
          src={selectedResult?.image?.src || '/router-position/default.svg'}
          alt="Router Position"
          fill
          style={{ objectFit: 'cover' }}
          sizes="(min-width: 1280px) 960px, (min-width: 1024px) 590px, 100vw"
        />
      </div>
    </div>
  );
}

export default PositionConfigurator;
