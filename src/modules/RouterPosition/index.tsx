import type { TextImageTeaserProps } from '@/modules/TextImageTeaser/index.types';

import { getTextImageTeaser } from '@/apis/contentful/getTextImageTeaser';

import ModuleWrapper from '@/components/ModuleWrapper';

import type { ModuleCommonProps } from '@/types/moduleProps';

import PositionConfigurator from './PositionConfigurator';

export const routerPositionMapping = {
  upTo70: '2WP3EN5myx1B396vOoHNBM',
  'from71To120-oneFloor': '3PKApRXxVqzkL2mFhh8BJx',
  'from71To120-twoFloors': 'ygX2IhLWKzgubTuppHR94',
  'from121To180-twoFloorsBasement': '7B8oj6TaTH4geuKSEYWrZT',
  'from121To180-threeFloors': '5651gQgOCrIKpevjNnmTKK',
  'from121To180-threeFloorsBasement-central': '1ffSxDWNHse3AWks8iPwA2',
  'from121To180-threeFloorsBasement-basement': '5SFZ6FQpJDY6vZuxPDJ8Hf',
  'above181-threeFloors': '56ZqYNy2Agx9ppMxcHN6fa',
  'above181-threeFloorsBasement-central': '6mAUjRdwzVRKTpPfuwICwx',
  'above181-threeFloorsBasement-basement': '6muNBIlXB8oOjGPVV7N4lU',
} as const;

export default async function RouterPosition({ internalName }: ModuleCommonProps) {
  const ids = Object.values(routerPositionMapping);
  const keys = Object.keys(routerPositionMapping);
  const modules = await Promise.all(ids.map((id) => getTextImageTeaser(id)));

  // Build a mapping from key to module
  const resultMap = keys.reduce(
    (acc, key, idx) => {
      acc[key] = modules[idx];
      return acc;
    },
    {} as Record<string, TextImageTeaserProps>,
  );

  return (
    <ModuleWrapper internalName={internalName} moduleName="router-position" noPadding>
      <PositionConfigurator resultMap={resultMap} />
    </ModuleWrapper>
  );
}
