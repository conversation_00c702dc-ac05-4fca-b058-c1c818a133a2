import React, { Suspense } from 'react';

import TariffInfoGridClient from '@/modules/TariffInfoGrid/TariffInfoGridClient';

import type { GridModule } from '@/apis/contentful/generated/types';
import { getTeaser } from '@/apis/contentful/getTeaser';
import { fetchContentById } from '@/apis/contentful/utils/fetchContentById';

type TariffInfoGridWrapperProps = {
  id: string;
};

export default async function TariffInfoGrid({ id }: TariffInfoGridWrapperProps) {
  const data = await fetchContentById({ id, contentType: 'TariffInfoGrid' });

  const defaultGridId = data.defaultGrid?.sys.id;
  const tariffGridIds = data.tariffGridsCollection?.items.map((item: any) => item.sys.id);

  if (!defaultGridId) return <div>No Default Tariff Grid found</div>;

  const defaultGrid = (await fetchContentById({ id: defaultGridId, contentType: 'GridModule' })) as GridModule;

  (defaultGrid.elementsCollection?.items || [])
    .filter((item) => !!item)
    .map(async (item, index) => {
      if (!item || !defaultGrid.elementsCollection?.items[index]) return null;
      defaultGrid.elementsCollection.items[index] = await getTeaser(item);
    });

  if (!tariffGridIds) return <TariffInfoGridClient defaultGrid={defaultGrid} />;

  const tariffGrids = await Promise.all(
    tariffGridIds.map(async (id: string) => {
      if (!id) return null;
      const grid = (await fetchContentById({ id, contentType: 'GridModule' })) as GridModule;
      (grid.elementsCollection?.items || [])
        .filter((item) => !!item)
        .map(async (item, index) => {
          if (!item || !grid.elementsCollection?.items[index]) return null;
          grid.elementsCollection.items[index] = await getTeaser(item);
        });
      return grid;
    }),
  );

  return (
    <Suspense fallback={<p>Lade Tarif Infos...</p>}>
      <TariffInfoGridClient defaultGrid={defaultGrid} tariffGrids={tariffGrids} />
    </Suspense>
  );
}
