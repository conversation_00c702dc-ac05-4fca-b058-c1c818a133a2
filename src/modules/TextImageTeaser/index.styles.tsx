import { cva } from 'class-variance-authority';

import { Theme } from '@/types/theme';

import { TextImageTeaserImagePosition } from './index.types';

export const containerStyles = cva('relative grid grid-cols-1 lg:grid-cols-2', {
  variants: {
    theme: {
      [Theme.Sand]: 'bg-sand text-basalt',
      [Theme.Basalt]: 'theme-basalt group bg-basalt text-sand',
      [Theme.Gray]: 'bg-gray-50 text-basalt',
    },
  },
});

export const containerContentStyles = cva(
  'flex w-full flex-col justify-center gap-2 p-4 pb-10 text-base leading-6 text-inherit lg:p-8 lg:py-12',
  {
    variants: {
      imagePosition: {
        [TextImageTeaserImagePosition.Left]: 'pl-4 lg:pr-8 lg:pl-20 xl:pr-24 2xl:pr-48',
        [TextImageTeaserImagePosition.Right]: 'pr-4 lg:pr-20 lg:pl-8 xl:pl-24 2xl:pl-48',
      },
    },
  },
);

export const containerHeadlineStyles = cva('mb-2', {
  variants: {
    theme: {
      [Theme.Sand]: 'text-basalt',
      [Theme.Basalt]: 'text-sand',
      [Theme.Gray]: 'text-basalt',
    },
  },
});

export const containerImageStyles = cva('relative aspect-3/2 overflow-hidden lg:aspect-auto lg:min-h-[640px]', {
  variants: {
    imagePosition: {
      [TextImageTeaserImagePosition.Left]: 'order-first',
      [TextImageTeaserImagePosition.Right]: 'order-first lg:order-last',
    },
  },
});

export const containerButtonStyles = cva('flex gap-8', {
  variants: {
    ctaAlignment: {
      horizontal: 'flex-col xl:flex-row',
      vertical: 'flex-col',
    },
  },
});
