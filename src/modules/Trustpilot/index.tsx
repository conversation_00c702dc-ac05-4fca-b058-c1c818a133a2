import Headline from '@/components/Headline';
import { type HeadlineProps } from '@/components/Headline';
import ModuleWrapper from '@/components/ModuleWrapper';
import TrustpilotWidget from '@/components/TrustpilotScript';

import type { ModuleCommonProps } from '@/types/moduleProps';
import { Theme } from '@/types/theme';

export type TrustpilotProps = {
  headline?: string;
  headlineTag?: HeadlineProps['intent'];
} & ModuleCommonProps;

export default function Trustpilot({ internalName, headline, headlineTag }: TrustpilotProps) {
  return (
    <ModuleWrapper internalName={internalName} moduleName={'trustpilot-module'} theme={Theme.Sand}>
      {headline && (
        <Headline intent={'h2'} type={headlineTag} className={'mb-6'}>
          {headline}
        </Headline>
      )}
      <TrustpilotWidget />
    </ModuleWrapper>
  );
}
